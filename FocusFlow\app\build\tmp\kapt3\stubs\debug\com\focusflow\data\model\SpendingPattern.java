package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\bV\b\u0087\b\u0018\u00002\u00020\u0001B\u00a1\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\u0005\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\u0011\u001a\u00020\u0012\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0012\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\u0006\u0010\u0016\u001a\u00020\n\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0018\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0018\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\n\u0012\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010 \u001a\u00020\n\u0012\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0015\u0012\b\b\u0002\u0010\"\u001a\u00020\u0015\u0012\b\b\u0002\u0010#\u001a\u00020\u0015\u00a2\u0006\u0002\u0010$J\t\u0010L\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010M\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010N\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010O\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010P\u001a\u00020\u0012H\u00c6\u0003J\u000b\u0010Q\u001a\u0004\u0018\u00010\u0012H\u00c6\u0003J\t\u0010R\u001a\u00020\u0015H\u00c6\u0003J\t\u0010S\u001a\u00020\nH\u00c6\u0003J\t\u0010T\u001a\u00020\u0018H\u00c6\u0003J\u0010\u0010U\u001a\u0004\u0018\u00010\u0018H\u00c6\u0003\u00a2\u0006\u0002\u0010JJ\u000b\u0010V\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010W\u001a\u00020\u0005H\u00c6\u0003J\u0010\u0010X\u001a\u0004\u0018\u00010\nH\u00c6\u0003\u00a2\u0006\u0002\u00103J\u000b\u0010Y\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0005H\u00c6\u0003J\t\u0010[\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\\\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010]\u001a\u00020\nH\u00c6\u0003J\u0010\u0010^\u001a\u0004\u0018\u00010\u0015H\u00c6\u0003\u00a2\u0006\u0002\u0010EJ\t\u0010_\u001a\u00020\u0015H\u00c6\u0003J\t\u0010`\u001a\u00020\u0015H\u00c6\u0003J\t\u0010a\u001a\u00020\u0005H\u00c6\u0003J\t\u0010b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010d\u001a\u00020\nH\u00c6\u0003J\t\u0010e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010f\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010g\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u00bc\u0002\u0010h\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00052\b\b\u0002\u0010\f\u001a\u00020\u00052\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0011\u001a\u00020\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00122\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\n2\b\b\u0002\u0010\u0017\u001a\u00020\u00182\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00182\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\n2\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u001d\u001a\u00020\u00052\b\b\u0002\u0010\u001e\u001a\u00020\u00052\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010 \u001a\u00020\n2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u00152\b\b\u0002\u0010\"\u001a\u00020\u00152\b\b\u0002\u0010#\u001a\u00020\u0015H\u00c6\u0001\u00a2\u0006\u0002\u0010iJ\u0013\u0010j\u001a\u00020\u00182\b\u0010k\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010l\u001a\u00020\u0015H\u00d6\u0001J\t\u0010m\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0011\u0010\u0016\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010&R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010(R\u0011\u0010\u0011\u001a\u00020\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010,R\u0013\u0010\r\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010(R\u0011\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010(R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0011\u0010\u001e\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010(R\u0015\u0010\u001b\u001a\u0004\u0018\u00010\n\u00a2\u0006\n\n\u0002\u00104\u001a\u0004\b2\u00103R\u0013\u0010\u001a\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010(R\u0011\u0010\u0017\u001a\u00020\u0018\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u00106R\u0013\u0010\u0013\u001a\u0004\u0018\u00010\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010,R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010(R\u0013\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010(R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010;R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010(R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010(R\u0011\u0010#\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010;R\u0011\u0010\"\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010;R\u0013\u0010\u001f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010(R\u0011\u0010 \u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010&R\u0011\u0010\u001d\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010(R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010(R\u0015\u0010!\u001a\u0004\u0018\u00010\u0015\u00a2\u0006\n\n\u0002\u0010F\u001a\u0004\bD\u0010ER\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u0010(R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010(R\u0015\u0010\u0019\u001a\u0004\u0018\u00010\u0018\u00a2\u0006\n\n\u0002\u0010K\u001a\u0004\bI\u0010J\u00a8\u0006n"}, d2 = {"Lcom/focusflow/data/model/SpendingPattern;", "", "id", "", "patternType", "", "patternName", "description", "triggerConditions", "averageAmount", "", "frequency", "category", "emotionalTriggers", "timePatterns", "locationTriggers", "socialTriggers", "detectedDate", "Lkotlinx/datetime/LocalDateTime;", "lastOccurrence", "occurrenceCount", "", "confidenceScore", "isActive", "", "userConfirmed", "interventionStrategy", "interventionEffectiveness", "notes", "severity", "impactOnBudget", "relatedPatterns", "seasonalFactor", "stressLevel", "preventionSuccess", "preventionAttempts", "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;IDZLjava/lang/Boolean;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;DLjava/lang/Integer;II)V", "getAverageAmount", "()D", "getCategory", "()Ljava/lang/String;", "getConfidenceScore", "getDescription", "getDetectedDate", "()Lkotlinx/datetime/LocalDateTime;", "getEmotionalTriggers", "getFrequency", "getId", "()J", "getImpactOnBudget", "getInterventionEffectiveness", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getInterventionStrategy", "()Z", "getLastOccurrence", "getLocationTriggers", "getNotes", "getOccurrenceCount", "()I", "getPatternName", "getPatternType", "getPreventionAttempts", "getPreventionSuccess", "getRelatedPatterns", "getSeasonalFactor", "getSeverity", "getSocialTriggers", "getStressLevel", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getTimePatterns", "getTriggerConditions", "getUserConfirmed", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;IDZLjava/lang/Boolean;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;DLjava/lang/Integer;II)Lcom/focusflow/data/model/SpendingPattern;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "spending_patterns")
public final class SpendingPattern {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String patternType = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String patternName = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String description = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String triggerConditions = null;
    private final double averageAmount = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String frequency = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String category = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String emotionalTriggers = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String timePatterns = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String locationTriggers = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String socialTriggers = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime detectedDate = null;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime lastOccurrence = null;
    private final int occurrenceCount = 0;
    private final double confidenceScore = 0.0;
    private final boolean isActive = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean userConfirmed = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String interventionStrategy = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double interventionEffectiveness = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String notes = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String severity = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String impactOnBudget = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String relatedPatterns = null;
    private final double seasonalFactor = 0.0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer stressLevel = null;
    private final int preventionSuccess = 0;
    private final int preventionAttempts = 0;
    
    public SpendingPattern(long id, @org.jetbrains.annotations.NotNull
    java.lang.String patternType, @org.jetbrains.annotations.NotNull
    java.lang.String patternName, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    java.lang.String triggerConditions, double averageAmount, @org.jetbrains.annotations.NotNull
    java.lang.String frequency, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.String emotionalTriggers, @org.jetbrains.annotations.Nullable
    java.lang.String timePatterns, @org.jetbrains.annotations.Nullable
    java.lang.String locationTriggers, @org.jetbrains.annotations.Nullable
    java.lang.String socialTriggers, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime detectedDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastOccurrence, int occurrenceCount, double confidenceScore, boolean isActive, @org.jetbrains.annotations.Nullable
    java.lang.Boolean userConfirmed, @org.jetbrains.annotations.Nullable
    java.lang.String interventionStrategy, @org.jetbrains.annotations.Nullable
    java.lang.Double interventionEffectiveness, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    java.lang.String severity, @org.jetbrains.annotations.NotNull
    java.lang.String impactOnBudget, @org.jetbrains.annotations.Nullable
    java.lang.String relatedPatterns, double seasonalFactor, @org.jetbrains.annotations.Nullable
    java.lang.Integer stressLevel, int preventionSuccess, int preventionAttempts) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPatternType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPatternName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTriggerConditions() {
        return null;
    }
    
    public final double getAverageAmount() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFrequency() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getEmotionalTriggers() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getTimePatterns() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getLocationTriggers() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSocialTriggers() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getDetectedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getLastOccurrence() {
        return null;
    }
    
    public final int getOccurrenceCount() {
        return 0;
    }
    
    public final double getConfidenceScore() {
        return 0.0;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean getUserConfirmed() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getInterventionStrategy() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getInterventionEffectiveness() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNotes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSeverity() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getImpactOnBudget() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getRelatedPatterns() {
        return null;
    }
    
    public final double getSeasonalFactor() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getStressLevel() {
        return null;
    }
    
    public final int getPreventionSuccess() {
        return 0;
    }
    
    public final int getPreventionAttempts() {
        return 0;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component14() {
        return null;
    }
    
    public final int component15() {
        return 0;
    }
    
    public final double component16() {
        return 0.0;
    }
    
    public final boolean component17() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component22() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component23() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component24() {
        return null;
    }
    
    public final double component25() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component26() {
        return null;
    }
    
    public final int component27() {
        return 0;
    }
    
    public final int component28() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.SpendingPattern copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String patternType, @org.jetbrains.annotations.NotNull
    java.lang.String patternName, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    java.lang.String triggerConditions, double averageAmount, @org.jetbrains.annotations.NotNull
    java.lang.String frequency, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.Nullable
    java.lang.String emotionalTriggers, @org.jetbrains.annotations.Nullable
    java.lang.String timePatterns, @org.jetbrains.annotations.Nullable
    java.lang.String locationTriggers, @org.jetbrains.annotations.Nullable
    java.lang.String socialTriggers, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime detectedDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastOccurrence, int occurrenceCount, double confidenceScore, boolean isActive, @org.jetbrains.annotations.Nullable
    java.lang.Boolean userConfirmed, @org.jetbrains.annotations.Nullable
    java.lang.String interventionStrategy, @org.jetbrains.annotations.Nullable
    java.lang.Double interventionEffectiveness, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    java.lang.String severity, @org.jetbrains.annotations.NotNull
    java.lang.String impactOnBudget, @org.jetbrains.annotations.Nullable
    java.lang.String relatedPatterns, double seasonalFactor, @org.jetbrains.annotations.Nullable
    java.lang.Integer stressLevel, int preventionSuccess, int preventionAttempts) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}