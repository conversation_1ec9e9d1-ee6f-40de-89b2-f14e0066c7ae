package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.WishlistItem
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface WishlistItemDao {
    
    @Query("SELECT * FROM wishlist_items WHERE isPurchased = 0 ORDER BY addedDate DESC")
    fun getAllActiveWishlistItems(): Flow<List<WishlistItem>>
    
    @Query("SELECT * FROM wishlist_items WHERE isPurchased = 1 ORDER BY purchasedDate DESC")
    fun getPurchasedWishlistItems(): Flow<List<WishlistItem>>
    
    @Query("SELECT * FROM wishlist_items WHERE isDelayActive = 1 AND delayEndTime <= :currentTime")
    suspend fun getItemsWithExpiredDelay(currentTime: LocalDateTime): List<WishlistItem>
    
    @Query("SELECT * FROM wishlist_items WHERE isDelayActive = 1 ORDER BY delayEndTime ASC")
    fun getActiveDelayItems(): Flow<List<WishlistItem>>
    
    @Query("SELECT * FROM wishlist_items WHERE category = :category AND isPurchased = 0")
    fun getWishlistItemsByCategory(category: String): Flow<List<WishlistItem>>
    
    @Query("SELECT * FROM wishlist_items WHERE priority = :priority AND isPurchased = 0")
    fun getWishlistItemsByPriority(priority: String): Flow<List<WishlistItem>>
    
    @Query("SELECT * FROM wishlist_items WHERE estimatedPrice BETWEEN :minPrice AND :maxPrice AND isPurchased = 0")
    fun getWishlistItemsByPriceRange(minPrice: Double, maxPrice: Double): Flow<List<WishlistItem>>
    
    @Query("SELECT * FROM wishlist_items WHERE id = :id")
    suspend fun getWishlistItemById(id: Long): WishlistItem?
    
    @Query("SELECT COUNT(*) FROM wishlist_items WHERE isDelayActive = 1")
    suspend fun getActiveDelayCount(): Int
    
    @Query("SELECT AVG(estimatedPrice) FROM wishlist_items WHERE isPurchased = 0")
    suspend fun getAverageWishlistPrice(): Double?
    
    @Query("SELECT SUM(estimatedPrice) FROM wishlist_items WHERE isPurchased = 0")
    suspend fun getTotalWishlistValue(): Double?
    
    @Insert
    suspend fun insertWishlistItem(wishlistItem: WishlistItem): Long
    
    @Update
    suspend fun updateWishlistItem(wishlistItem: WishlistItem)
    
    @Delete
    suspend fun deleteWishlistItem(wishlistItem: WishlistItem)
    
    @Query("UPDATE wishlist_items SET isPurchased = 1, purchasedDate = :purchaseDate, actualPrice = :actualPrice WHERE id = :id")
    suspend fun markAsPurchased(id: Long, purchaseDate: LocalDateTime, actualPrice: Double)
    
    @Query("UPDATE wishlist_items SET isDelayActive = 0 WHERE id = :id")
    suspend fun removeDelay(id: Long)
    
    @Query("UPDATE wishlist_items SET stillWanted = :stillWanted, reflectionNotes = :notes WHERE id = :id")
    suspend fun updateReflection(id: Long, stillWanted: Boolean, notes: String?)
    
    @Query("DELETE FROM wishlist_items WHERE isPurchased = 1 AND purchasedDate < :cutoffDate")
    suspend fun deletePurchasedItemsOlderThan(cutoffDate: LocalDateTime)
}
