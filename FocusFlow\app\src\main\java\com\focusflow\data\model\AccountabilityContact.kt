package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "accountability_contacts")
data class AccountabilityContact(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val relationship: String, // "partner", "friend", "family", "mentor", "coach"
    val contactMethod: String, // "email", "sms", "app_notification", "phone"
    val contactInfo: String, // Email, phone number, or app user ID
    val isActive: Boolean = true,
    val permissionLevel: String, // "basic", "detailed", "full"
    val sharingPreferences: String, // JSON object with sharing settings
    val addedDate: LocalDateTime,
    val lastContactDate: LocalDateTime? = null,
    val totalInteractions: Int = 0,
    val successfulInterventions: Int = 0,
    val trustLevel: Int = 5, // 1-5 scale
    val responseRate: Double = 0.0, // Percentage of times they respond
    val averageResponseTime: Int? = null, // Minutes
    val preferredContactTime: String? = null, // JSON object with time preferences
    val timeZone: String? = null,
    val notificationFrequency: String = "as_needed", // "daily", "weekly", "as_needed", "emergency_only"
    val supportType: String, // "encouragement", "accountability", "advice", "emergency"
    val specializations: String? = null, // JSON array of areas they help with
    val notes: String? = null,
    val emergencyContact: Boolean = false,
    val canReceiveSpendingAlerts: Boolean = false,
    val canReceiveBudgetUpdates: Boolean = false,
    val canReceiveGoalProgress: Boolean = false,
    val canReceiveEmergencyAlerts: Boolean = false,
    val mutualAccountability: Boolean = false, // If they also use the app
    val partnerUserId: String? = null, // If they're also an app user
    val consentGiven: Boolean = false,
    val consentDate: LocalDateTime? = null,
    val lastConsentUpdate: LocalDateTime? = null,
    val privacyLevel: String = "standard" // "minimal", "standard", "detailed"
)
