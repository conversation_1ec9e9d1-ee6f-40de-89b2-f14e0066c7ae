package com.focusflow.ui.viewmodel;

import com.focusflow.data.repository.NotificationRepository;
import com.focusflow.data.repository.UserPreferencesRepository;
import com.focusflow.service.GamificationService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class MainViewModel_Factory implements Factory<MainViewModel> {
  private final Provider<UserPreferencesRepository> userPreferencesRepositoryProvider;

  private final Provider<GamificationService> gamificationServiceProvider;

  private final Provider<NotificationRepository> notificationRepositoryProvider;

  public MainViewModel_Factory(
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    this.userPreferencesRepositoryProvider = userPreferencesRepositoryProvider;
    this.gamificationServiceProvider = gamificationServiceProvider;
    this.notificationRepositoryProvider = notificationRepositoryProvider;
  }

  @Override
  public MainViewModel get() {
    return newInstance(userPreferencesRepositoryProvider.get(), gamificationServiceProvider.get(), notificationRepositoryProvider.get());
  }

  public static MainViewModel_Factory create(
      Provider<UserPreferencesRepository> userPreferencesRepositoryProvider,
      Provider<GamificationService> gamificationServiceProvider,
      Provider<NotificationRepository> notificationRepositoryProvider) {
    return new MainViewModel_Factory(userPreferencesRepositoryProvider, gamificationServiceProvider, notificationRepositoryProvider);
  }

  public static MainViewModel newInstance(UserPreferencesRepository userPreferencesRepository,
      GamificationService gamificationService, NotificationRepository notificationRepository) {
    return new MainViewModel(userPreferencesRepository, gamificationService, notificationRepository);
  }
}
