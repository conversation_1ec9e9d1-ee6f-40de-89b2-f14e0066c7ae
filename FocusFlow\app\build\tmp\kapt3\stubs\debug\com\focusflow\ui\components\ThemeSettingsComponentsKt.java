package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a.\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00042\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a$\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u00042\u0012\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a\b\u0010\f\u001a\u00020\u0001H\u0007\u001a&\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u00112\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00010\u0013H\u0007\u001a$\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\u0006H\u0007\u001a.\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u00a8\u0006\u001a"}, d2 = {"AccessibilityBadge", "", "FontSizeSettingsCard", "currentFontScale", "", "onFontScaleChanged", "Lkotlin/Function1;", "modifier", "Landroidx/compose/ui/Modifier;", "FontSizeSlider", "currentScale", "onScaleChanged", "RecommendedBadge", "ThemeOptionItem", "option", "Lcom/focusflow/ui/components/ThemeOption;", "isSelected", "", "onSelected", "Lkotlin/Function0;", "ThemeOptionsList", "currentTheme", "Lcom/focusflow/ui/theme/ThemeMode;", "onThemeSelected", "ThemeSettingsCard", "onThemeChanged", "app_debug"})
public final class ThemeSettingsComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void ThemeSettingsCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.theme.ThemeMode currentTheme, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.theme.ThemeMode, kotlin.Unit> onThemeChanged, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ThemeOptionsList(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.theme.ThemeMode currentTheme, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.theme.ThemeMode, kotlin.Unit> onThemeSelected) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ThemeOptionItem(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.components.ThemeOption option, boolean isSelected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSelected) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void RecommendedBadge() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void AccessibilityBadge() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void FontSizeSettingsCard(float currentFontScale, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onFontScaleChanged, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void FontSizeSlider(float currentScale, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Float, kotlin.Unit> onScaleChanged) {
    }
}