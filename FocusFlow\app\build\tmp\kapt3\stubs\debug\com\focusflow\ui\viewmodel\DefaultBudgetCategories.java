package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0006\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R#\u0010\u0003\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00070\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\t\u00a8\u0006\n"}, d2 = {"Lcom/focusflow/ui/viewmodel/DefaultBudgetCategories;", "", "()V", "categories", "", "Lkotlin/Pair;", "", "", "getCategories", "()Ljava/util/List;", "app_debug"})
public final class DefaultBudgetCategories {
    @org.jetbrains.annotations.NotNull
    private static final java.util.List<kotlin.Pair<java.lang.String, java.lang.Double>> categories = null;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.ui.viewmodel.DefaultBudgetCategories INSTANCE = null;
    
    private DefaultBudgetCategories() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<kotlin.Pair<java.lang.String, java.lang.Double>> getCategories() {
        return null;
    }
}