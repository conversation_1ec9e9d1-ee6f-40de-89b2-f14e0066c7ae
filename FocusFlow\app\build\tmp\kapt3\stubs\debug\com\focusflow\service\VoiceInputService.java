package com.focusflow.service;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000^\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\b\n\b\u0007\u0018\u00002\u00020\u0001:\u0001-B\u0019\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0006\u0010\u001f\u001a\u00020 J\u0018\u0010!\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020$H\u0002J\u0018\u0010%\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020$H\u0002J\u0018\u0010&\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020$H\u0002J\u0018\u0010\'\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020$H\u0002J\u0018\u0010(\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020$H\u0002J\u0018\u0010)\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020$H\u0002J\u0018\u0010*\u001a\u00020 2\u0006\u0010\"\u001a\u00020\t2\u0006\u0010#\u001a\u00020$H\u0002J\u0006\u0010+\u001a\u00020 J\u0006\u0010,\u001a\u00020 R\u0016\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001c\u0010\n\u001a\u0010\u0012\f\u0012\n \f*\u0004\u0018\u00010\u000b0\u000b0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\t0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u000b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u000e\u0010\u0017\u001a\u00020\u0018X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\t0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0015R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0015\u00a8\u0006."}, d2 = {"Lcom/focusflow/service/VoiceInputService;", "", "context", "Landroid/content/Context;", "voiceCommandDao", "Lcom/focusflow/data/dao/VoiceCommandDao;", "(Landroid/content/Context;Lcom/focusflow/data/dao/VoiceCommandDao;)V", "_error", "Landroidx/lifecycle/MutableLiveData;", "", "_isListening", "", "kotlin.jvm.PlatformType", "_recognizedText", "_voiceCommandResult", "Lcom/focusflow/service/VoiceInputService$VoiceCommandResult;", "coroutineScope", "Lkotlinx/coroutines/CoroutineScope;", "error", "Landroidx/lifecycle/LiveData;", "getError", "()Landroidx/lifecycle/LiveData;", "isListening", "recognitionListener", "Landroid/speech/RecognitionListener;", "recognizedText", "getRecognizedText", "speechRecognizer", "Landroid/speech/SpeechRecognizer;", "voiceCommandResult", "getVoiceCommandResult", "destroy", "", "parseBudgetCheckCommand", "text", "confidence", "", "parseExpenseCommand", "parseFocusTimerCommand", "parseNavigationCommand", "parseTaskCommand", "parseVoiceCommand", "processVoiceCommand", "startListening", "stopListening", "VoiceCommandResult", "app_debug"})
public final class VoiceInputService {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.VoiceCommandDao voiceCommandDao = null;
    @org.jetbrains.annotations.Nullable
    private android.speech.SpeechRecognizer speechRecognizer;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.CoroutineScope coroutineScope = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.Boolean> _isListening = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.Boolean> isListening = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _recognizedText = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.String> recognizedText = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<com.focusflow.service.VoiceInputService.VoiceCommandResult> _voiceCommandResult = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<com.focusflow.service.VoiceInputService.VoiceCommandResult> voiceCommandResult = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.MutableLiveData<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.lifecycle.LiveData<java.lang.String> error = null;
    @org.jetbrains.annotations.NotNull
    private final android.speech.RecognitionListener recognitionListener = null;
    
    @javax.inject.Inject
    public VoiceInputService(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.VoiceCommandDao voiceCommandDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.Boolean> isListening() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.String> getRecognizedText() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<com.focusflow.service.VoiceInputService.VoiceCommandResult> getVoiceCommandResult() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.lifecycle.LiveData<java.lang.String> getError() {
        return null;
    }
    
    public final void startListening() {
    }
    
    public final void stopListening() {
    }
    
    private final void processVoiceCommand(java.lang.String text, double confidence) {
    }
    
    private final com.focusflow.service.VoiceInputService.VoiceCommandResult parseVoiceCommand(java.lang.String text, double confidence) {
        return null;
    }
    
    private final com.focusflow.service.VoiceInputService.VoiceCommandResult parseExpenseCommand(java.lang.String text, double confidence) {
        return null;
    }
    
    private final com.focusflow.service.VoiceInputService.VoiceCommandResult parseBudgetCheckCommand(java.lang.String text, double confidence) {
        return null;
    }
    
    private final com.focusflow.service.VoiceInputService.VoiceCommandResult parseNavigationCommand(java.lang.String text, double confidence) {
        return null;
    }
    
    private final com.focusflow.service.VoiceInputService.VoiceCommandResult parseFocusTimerCommand(java.lang.String text, double confidence) {
        return null;
    }
    
    private final com.focusflow.service.VoiceInputService.VoiceCommandResult parseTaskCommand(java.lang.String text, double confidence) {
        return null;
    }
    
    public final void destroy() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010$\n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u001b\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B[\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0012\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\f\u001a\u00020\n\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001a\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0003H\u00c6\u0003J\u0015\u0010\u001c\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0006H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\bH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\nH\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010 \u001a\u00020\nH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003Ji\u0010\"\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u0014\b\u0002\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\f\u001a\u00020\n2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010#\u001a\u00020\n2\b\u0010$\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010%\u001a\u00020&H\u00d6\u0001J\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0013\u0010\r\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u0011\u0010\f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0010R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0016R\u001d\u0010\u0005\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0019\u00a8\u0006("}, d2 = {"Lcom/focusflow/service/VoiceInputService$VoiceCommandResult;", "", "command", "", "intent", "parameters", "", "confidence", "", "isSuccessful", "", "actionTaken", "followUpRequired", "followUpPrompt", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;DZLjava/lang/String;ZLjava/lang/String;)V", "getActionTaken", "()Ljava/lang/String;", "getCommand", "getConfidence", "()D", "getFollowUpPrompt", "getFollowUpRequired", "()Z", "getIntent", "getParameters", "()Ljava/util/Map;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
    public static final class VoiceCommandResult {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String command = null;
        @org.jetbrains.annotations.NotNull
        private final java.lang.String intent = null;
        @org.jetbrains.annotations.NotNull
        private final java.util.Map<java.lang.String, java.lang.Object> parameters = null;
        private final double confidence = 0.0;
        private final boolean isSuccessful = false;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String actionTaken = null;
        private final boolean followUpRequired = false;
        @org.jetbrains.annotations.Nullable
        private final java.lang.String followUpPrompt = null;
        
        public VoiceCommandResult(@org.jetbrains.annotations.NotNull
        java.lang.String command, @org.jetbrains.annotations.NotNull
        java.lang.String intent, @org.jetbrains.annotations.NotNull
        java.util.Map<java.lang.String, ? extends java.lang.Object> parameters, double confidence, boolean isSuccessful, @org.jetbrains.annotations.Nullable
        java.lang.String actionTaken, boolean followUpRequired, @org.jetbrains.annotations.Nullable
        java.lang.String followUpPrompt) {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getCommand() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getIntent() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.Map<java.lang.String, java.lang.Object> getParameters() {
            return null;
        }
        
        public final double getConfidence() {
            return 0.0;
        }
        
        public final boolean isSuccessful() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getActionTaken() {
            return null;
        }
        
        public final boolean getFollowUpRequired() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String getFollowUpPrompt() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.util.Map<java.lang.String, java.lang.Object> component3() {
            return null;
        }
        
        public final double component4() {
            return 0.0;
        }
        
        public final boolean component5() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component6() {
            return null;
        }
        
        public final boolean component7() {
            return false;
        }
        
        @org.jetbrains.annotations.Nullable
        public final java.lang.String component8() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.focusflow.service.VoiceInputService.VoiceCommandResult copy(@org.jetbrains.annotations.NotNull
        java.lang.String command, @org.jetbrains.annotations.NotNull
        java.lang.String intent, @org.jetbrains.annotations.NotNull
        java.util.Map<java.lang.String, ? extends java.lang.Object> parameters, double confidence, boolean isSuccessful, @org.jetbrains.annotations.Nullable
        java.lang.String actionTaken, boolean followUpRequired, @org.jetbrains.annotations.Nullable
        java.lang.String followUpPrompt) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}