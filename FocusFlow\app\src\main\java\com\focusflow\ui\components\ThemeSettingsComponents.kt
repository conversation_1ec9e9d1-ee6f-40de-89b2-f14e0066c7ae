package com.focusflow.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.focusflow.ui.theme.ThemeMode
import com.focusflow.utils.AccessibilityUtils

@Composable
fun ThemeSettingsCard(
    currentTheme: ThemeMode,
    onThemeChanged: (ThemeMode) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .semantics { 
                contentDescription = AccessibilityUtils.getButtonDescription("Theme settings", "appearance customization")
            },
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "Theme & Appearance",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Choose a theme that works best for your ADHD needs",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            ThemeOptionsList(
                currentTheme = currentTheme,
                onThemeSelected = onThemeChanged
            )
        }
    }
}

@Composable
fun ThemeOptionsList(
    currentTheme: ThemeMode,
    onThemeSelected: (ThemeMode) -> Unit
) {
    val themeOptions = listOf(
        ThemeOption(
            mode = ThemeMode.SYSTEM,
            title = "System Default",
            description = "Follow your device's theme setting",
            icon = Icons.Default.Phone,
            isRecommended = true
        ),
        ThemeOption(
            mode = ThemeMode.LIGHT,
            title = "Light Mode",
            description = "Bright theme for daytime use",
            icon = Icons.Default.Star,
            isRecommended = false
        ),
        ThemeOption(
            mode = ThemeMode.DARK,
            title = "Dark Mode",
            description = "Reduced eye strain for evening use",
            icon = Icons.Default.Favorite,
            isRecommended = false
        ),
        ThemeOption(
            mode = ThemeMode.HIGH_CONTRAST_LIGHT,
            title = "High Contrast Light",
            description = "Enhanced visibility with high contrast",
            icon = Icons.Default.Settings,
            isRecommended = false,
            isAccessibility = true
        ),
        ThemeOption(
            mode = ThemeMode.HIGH_CONTRAST_DARK,
            title = "High Contrast Dark",
            description = "Dark theme with enhanced contrast",
            icon = Icons.Default.Build,
            isRecommended = false,
            isAccessibility = true
        )
    )
    
    Column {
        themeOptions.forEach { option ->
            ThemeOptionItem(
                option = option,
                isSelected = currentTheme == option.mode,
                onSelected = { onThemeSelected(option.mode) }
            )
            if (option != themeOptions.last()) {
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
fun ThemeOptionItem(
    option: ThemeOption,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    val borderColor = if (isSelected) {
        MaterialTheme.colors.primary
    } else {
        MaterialTheme.colors.onSurface.copy(alpha = 0.12f)
    }
    
    val backgroundColor = if (isSelected) {
        MaterialTheme.colors.primary.copy(alpha = 0.08f)
    } else {
        MaterialTheme.colors.surface
    }
    
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .border(
                width = if (isSelected) 2.dp else 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onSelected() }
            .semantics { 
                contentDescription = AccessibilityUtils.getButtonDescription(
                    "Select ${option.title} theme", 
                    option.description
                )
            }
            .padding(16.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            Icon(
                imageVector = option.icon,
                contentDescription = null,
                tint = if (isSelected) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface,
                modifier = Modifier.size(24.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = option.title,
                        style = MaterialTheme.typography.subtitle1,
                        fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                        color = if (isSelected) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface
                    )
                    
                    if (option.isRecommended) {
                        Spacer(modifier = Modifier.width(8.dp))
                        RecommendedBadge()
                    }
                    
                    if (option.isAccessibility) {
                        Spacer(modifier = Modifier.width(8.dp))
                        AccessibilityBadge()
                    }
                }
                
                Spacer(modifier = Modifier.height(4.dp))
                
                Text(
                    text = option.description,
                    style = MaterialTheme.typography.body2,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
            
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Selected",
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
fun RecommendedBadge() {
    Surface(
        color = MaterialTheme.colors.secondary,
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier.semantics { 
            contentDescription = "Recommended option"
        }
    ) {
        Text(
            text = "Recommended",
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSecondary,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
        )
    }
}

@Composable
fun AccessibilityBadge() {
    Surface(
        color = MaterialTheme.colors.primary.copy(alpha = 0.1f),
        shape = RoundedCornerShape(12.dp),
        modifier = Modifier.semantics { 
            contentDescription = "Accessibility option"
        }
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
        ) {
            Icon(
                imageVector = Icons.Default.Person,
                contentDescription = null,
                tint = MaterialTheme.colors.primary,
                modifier = Modifier.size(12.dp)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = "Accessibility",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.primary
            )
        }
    }
}

@Composable
fun FontSizeSettingsCard(
    currentFontScale: Float,
    onFontScaleChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .semantics { 
                contentDescription = AccessibilityUtils.getButtonDescription("Font size settings", "text readability")
            },
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.Settings,
                    contentDescription = null,
                    tint = MaterialTheme.colors.primary,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = "Font Size",
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "Adjust text size for better readability",
                style = MaterialTheme.typography.body2,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            FontSizeSlider(
                currentScale = currentFontScale,
                onScaleChanged = onFontScaleChanged
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Preview text
            Text(
                text = "Preview: This is how your text will look",
                style = MaterialTheme.typography.body1.copy(
                    fontSize = MaterialTheme.typography.body1.fontSize * currentFontScale
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        MaterialTheme.colors.onSurface.copy(alpha = 0.05f),
                        RoundedCornerShape(8.dp)
                    )
                    .padding(12.dp)
            )
        }
    }
}

@Composable
fun FontSizeSlider(
    currentScale: Float,
    onScaleChanged: (Float) -> Unit
) {
    val fontSizeOptions = listOf(
        0.8f to "Small",
        1.0f to "Normal",
        1.2f to "Large",
        1.4f to "Extra Large"
    )
    
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            fontSizeOptions.forEach { (scale, label) ->
                Text(
                    text = label,
                    style = MaterialTheme.typography.caption,
                    color = if (currentScale == scale) {
                        MaterialTheme.colors.primary
                    } else {
                        MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Slider(
            value = currentScale,
            onValueChange = onScaleChanged,
            valueRange = 0.8f..1.4f,
            steps = 2,
            modifier = Modifier
                .fillMaxWidth()
                .semantics { 
                    contentDescription = AccessibilityUtils.getFormFieldDescription(
                        "Font size slider", 
                        false, 
                        "Current scale: ${String.format("%.1f", currentScale)}"
                    )
                }
        )
    }
}

data class ThemeOption(
    val mode: ThemeMode,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val isRecommended: Boolean = false,
    val isAccessibility: Boolean = false
)
