package com.focusflow.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0006\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u001a\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0010\t\n\u0000\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u00042\u0006\u0010\u0007\u001a\u00020\bJ\u0018\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u00042\b\b\u0002\u0010\u000b\u001a\u00020\u0004J \u0010\f\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\u00042\u0006\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u0004J\u0018\u0010\u0011\u001a\u00020\u00042\u0006\u0010\u0012\u001a\u00020\u0013\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0014\u0010\u0015J\u0016\u0010\u0016\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u0010\u000b\u001a\u00020\u0004J\u001e\u0010\u0019\u001a\u00020\u00042\u0006\u0010\u001a\u001a\u00020\u00042\u0006\u0010\u001b\u001a\u00020\u00182\u0006\u0010\u001c\u001a\u00020\u0018J\u000e\u0010\u001d\u001a\u00020\u00042\u0006\u0010\u001e\u001a\u00020\u0004J\u001e\u0010\u001f\u001a\u00020\u00042\u0006\u0010 \u001a\u00020\u000f2\u0006\u0010!\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\bJ \u0010#\u001a\u00020\u00042\u0006\u0010$\u001a\u00020\u00042\u0006\u0010%\u001a\u00020\b2\b\b\u0002\u0010&\u001a\u00020\u0004J\u000e\u0010\'\u001a\u00020\u00042\u0006\u0010(\u001a\u00020\u0004J\u0016\u0010)\u001a\u00020\u00042\u0006\u0010*\u001a\u00020\u00182\u0006\u0010+\u001a\u00020\u0018J\u000e\u0010,\u001a\u00020\u00042\u0006\u0010-\u001a\u00020\u0004J\u001e\u0010.\u001a\u00020\u00042\u0006\u0010/\u001a\u00020\u00042\u0006\u0010\u0017\u001a\u00020\u00182\u0006\u00100\u001a\u00020\u0018J\u0014\u00101\u001a\u00020\u00042\f\u00102\u001a\b\u0012\u0004\u0012\u00020\u000403J\u000e\u00104\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u0004J\u000e\u00105\u001a\u00020\u00042\u0006\u00106\u001a\u000207\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00068"}, d2 = {"Lcom/focusflow/utils/AccessibilityUtils;", "", "()V", "getAchievementDescription", "", "title", "description", "isUnlocked", "", "getButtonDescription", "action", "context", "getChartDescription", "chartType", "dataPoints", "", "trend", "getColorDescription", "color", "Landroidx/compose/ui/graphics/Color;", "getColorDescription-8_81llA", "(J)Ljava/lang/String;", "getContentDescription", "amount", "", "getDebtDescription", "cardName", "balance", "limit", "getErrorDescription", "error", "getFocusTimerDescription", "minutes", "seconds", "isRunning", "getFormFieldDescription", "fieldName", "isRequired", "currentValue", "getNavigationDescription", "destination", "getProgressDescription", "current", "total", "getSimplifiedDescription", "complexText", "getSpendingCategoryDescription", "category", "budget", "getStepByStepDescription", "steps", "", "getSuccessDescription", "getTimeBasedDescription", "timestamp", "", "app_debug"})
public final class AccessibilityUtils {
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.utils.AccessibilityUtils INSTANCE = null;
    
    private AccessibilityUtils() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getContentDescription(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getProgressDescription(double current, double total) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSpendingCategoryDescription(@org.jetbrains.annotations.NotNull
    java.lang.String category, double amount, double budget) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDebtDescription(@org.jetbrains.annotations.NotNull
    java.lang.String cardName, double balance, double limit) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFocusTimerDescription(int minutes, int seconds, boolean isRunning) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getAchievementDescription(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, boolean isUnlocked) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getButtonDescription(@org.jetbrains.annotations.NotNull
    java.lang.String action, @org.jetbrains.annotations.NotNull
    java.lang.String context) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getNavigationDescription(@org.jetbrains.annotations.NotNull
    java.lang.String destination) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getChartDescription(@org.jetbrains.annotations.NotNull
    java.lang.String chartType, int dataPoints, @org.jetbrains.annotations.NotNull
    java.lang.String trend) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFormFieldDescription(@org.jetbrains.annotations.NotNull
    java.lang.String fieldName, boolean isRequired, @org.jetbrains.annotations.NotNull
    java.lang.String currentValue) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getErrorDescription(@org.jetbrains.annotations.NotNull
    java.lang.String error) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSuccessDescription(@org.jetbrains.annotations.NotNull
    java.lang.String action) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSimplifiedDescription(@org.jetbrains.annotations.NotNull
    java.lang.String complexText) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getStepByStepDescription(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> steps) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTimeBasedDescription(long timestamp) {
        return null;
    }
}