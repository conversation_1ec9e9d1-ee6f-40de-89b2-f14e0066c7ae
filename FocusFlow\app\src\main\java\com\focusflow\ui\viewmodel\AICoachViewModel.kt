package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.repository.ExpenseRepository
import com.focusflow.data.repository.CreditCardRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay
import javax.inject.Inject

@HiltViewModel
class AICoachViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository,
    private val expenseRepository: ExpenseRepository,
    private val creditCardRepository: CreditCardRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(AICoachUiState())
    val uiState: StateFlow<AICoachUiState> = _uiState.asStateFlow()

    fun updateCurrentMessage(message: String) {
        _uiState.value = _uiState.value.copy(currentMessage = message)
    }

    fun sendMessage(message: String) {
        if (message.isBlank()) return
        
        viewModelScope.launch {
            try {
                // Add user message
                val userMessage = ChatMessage(
                    content = message,
                    isFromUser = true,
                    timestamp = System.currentTimeMillis()
                )
                
                _uiState.value = _uiState.value.copy(
                    conversations = _uiState.value.conversations + userMessage,
                    currentMessage = "",
                    isLoading = true,
                    error = null
                )
                
                // Simulate AI processing delay
                delay(1500)
                
                // Generate AI response based on message content
                val aiResponse = generateAIResponse(message)
                
                val aiMessage = ChatMessage(
                    content = aiResponse,
                    isFromUser = false,
                    timestamp = System.currentTimeMillis()
                )
                
                _uiState.value = _uiState.value.copy(
                    conversations = _uiState.value.conversations + aiMessage,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to send message: ${e.message}"
                )
            }
        }
    }
    
    private suspend fun generateAIResponse(userMessage: String): String {
        // This is a simplified AI response generator
        // In a real implementation, this would call an actual AI service
        
        val lowerMessage = userMessage.lowercase()
        
        return when {
            lowerMessage.contains("spending") || lowerMessage.contains("💰") -> {
                generateSpendingAnalysis()
            }
            lowerMessage.contains("budget") || lowerMessage.contains("📊") -> {
                generateBudgetAdvice()
            }
            lowerMessage.contains("debt") || lowerMessage.contains("💳") -> {
                generateDebtAdvice()
            }
            lowerMessage.contains("task") || lowerMessage.contains("🎯") -> {
                generateTaskBreakdown(userMessage)
            }
            lowerMessage.contains("progress") || lowerMessage.contains("📈") -> {
                generateProgressReport()
            }
            lowerMessage.contains("tip") || lowerMessage.contains("💡") -> {
                generateMoneyTip()
            }
            else -> {
                generateGeneralResponse(userMessage)
            }
        }
    }
    
    private suspend fun generateSpendingAnalysis(): String {
        // Get actual spending data
        val preferences = userPreferencesRepository.getUserPreferencesSync()
        val weeklyBudget = preferences?.weeklyBudget ?: 300.0
        
        return """
            📊 **Your Spending Analysis**
            
            Based on your recent activity:
            • Weekly budget: $${String.format("%.2f", weeklyBudget)}
            • You're doing great at tracking expenses! 
            • Consider setting up categories for better insights
            
            💡 **ADHD-Friendly Tip**: Try the "24-hour rule" - wait a day before making non-essential purchases over $25.
        """.trimIndent()
    }
    
    private fun generateBudgetAdvice(): String {
        return """
            🎯 **Budget Setup Made Simple**
            
            For ADHD-friendly budgeting:
            1. **Start small** - Weekly budgets are easier than monthly
            2. **Use the 50/30/20 rule** - 50% needs, 30% wants, 20% savings
            3. **Automate everything** - Set up automatic transfers
            4. **Visual tracking** - Use our color-coded spending widget
            
            Would you like me to help you set up specific budget categories?
        """.trimIndent()
    }
    
    private suspend fun generateDebtAdvice(): String {
        return """
            💪 **Debt Management Strategy**
            
            Here's your ADHD-friendly debt plan:
            1. **List all debts** - Start with the smallest balance (snowball method)
            2. **Automate minimums** - Never miss a payment
            3. **Extra payments** - Put any extra money toward the smallest debt
            4. **Celebrate wins** - Each paid-off debt is a victory! 🎉
            
            Remember: Progress over perfection. Every payment counts!
        """.trimIndent()
    }
    
    private fun generateTaskBreakdown(task: String): String {
        return """
            🔧 **Task Breakdown**
            
            Let's make this manageable! Here's how to break down big tasks:
            
            1. **Write it down** - Get it out of your head
            2. **Break into 15-minute chunks** - Perfect for ADHD attention spans
            3. **Start with the easiest part** - Build momentum
            4. **Set a timer** - Use the Pomodoro technique
            5. **Reward yourself** - After each completed chunk
            
            What specific task would you like me to help break down?
        """.trimIndent()
    }
    
    private fun generateProgressReport(): String {
        return """
            📈 **Your Progress Report**
            
            You're making great strides! Here's what I see:
            • ✅ Onboarding completed - Welcome to FocusFlow!
            • 📱 App setup complete
            • 🎯 Ready to start tracking
            
            **Next steps:**
            1. Log your first expense
            2. Set up your first budget category
            3. Explore the debt management tools
            
            Remember: Every small step is progress! 🌟
        """.trimIndent()
    }
    
    private fun generateMoneyTip(): String {
        val tips = listOf(
            "💡 **The 24-Hour Rule**: Wait a day before buying anything over $25. You'll be surprised how often you change your mind!",
            "🏦 **Automate Savings**: Set up automatic transfers to savings right after payday. You can't spend what you don't see!",
            "📱 **Use Cash for Discretionary Spending**: Physical money makes spending feel more real than cards.",
            "🛒 **Shop with a List**: And stick to it! Impulse purchases are the enemy of budgets.",
            "☕ **The Latte Factor**: Small daily expenses add up. That $5 coffee is $1,825 per year!"
        )
        return tips.random()
    }
    
    private fun generateGeneralResponse(message: String): String {
        return """
            Thanks for reaching out! I'm here to help you manage your finances in an ADHD-friendly way.
            
            I can help you with:
            • 💰 Spending analysis and budgeting
            • 💳 Debt management strategies  
            • 🎯 Breaking down overwhelming tasks
            • 📈 Tracking your progress
            • 💡 Money-saving tips and tricks
            
            What would you like to explore first?
        """.trimIndent()
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class AICoachUiState(
    val conversations: List<ChatMessage> = emptyList(),
    val currentMessage: String = "",
    val isLoading: Boolean = false,
    val error: String? = null
)

data class ChatMessage(
    val content: String,
    val isFromUser: Boolean,
    val timestamp: Long
)
