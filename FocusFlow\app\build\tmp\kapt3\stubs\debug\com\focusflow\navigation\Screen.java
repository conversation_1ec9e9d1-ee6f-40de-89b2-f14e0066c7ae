package com.focusflow.navigation;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000<\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\b\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014B\u001f\b\u0004\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\u0002\u0010\u0007R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000b\u0082\u0001\b\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u00a8\u0006\u001d"}, d2 = {"Lcom/focusflow/navigation/Screen;", "", "route", "", "title", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "(Ljava/lang/String;Ljava/lang/String;Landroidx/compose/ui/graphics/vector/ImageVector;)V", "getIcon", "()Landroidx/compose/ui/graphics/vector/ImageVector;", "getRoute", "()Ljava/lang/String;", "getTitle", "AICoach", "Budget", "Dashboard", "Debt", "Expenses", "Habits", "Settings", "Tasks", "Lcom/focusflow/navigation/Screen$AICoach;", "Lcom/focusflow/navigation/Screen$Budget;", "Lcom/focusflow/navigation/Screen$Dashboard;", "Lcom/focusflow/navigation/Screen$Debt;", "Lcom/focusflow/navigation/Screen$Expenses;", "Lcom/focusflow/navigation/Screen$Habits;", "Lcom/focusflow/navigation/Screen$Settings;", "Lcom/focusflow/navigation/Screen$Tasks;", "app_debug"})
public abstract class Screen {
    @org.jetbrains.annotations.NotNull
    private final java.lang.String route = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String title = null;
    @org.jetbrains.annotations.NotNull
    private final androidx.compose.ui.graphics.vector.ImageVector icon = null;
    
    private Screen(java.lang.String route, java.lang.String title, androidx.compose.ui.graphics.vector.ImageVector icon) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getRoute() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.graphics.vector.ImageVector getIcon() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$AICoach;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class AICoach extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.AICoach INSTANCE = null;
        
        private AICoach() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$Budget;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class Budget extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.Budget INSTANCE = null;
        
        private Budget() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$Dashboard;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class Dashboard extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.Dashboard INSTANCE = null;
        
        private Dashboard() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$Debt;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class Debt extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.Debt INSTANCE = null;
        
        private Debt() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$Expenses;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class Expenses extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.Expenses INSTANCE = null;
        
        private Expenses() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$Habits;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class Habits extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.Habits INSTANCE = null;
        
        private Habits() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$Settings;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class Settings extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.Settings INSTANCE = null;
        
        private Settings() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/focusflow/navigation/Screen$Tasks;", "Lcom/focusflow/navigation/Screen;", "()V", "app_debug"})
    public static final class Tasks extends com.focusflow.navigation.Screen {
        @org.jetbrains.annotations.NotNull
        public static final com.focusflow.navigation.Screen.Tasks INSTANCE = null;
        
        private Tasks() {
        }
    }
}