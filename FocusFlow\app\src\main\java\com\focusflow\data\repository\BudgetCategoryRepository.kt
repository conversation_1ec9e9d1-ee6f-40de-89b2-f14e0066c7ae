package com.focusflow.data.repository

import com.focusflow.data.dao.BudgetCategoryDao
import com.focusflow.data.model.BudgetCategory
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class BudgetCategoryRepository @Inject constructor(
    private val budgetCategoryDao: BudgetCategoryDao
) {
    fun getAllBudgetCategories(): Flow<List<BudgetCategory>> = budgetCategoryDao.getAllBudgetCategories()

    fun getBudgetCategoriesByPeriod(period: String): Flow<List<BudgetCategory>> =
        budgetCategoryDao.getBudgetCategoriesByPeriod(period)

    suspend fun getBudgetCategoryByName(name: String): BudgetCategory? =
        budgetCategoryDao.getBudgetCategoryByName(name)

    suspend fun getTotalBudgetForPeriod(period: String): Double =
        budgetCategoryDao.getTotalBudgetForPeriod(period) ?: 0.0

    suspend fun getTotalSpentForPeriod(period: String): Double =
        budgetCategoryDao.getTotalSpentForPeriod(period) ?: 0.0

    suspend fun insertBudgetCategory(budgetCategory: BudgetCategory): Long =
        budgetCategoryDao.insertBudgetCategory(budgetCategory)

    suspend fun updateBudgetCategory(budgetCategory: BudgetCategory) =
        budgetCategoryDao.updateBudgetCategory(budgetCategory)

    suspend fun deleteBudgetCategory(budgetCategory: BudgetCategory) =
        budgetCategoryDao.deleteBudgetCategory(budgetCategory)

    suspend fun updateSpentAmount(categoryId: Long, newSpentAmount: Double) =
        budgetCategoryDao.updateSpentAmount(categoryId, newSpentAmount)

    // Sync methods for services that need immediate results
    suspend fun getAllBudgetCategoriesSync(): List<BudgetCategory> =
        budgetCategoryDao.getAllBudgetCategoriesSync()

    suspend fun getBudgetCategoryByNameSync(name: String): BudgetCategory? =
        budgetCategoryDao.getBudgetCategoryByName(name)
}

