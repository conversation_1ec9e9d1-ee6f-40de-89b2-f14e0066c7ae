package com.focusflow.data.repository

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.service.FocusFlowNotificationManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val userPreferencesRepository: UserPreferencesRepository,
    private val notificationManager: FocusFlowNotificationManager
) {
    
    suspend fun initializeNotifications() {
        val preferences = userPreferencesRepository.getUserPreferencesSync()
        
        if (preferences?.notificationsEnabled == true && hasNotificationPermission()) {
            setupAllNotifications()
        }
    }
    
    suspend fun setupAllNotifications() {
        val preferences = userPreferencesRepository.getUserPreferencesSync() ?: return
        
        if (!preferences.notificationsEnabled || !hasNotificationPermission()) {
            return
        }
        
        // Parse notification time (format: "HH:mm")
        val timeParts = preferences.reminderTime.split(":")
        val hour = timeParts.getOrNull(0)?.toIntOrNull() ?: 18
        val minute = timeParts.getOrNull(1)?.toIntOrNull() ?: 0
        
        // Schedule daily spending reminder
        notificationManager.scheduleDailySpendingReminder(hour, minute)
        
        // Schedule weekly budget summary
        notificationManager.scheduleWeeklyBudgetSummary()
        
        // Schedule habit reminders (we'll add specific habits later)
        scheduleDefaultHabitReminders(hour, minute)
    }
    
    private fun scheduleDefaultHabitReminders(hour: Int, minute: Int) {
        // Schedule reminders for common habits
        val habits = listOf(
            "medication" to (hour to minute), // Same time as daily reminder
            "sleep tracking" to (22 to 0), // 10 PM for sleep tracking
            "mood check" to (hour to minute) // Same time as daily reminder
        )
        
        habits.forEach { (habitName, time) ->
            notificationManager.scheduleHabitReminder(habitName, time.first, time.second)
        }
    }
    
    suspend fun updateNotificationSettings(enabled: Boolean, time: String) {
        if (enabled && hasNotificationPermission()) {
            // Cancel existing notifications
            notificationManager.cancelAllNotifications()
            
            // Setup new notifications with updated time
            setupAllNotifications()
        } else {
            // Cancel all notifications
            notificationManager.cancelAllNotifications()
        }
    }
    
    fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            true // Notifications are allowed by default on older versions
        }
    }
    
    fun showAchievementNotification(achievementTitle: String, description: String) {
        if (hasNotificationPermission()) {
            notificationManager.showAchievementNotification(
                "Achievement Unlocked! 🎉",
                "$achievementTitle: $description"
            )
        }
    }
    
    fun showBudgetWarning(categoryName: String, percentageUsed: Double) {
        if (hasNotificationPermission()) {
            val message = when {
                percentageUsed >= 100 -> "You've exceeded your $categoryName budget! Consider reviewing your spending."
                percentageUsed >= 90 -> "You've used ${percentageUsed.toInt()}% of your $categoryName budget. Almost at your limit!"
                percentageUsed >= 80 -> "You've used ${percentageUsed.toInt()}% of your $categoryName budget. Getting close to your limit."
                else -> return // Don't show warning for lower percentages
            }
            
            notificationManager.showBudgetWarning(message)
        }
    }
    
    fun showSpendingMilestone(amount: Double, period: String) {
        if (hasNotificationPermission()) {
            notificationManager.showAchievementNotification(
                "Spending Milestone! 💰",
                "You've successfully tracked $${String.format("%.2f", amount)} in expenses this $period!"
            )
        }
    }
    
    fun showDebtPaymentReminder(cardName: String, amount: Double, daysUntilDue: Int) {
        if (hasNotificationPermission()) {
            val urgencyMessage = when (daysUntilDue) {
                0 -> "due today"
                1 -> "due tomorrow"
                else -> "due in $daysUntilDue days"
            }
            
            notificationManager.scheduleBillReminder(
                billName = cardName,
                dueDate = System.currentTimeMillis() + (daysUntilDue * 24 * 60 * 60 * 1000),
                daysBeforeList = listOf(3, 1, 0) // Remind 3 days before, 1 day before, and on the day
            )
        }
    }
    
    fun showHabitStreakCelebration(habitName: String, streakDays: Int) {
        if (hasNotificationPermission()) {
            val message = when {
                streakDays >= 30 -> "Amazing! You've maintained your $habitName habit for $streakDays days! 🌟"
                streakDays >= 14 -> "Great job! $streakDays days of $habitName! You're building a strong habit! 💪"
                streakDays >= 7 -> "One week of $habitName! Keep up the excellent work! 🎯"
                streakDays >= 3 -> "$streakDays days of $habitName! You're on a roll! 🔥"
                else -> return // Don't celebrate very short streaks
            }
            
            notificationManager.showAchievementNotification(
                "Habit Streak! 🔥",
                message
            )
        }
    }
    
    suspend fun scheduleCustomNotification(
        title: String,
        message: String,
        triggerTimeMillis: Long,
        notificationType: String = "custom"
    ) {
        if (hasNotificationPermission()) {
            // This would be used for custom reminders or one-time notifications
            // Implementation would depend on specific requirements
        }
    }
    
    fun cancelAllNotifications() {
        notificationManager.cancelAllNotifications()
    }
    
    // Notification analytics (for future use)
    fun trackNotificationInteraction(notificationType: String, action: String) {
        // This could be used to track which notifications are most effective
        // and adjust the notification strategy accordingly
        // For now, we'll just log it (implementation in Phase 4)
    }
    
    // ADHD-friendly notification timing
    fun getOptimalNotificationTime(userPreferredTime: String): Pair<Int, Int> {
        // Parse user preferred time
        val timeParts = userPreferredTime.split(":")
        val preferredHour = timeParts.getOrNull(0)?.toIntOrNull() ?: 18
        val preferredMinute = timeParts.getOrNull(1)?.toIntOrNull() ?: 0
        
        // For ADHD users, we might want to adjust timing based on research
        // For now, we'll use their preferred time but could add logic to:
        // - Avoid notification fatigue
        // - Space out notifications appropriately
        // - Consider circadian rhythms
        
        return Pair(preferredHour, preferredMinute)
    }
    
    // Check if it's a good time to send notifications (avoid overwhelming the user)
    fun isGoodTimeForNotification(): Boolean {
        val currentHour = java.util.Calendar.getInstance().get(java.util.Calendar.HOUR_OF_DAY)
        
        // Avoid very early morning (before 7 AM) and very late night (after 10 PM)
        // This is especially important for ADHD users who may have irregular sleep patterns
        return currentHour in 7..22
    }
}
