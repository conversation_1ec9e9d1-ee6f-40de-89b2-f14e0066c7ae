package com.focusflow.receiver

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.focusflow.MainActivity
import com.focusflow.R
import com.focusflow.service.FocusFlowNotificationManager

class AlarmReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context, intent: Intent) {
        val notificationType = intent.getStringExtra("notification_type") ?: "general"
        val title = intent.getStringExtra("title") ?: "FocusFlow Reminder"
        val message = intent.getStringExtra("message") ?: "Time to check your tasks!"

        showNotification(context, notificationType, title, message)
    }

    private fun showNotification(context: Context, type: String, title: String, message: String) {
        val channelId = when (type) {
            "daily_spending" -> FocusFlowNotificationManager.CHANNEL_SPENDING
            "bill_reminder" -> FocusFlowNotificationManager.CHANNEL_BILLS
            "habit_reminder" -> FocusFlowNotificationManager.CHANNEL_HABITS
            "budget_summary" -> FocusFlowNotificationManager.CHANNEL_BUDGET
            else -> FocusFlowNotificationManager.CHANNEL_SPENDING
        }

        val notificationId = when (type) {
            "daily_spending" -> FocusFlowNotificationManager.NOTIFICATION_DAILY_SPENDING
            "bill_reminder" -> FocusFlowNotificationManager.NOTIFICATION_BILL_REMINDER
            "habit_reminder" -> FocusFlowNotificationManager.NOTIFICATION_HABIT_REMINDER
            "budget_summary" -> FocusFlowNotificationManager.NOTIFICATION_BUDGET_SUMMARY
            else -> FocusFlowNotificationManager.NOTIFICATION_DAILY_SPENDING
        }

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            // Add navigation hints based on notification type
            when (type) {
                "daily_spending" -> putExtra("navigate_to", "expenses")
                "bill_reminder" -> putExtra("navigate_to", "debt")
                "habit_reminder" -> putExtra("navigate_to", "habits")
                "budget_summary" -> putExtra("navigate_to", "budget")
            }
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            notificationId,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val priority = when (type) {
            "bill_reminder" -> NotificationCompat.PRIORITY_HIGH
            "achievement" -> NotificationCompat.PRIORITY_LOW
            else -> NotificationCompat.PRIORITY_DEFAULT
        }

        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(android.R.drawable.ic_dialog_info) // TODO: Replace with custom icon
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(priority)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .addQuickActions(context, type)
            .build()

        val notificationManager = NotificationManagerCompat.from(context)
        notificationManager.notify(notificationId, notification)
    }

    private fun NotificationCompat.Builder.addQuickActions(context: Context, type: String): NotificationCompat.Builder {
        return when (type) {
            "daily_spending" -> {
                val quickLogIntent = Intent(context, MainActivity::class.java).apply {
                    putExtra("quick_action", "log_expense")
                }
                val quickLogPendingIntent = PendingIntent.getActivity(
                    context,
                    3001,
                    quickLogIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                addAction(
                    android.R.drawable.ic_input_add,
                    "Quick Log",
                    quickLogPendingIntent
                )
            }
            "habit_reminder" -> {
                val quickLogIntent = Intent(context, MainActivity::class.java).apply {
                    putExtra("quick_action", "log_habit")
                }
                val quickLogPendingIntent = PendingIntent.getActivity(
                    context,
                    3002,
                    quickLogIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                addAction(
                    android.R.drawable.ic_input_add,
                    "Log Now",
                    quickLogPendingIntent
                )
            }
            else -> this
        }
    }
}
