package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "budget_categories")
data class BudgetCategory(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val allocatedAmount: Double,
    val spentAmount: Double = 0.0,
    val budgetPeriod: String, // "weekly" or "monthly"
    val budgetYear: Int,
    val budgetMonth: Int? = null, // null for weekly budgets
    val budgetWeek: Int? = null, // week number for weekly budgets
    val isActive: Boolean = true,
    // Enhanced budget features
    val recommendedAmount: Double? = null, // AI-suggested amount
    val lastRecommendationUpdate: kotlinx.datetime.LocalDateTime? = null,
    val varianceThreshold: Double = 0.1, // 10% variance threshold for alerts
    val categoryColor: String = "#2196F3" // Color for visual identification
)

