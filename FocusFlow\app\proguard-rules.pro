# Add any ProGuard configurations here
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Keep all model classes for Room database
-keep class com.focusflow.data.model.** { *; }

# Keep all DAO interfaces
-keep interface com.focusflow.data.dao.** { *; }

# Keep Hilt generated classes
-keep class dagger.hilt.** { *; }
-keep class * extends dagger.hilt.android.lifecycle.HiltViewModel { *; }

# Keep Compose classes
-keep class androidx.compose.** { *; }

# Keep kotlinx.datetime classes
-keep class kotlinx.datetime.** { *; }

# Keep Room database classes
-keep class androidx.room.** { *; }

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Keep line numbers for crash reports
-keepattributes SourceFile,LineNumberTable

# Rename source file attribute to something that doesn't reveal the original source file name
-renamesourcefileattribute SourceFile

