package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "spending_reflections")
data class SpendingReflection(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val expenseId: Long? = null, // Link to actual expense if purchase was made
    val wishlistItemId: Long? = null, // Link to wishlist item if applicable
    val amount: Double,
    val category: String,
    val itemDescription: String,
    val reflectionDate: LocalDateTime,
    val emotionalState: String? = null, // "stressed", "happy", "bored", etc.
    val triggerReason: String? = null, // "impulse", "need", "social", etc.
    val needVsWant: String? = null, // "need", "want", "unsure"
    val satisfactionLevel: Int? = null, // 1-5 scale
    val regretLevel: Int? = null, // 1-5 scale
    val wouldBuyAgain: Boolean? = null,
    val alternativeConsidered: String? = null,
    val delayHelpful: Boolean? = null,
    val notes: String? = null,
    val mindfulnessScore: Int? = null, // 1-5 scale for how mindful the purchase was
    val budgetImpact: String? = null // "none", "minor", "significant", "major"
)
