package com.focusflow.ui.theme

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material.MaterialTheme
import androidx.compose.material.darkColors
import androidx.compose.material.lightColors
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

// ADHD-friendly color palettes with enhanced accessibility
private val DarkColorPalette = darkColors(
    primary = Color(0xFF64B5F6), // Lighter blue for better contrast in dark mode
    primaryVariant = Color(0xFF2196F3),
    secondary = Color(0xFF81C784), // Calming green
    background = Color(0xFF121212), // True dark background
    surface = Color(0xFF1F1F1F), // Slightly lighter surface
    onPrimary = Color(0xFF000000),
    onSecondary = Color(0xFF000000),
    onBackground = Color(0xFFFFFFFF),
    onSurface = Color(0xFFFFFFFF),
    error = Color(0xFFE57373) // Softer error color for ADHD users
)

private val LightColorPalette = lightColors(
    primary = Color(0xFF2196F3),
    primaryVariant = Color(0xFF1976D2),
    secondary = Color(0xFF4CAF50),
    background = Color(0xFFFAFAFA), // Slightly off-white to reduce eye strain
    surface = Color.White,
    onPrimary = Color.White,
    onSecondary = Color.White,
    onBackground = Color(0xFF212121),
    onSurface = Color(0xFF212121),
    error = Color(0xFFD32F2F)
)

// High contrast palettes for accessibility
private val HighContrastLightPalette = lightColors(
    primary = Color(0xFF000000),
    primaryVariant = Color(0xFF000000),
    secondary = Color(0xFF000000),
    background = Color(0xFFFFFFFF),
    surface = Color(0xFFFFFFFF),
    onPrimary = Color(0xFFFFFFFF),
    onSecondary = Color(0xFFFFFFFF),
    onBackground = Color(0xFF000000),
    onSurface = Color(0xFF000000),
    error = Color(0xFFFF0000)
)

private val HighContrastDarkPalette = darkColors(
    primary = Color(0xFFFFFFFF),
    primaryVariant = Color(0xFFFFFFFF),
    secondary = Color(0xFFFFFFFF),
    background = Color(0xFF000000),
    surface = Color(0xFF000000),
    onPrimary = Color(0xFF000000),
    onSecondary = Color(0xFF000000),
    onBackground = Color(0xFFFFFFFF),
    onSurface = Color(0xFFFFFFFF),
    error = Color(0xFFFF4444)
)

enum class ThemeMode {
    SYSTEM,
    LIGHT,
    DARK,
    HIGH_CONTRAST_LIGHT,
    HIGH_CONTRAST_DARK
}

@Composable
fun FocusFlowTheme(
    themeMode: ThemeMode = ThemeMode.SYSTEM,
    content: @Composable () -> Unit
) {
    val systemInDarkTheme = isSystemInDarkTheme()

    val colors = when (themeMode) {
        ThemeMode.LIGHT -> LightColorPalette
        ThemeMode.DARK -> DarkColorPalette
        ThemeMode.HIGH_CONTRAST_LIGHT -> HighContrastLightPalette
        ThemeMode.HIGH_CONTRAST_DARK -> HighContrastDarkPalette
        ThemeMode.SYSTEM -> if (systemInDarkTheme) DarkColorPalette else LightColorPalette
    }

    MaterialTheme(
        colors = colors,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}

// Overloaded function for backward compatibility
@Composable
fun FocusFlowTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colors = if (darkTheme) {
        DarkColorPalette
    } else {
        LightColorPalette
    }

    MaterialTheme(
        colors = colors,
        typography = Typography,
        shapes = Shapes,
        content = content
    )
}

