package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "wishlist_items")
data class WishlistItem(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val itemName: String,
    val estimatedPrice: Double,
    val category: String,
    val description: String? = null,
    val merchant: String? = null,
    val addedDate: LocalDateTime,
    val delayPeriodHours: Int = 24, // Default 24-hour delay
    val isDelayActive: Boolean = true,
    val delayEndTime: LocalDateTime,
    val priority: String = "medium", // "low", "medium", "high"
    val tags: String? = null, // JSON array of tags
    val imageUrl: String? = null,
    val productUrl: String? = null,
    val isPurchased: Boolean = false,
    val purchasedDate: LocalDateTime? = null,
    val actualPrice: Double? = null,
    val reflectionNotes: String? = null,
    val stillWanted: Boolean? = null // User's reflection after delay period
)
