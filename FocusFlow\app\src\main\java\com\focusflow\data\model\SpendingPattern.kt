package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "spending_patterns")
data class SpendingPattern(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val patternType: String, // "emotional", "temporal", "location", "trigger"
    val patternName: String,
    val description: String,
    val triggerConditions: String, // JSON object with trigger conditions
    val averageAmount: Double,
    val frequency: String, // "daily", "weekly", "monthly", "situational"
    val category: String,
    val emotionalTriggers: String? = null, // JSON array of emotions
    val timePatterns: String? = null, // JSON object with time-based patterns
    val locationTriggers: String? = null, // JSON array of locations/merchants
    val socialTriggers: String? = null, // JSON array of social situations
    val detectedDate: LocalDateTime,
    val lastOccurrence: LocalDateTime? = null,
    val occurrenceCount: Int = 1,
    val confidenceScore: Double, // 0.0 to 1.0
    val isActive: Boolean = true,
    val userConfirmed: Boolean? = null, // null = pending, true = confirmed, false = rejected
    val interventionStrategy: String? = null, // Suggested intervention
    val interventionEffectiveness: Double? = null, // 0.0 to 1.0
    val notes: String? = null,
    val severity: String = "medium", // "low", "medium", "high"
    val impactOnBudget: String = "moderate", // "minimal", "moderate", "significant"
    val relatedPatterns: String? = null, // JSON array of related pattern IDs
    val seasonalFactor: Double = 1.0,
    val stressLevel: Int? = null, // 1-5 scale when pattern occurs
    val preventionSuccess: Int = 0, // Number of times successfully prevented
    val preventionAttempts: Int = 0 // Number of times intervention was attempted
)
