package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate

@Entity(tableName = "credit_cards")
data class CreditCard(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val currentBalance: Double,
    val creditLimit: Double,
    val minimumPayment: Double,
    val dueDate: LocalDate,
    val interestRate: Double,
    val lastPaymentAmount: Double? = null,
    val lastPaymentDate: LocalDate? = null,
    val isActive: Boolean = true
)

