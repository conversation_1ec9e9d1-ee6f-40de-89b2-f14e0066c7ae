package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\u000b\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0014\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\t0\bH\'J\u000e\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\t0\bH\'J\u0018\u0010\u000e\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u000f\u001a\u00020\u0010H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0018\u0010\u0012\u001a\u0004\u0018\u00010\u00052\u0006\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u0016\u0010\u0016\u001a\u00020\u00102\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u0019H\u00a7@\u00a2\u0006\u0002\u0010\u001aJ\u0016\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u001d\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u001e\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010 \u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010!J\u001e\u0010\"\u001a\u00020\u00032\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010#\u001a\u00020$H\u00a7@\u00a2\u0006\u0002\u0010%\u00a8\u0006&"}, d2 = {"Lcom/focusflow/data/dao/DashboardWidgetDao;", "", "deleteWidget", "", "widget", "Lcom/focusflow/data/model/DashboardWidget;", "(Lcom/focusflow/data/model/DashboardWidget;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllWidgets", "Lkotlinx/coroutines/flow/Flow;", "", "getVisibleWidgetCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVisibleWidgets", "getWidgetById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getWidgetByType", "widgetType", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertWidget", "updateLastRefresh", "timestamp", "Lkotlinx/datetime/LocalDateTime;", "(JLkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWidget", "updateWidgetConfiguration", "config", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWidgetPosition", "position", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWidgetVisibility", "isVisible", "", "(JZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao
public abstract interface DashboardWidgetDao {
    
    @androidx.room.Query(value = "SELECT * FROM dashboard_widgets WHERE isVisible = 1 AND isEnabled = 1 ORDER BY position ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.DashboardWidget>> getVisibleWidgets();
    
    @androidx.room.Query(value = "SELECT * FROM dashboard_widgets ORDER BY position ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.DashboardWidget>> getAllWidgets();
    
    @androidx.room.Query(value = "SELECT * FROM dashboard_widgets WHERE widgetType = :widgetType")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getWidgetByType(@org.jetbrains.annotations.NotNull
    java.lang.String widgetType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.DashboardWidget> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM dashboard_widgets WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getWidgetById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.DashboardWidget> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM dashboard_widgets WHERE isVisible = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getVisibleWidgetCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertWidget(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.DashboardWidget widget, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateWidget(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.DashboardWidget widget, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteWidget(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.DashboardWidget widget, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE dashboard_widgets SET position = :position WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateWidgetPosition(long id, int position, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE dashboard_widgets SET isVisible = :isVisible WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateWidgetVisibility(long id, boolean isVisible, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE dashboard_widgets SET lastUpdated = :timestamp WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateLastRefresh(long id, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE dashboard_widgets SET configuration = :config WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateWidgetConfiguration(long id, @org.jetbrains.annotations.NotNull
    java.lang.String config, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}