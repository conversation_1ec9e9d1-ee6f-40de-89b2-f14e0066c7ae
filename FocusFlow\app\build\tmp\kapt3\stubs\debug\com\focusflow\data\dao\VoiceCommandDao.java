package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\b\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0014\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\fH\'J\u0010\u0010\u000e\u001a\u0004\u0018\u00010\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\rH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u001c\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\f2\u0006\u0010\u0015\u001a\u00020\u0005H\'J\u000e\u0010\u0016\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0014\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\fH\'J\u000e\u0010\u0019\u001a\u00020\u0017H\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u0018\u0010\u001a\u001a\u0004\u0018\u00010\t2\u0006\u0010\u001b\u001a\u00020\u001cH\u00a7@\u00a2\u0006\u0002\u0010\u001dJ\u001c\u0010\u001e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\r0\f2\u0006\u0010\u001f\u001a\u00020 H\'J\u0016\u0010!\u001a\u00020\u001c2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001e\u0010\"\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010#\u001a\u00020 H\u00a7@\u00a2\u0006\u0002\u0010$J\u001e\u0010%\u001a\u00020\u00032\u0006\u0010\u001b\u001a\u00020\u001c2\u0006\u0010&\u001a\u00020 H\u00a7@\u00a2\u0006\u0002\u0010$J\u0016\u0010\'\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\n\u00a8\u0006("}, d2 = {"Lcom/focusflow/data/dao/VoiceCommandDao;", "", "deleteOldCommands", "", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteVoiceCommand", "voiceCommand", "Lcom/focusflow/data/model/VoiceCommand;", "(Lcom/focusflow/data/model/VoiceCommand;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllVoiceCommands", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageProcessingTime", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAverageSuccessConfidence", "getCommandTypeUsage", "Lcom/focusflow/data/dao/CommandTypeUsage;", "getRecentCommands", "startDate", "getSuccessfulCommandCount", "", "getSuccessfulCommands", "getTotalCommandCount", "getVoiceCommandById", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVoiceCommandsByType", "commandType", "", "insertVoiceCommand", "updateCorrectedCommand", "correctedCommand", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserFeedback", "feedback", "updateVoiceCommand", "app_debug"})
@androidx.room.Dao
public abstract interface VoiceCommandDao {
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands ORDER BY timestamp DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.VoiceCommand>> getAllVoiceCommands();
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE commandType = :commandType ORDER BY timestamp DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.VoiceCommand>> getVoiceCommandsByType(@org.jetbrains.annotations.NotNull
    java.lang.String commandType);
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE isSuccessful = 1 ORDER BY timestamp DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.VoiceCommand>> getSuccessfulCommands();
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE timestamp >= :startDate ORDER BY timestamp DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.VoiceCommand>> getRecentCommands(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate);
    
    @androidx.room.Query(value = "SELECT * FROM voice_commands WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getVoiceCommandById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.VoiceCommand> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM voice_commands WHERE isSuccessful = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getSuccessfulCommandCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM voice_commands")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalCommandCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(confidence) FROM voice_commands WHERE isSuccessful = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageSuccessConfidence(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(processingTime) FROM voice_commands")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageProcessingTime(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT commandType, COUNT(*) as count FROM voice_commands WHERE isSuccessful = 1 GROUP BY commandType ORDER BY count DESC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCommandTypeUsage(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.dao.CommandTypeUsage>> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertVoiceCommand(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.VoiceCommand voiceCommand, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateVoiceCommand(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.VoiceCommand voiceCommand, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteVoiceCommand(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.VoiceCommand voiceCommand, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE voice_commands SET userFeedback = :feedback WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateUserFeedback(long id, @org.jetbrains.annotations.NotNull
    java.lang.String feedback, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE voice_commands SET correctedCommand = :correctedCommand WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateCorrectedCommand(long id, @org.jetbrains.annotations.NotNull
    java.lang.String correctedCommand, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM voice_commands WHERE timestamp < :cutoffDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteOldCommands(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}