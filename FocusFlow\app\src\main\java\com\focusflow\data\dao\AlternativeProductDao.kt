package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.AlternativeProduct
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface AlternativeProductDao {
    
    @Query("SELECT * FROM alternative_products WHERE isActive = 1 ORDER BY confidenceScore DESC")
    fun getAllActiveAlternatives(): Flow<List<AlternativeProduct>>
    
    @Query("SELECT * FROM alternative_products WHERE originalCategory = :category AND isActive = 1 ORDER BY savingsAmount DESC")
    fun getAlternativesByCategory(category: String): Flow<List<AlternativeProduct>>
    
    @Query("SELECT * FROM alternative_products WHERE alternativeType = :type AND isActive = 1 ORDER BY savingsAmount DESC")
    fun getAlternativesByType(type: String): Flow<List<AlternativeProduct>>
    
    @Query("SELECT * FROM alternative_products WHERE originalProductName LIKE '%' || :productName || '%' AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getAlternativesForProduct(productName: String): Flow<List<AlternativeProduct>>
    
    @Query("SELECT * FROM alternative_products WHERE savingsAmount >= :minSavings AND isActive = 1 ORDER BY savingsAmount DESC")
    fun getAlternativesBySavings(minSavings: Double): Flow<List<AlternativeProduct>>
    
    @Query("SELECT * FROM alternative_products WHERE id = :id")
    suspend fun getAlternativeById(id: Long): AlternativeProduct?
    
    @Query("SELECT * FROM alternative_products WHERE confidenceScore >= :minConfidence AND isActive = 1 ORDER BY confidenceScore DESC LIMIT :limit")
    suspend fun getTopAlternatives(minConfidence: Double, limit: Int): List<AlternativeProduct>
    
    @Query("SELECT COUNT(*) FROM alternative_products WHERE isActive = 1")
    suspend fun getActiveAlternativeCount(): Int
    
    @Query("SELECT AVG(savingsAmount) FROM alternative_products WHERE isActive = 1")
    suspend fun getAverageSavings(): Double?
    
    @Query("SELECT SUM(savingsAmount * timesAccepted) FROM alternative_products WHERE isActive = 1")
    suspend fun getTotalSavingsRealized(): Double?
    
    @Query("SELECT AVG(userRating) FROM alternative_products WHERE userRating IS NOT NULL")
    suspend fun getAverageUserRating(): Double?
    
    @Insert
    suspend fun insertAlternative(alternative: AlternativeProduct): Long
    
    @Update
    suspend fun updateAlternative(alternative: AlternativeProduct)
    
    @Delete
    suspend fun deleteAlternative(alternative: AlternativeProduct)
    
    @Query("UPDATE alternative_products SET timesShown = timesShown + 1, lastSuggested = :timestamp WHERE id = :id")
    suspend fun recordAlternativeShown(id: Long, timestamp: LocalDateTime)
    
    @Query("UPDATE alternative_products SET timesAccepted = timesAccepted + 1 WHERE id = :id")
    suspend fun recordAlternativeAccepted(id: Long)
    
    @Query("UPDATE alternative_products SET timesRejected = timesRejected + 1 WHERE id = :id")
    suspend fun recordAlternativeRejected(id: Long)
    
    @Query("UPDATE alternative_products SET userRating = :rating, userFeedback = :feedback WHERE id = :id")
    suspend fun updateUserFeedback(id: Long, rating: Int?, feedback: String?)
    
    @Query("UPDATE alternative_products SET isActive = 0 WHERE id = :id")
    suspend fun deactivateAlternative(id: Long)
    
    @Query("DELETE FROM alternative_products WHERE createdDate < :cutoffDate AND timesAccepted = 0")
    suspend fun deleteUnusedOldAlternatives(cutoffDate: LocalDateTime)
}
