package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "tasks")
data class Task(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val title: String,
    val description: String? = null,
    val isCompleted: Boolean = false,
    val dueDate: LocalDateTime? = null,
    val priority: String = "medium", // "low", "medium", "high"
    val category: String? = null,
    val isRecurring: Boolean = false,
    val recurringFrequency: String? = null, // "daily", "weekly", "monthly"
    val createdAt: LocalDateTime,
    val completedAt: LocalDateTime? = null,
    val estimatedDuration: Int? = null // in minutes
)

