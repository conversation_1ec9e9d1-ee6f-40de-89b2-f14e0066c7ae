package com.focusflow.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\u001a(\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u00a8\u0006\b"}, d2 = {"ErrorSnackbar", "", "error", "", "snackbarHostState", "Landroidx/compose/material/SnackbarHostState;", "onErrorShown", "Lkotlin/Function0;", "app_debug"})
public final class ErrorHandlingKt {
    
    @androidx.compose.runtime.Composable
    public static final void ErrorSnackbar(@org.jetbrains.annotations.Nullable
    java.lang.String error, @org.jetbrains.annotations.NotNull
    androidx.compose.material.SnackbarHostState snackbarHostState, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onErrorShown) {
    }
}