package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.repository.UserPreferencesRepository
import com.focusflow.data.repository.NotificationRepository
import com.focusflow.ui.theme.ThemeMode
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainViewModel @Inject constructor(
    private val userPreferencesRepository: UserPreferencesRepository,
    private val gamificationService: com.focusflow.service.GamificationService,
    private val notificationRepository: NotificationRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(MainUiState())
    val uiState: StateFlow<MainUiState> = _uiState.asStateFlow()

    init {
        checkOnboardingStatus()
        // Initialize gamification system
        viewModelScope.launch {
            try {
                gamificationService.initializeGamification()
            } catch (e: Exception) {
                // Log error but don't block app startup
            }
        }

        // Initialize notification system
        viewModelScope.launch {
            try {
                notificationRepository.initializeNotifications()
            } catch (e: Exception) {
                // Log error but don't block app startup
            }
        }
    }

    fun checkOnboardingStatus() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)

                val userPreferences = userPreferencesRepository.getUserPreferencesSync()
                val hasCompletedOnboarding = userPreferences?.hasCompletedOnboarding ?: false
                val themeMode = parseThemeMode(userPreferences?.themePreference)

                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasCompletedOnboarding = hasCompletedOnboarding,
                    themeMode = themeMode
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    hasCompletedOnboarding = false,
                    error = "Failed to check onboarding status: ${e.message}"
                )
            }
        }
    }

    fun updateTheme(themeMode: ThemeMode) {
        viewModelScope.launch {
            try {
                userPreferencesRepository.updateThemePreference(themeMode.name)
                _uiState.value = _uiState.value.copy(themeMode = themeMode)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update theme: ${e.message}"
                )
            }
        }
    }

    private fun parseThemeMode(themePreference: String?): ThemeMode {
        return try {
            ThemeMode.valueOf(themePreference ?: "SYSTEM")
        } catch (e: Exception) {
            ThemeMode.SYSTEM
        }
    }
}

data class MainUiState(
    val isLoading: Boolean = true,
    val hasCompletedOnboarding: Boolean = false,
    val themeMode: ThemeMode? = null,
    val error: String? = null
)
