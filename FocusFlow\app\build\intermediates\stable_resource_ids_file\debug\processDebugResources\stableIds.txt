com.focusflow.debug:xml/file_paths = 0x7f110002
com.focusflow.debug:styleable/ViewBackgroundHelper = 0x7f100035
com.focusflow.debug:styleable/Toolbar = 0x7f100033
com.focusflow.debug:styleable/RecycleListView = 0x7f10002c
com.focusflow.debug:styleable/PopupWindowBackgroundState = 0x7f10002b
com.focusflow.debug:styleable/Navigator = 0x7f100029
com.focusflow.debug:styleable/NavGraphNavigator = 0x7f100026
com.focusflow.debug:styleable/MenuView = 0x7f100022
com.focusflow.debug:styleable/LinearLayoutCompat_Layout = 0x7f10001e
com.focusflow.debug:styleable/GradientColorItem = 0x7f10001c
com.focusflow.debug:styleable/Fragment = 0x7f100019
com.focusflow.debug:styleable/FontFamily = 0x7f100017
com.focusflow.debug:styleable/CompoundButton = 0x7f100015
com.focusflow.debug:styleable/ColorStateListItem = 0x7f100014
com.focusflow.debug:styleable/Capability = 0x7f100012
com.focusflow.debug:styleable/AppCompatTextView = 0x7f10000f
com.focusflow.debug:styleable/AppCompatImageView = 0x7f10000c
com.focusflow.debug:styleable/AppCompatEmojiHelper = 0x7f10000b
com.focusflow.debug:styleable/AnimatedStateListDrawableTransition = 0x7f10000a
com.focusflow.debug:styleable/AlertDialog = 0x7f100007
com.focusflow.debug:styleable/ActivityNavigator = 0x7f100006
com.focusflow.debug:styleable/ActivityChooserView = 0x7f100005
com.focusflow.debug:styleable/ActionMenuView = 0x7f100003
com.focusflow.debug:styleable/ActionMenuItemView = 0x7f100002
com.focusflow.debug:styleable/ActionBarLayout = 0x7f100001
com.focusflow.debug:styleable/ActionBar = 0x7f100000
com.focusflow.debug:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f0160
com.focusflow.debug:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0f015e
com.focusflow.debug:style/Widget.AppCompat.Spinner.Underlined = 0x7f0f015c
com.focusflow.debug:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0f0158
com.focusflow.debug:style/Widget.AppCompat.RatingBar.Small = 0x7f0f0154
com.focusflow.debug:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0f0153
com.focusflow.debug:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0151
com.focusflow.debug:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0f014e
com.focusflow.debug:style/Widget.AppCompat.ListView.DropDown = 0x7f0f014b
com.focusflow.debug:style/Widget.AppCompat.ListPopupWindow = 0x7f0f0149
com.focusflow.debug:style/Widget.AppCompat.ListMenuView = 0x7f0f0148
com.focusflow.debug:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0145
com.focusflow.debug:style/Widget.AppCompat.Light.PopupMenu = 0x7f0f0144
com.focusflow.debug:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0f0141
com.focusflow.debug:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0f013d
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0139
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0f0136
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0f0134
com.focusflow.debug:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0f0142
com.focusflow.debug:style/Widget.AppCompat.ImageButton = 0x7f0f0131
com.focusflow.debug:style/Widget.AppCompat.EditText = 0x7f0f0130
com.focusflow.debug:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0f012f
com.focusflow.debug:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0f012d
com.focusflow.debug:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f012c
com.focusflow.debug:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f012a
com.focusflow.debug:style/Widget.AppCompat.ButtonBar = 0x7f0f0129
com.focusflow.debug:style/Widget.AppCompat.Button.Small = 0x7f0f0128
com.focusflow.debug:style/Widget.AppCompat.Button.Colored = 0x7f0f0127
com.focusflow.debug:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0125
com.focusflow.debug:style/Widget.AppCompat.Button = 0x7f0f0123
com.focusflow.debug:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0f0147
com.focusflow.debug:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0f0122
com.focusflow.debug:style/Widget.AppCompat.ActivityChooserView = 0x7f0f0121
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0137
com.focusflow.debug:style/Widget.AppCompat.ActionMode = 0x7f0f0120
com.focusflow.debug:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0f011f
com.focusflow.debug:styleable/ButtonBarLayout = 0x7f100011
com.focusflow.debug:style/Widget.AppCompat.ActionButton = 0x7f0f011d
com.focusflow.debug:style/Widget.AppCompat.ActionBar.TabView = 0x7f0f011c
com.focusflow.debug:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f0116
com.focusflow.debug:styleable/PopupWindow = 0x7f10002a
com.focusflow.debug:style/Theme.AppCompat.NoActionBar = 0x7f0f010c
com.focusflow.debug:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f010a
com.focusflow.debug:style/Theme.AppCompat.Light.Dialog = 0x7f0f0107
com.focusflow.debug:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0f0106
com.focusflow.debug:style/Theme.AppCompat.Light = 0x7f0f0105
com.focusflow.debug:style/Theme.AppCompat.Empty = 0x7f0f0104
com.focusflow.debug:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0f0102
com.focusflow.debug:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0f00ff
com.focusflow.debug:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0f00fe
com.focusflow.debug:style/Widget.AppCompat.RatingBar = 0x7f0f0152
com.focusflow.debug:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0f00fd
com.focusflow.debug:style/Theme.AppCompat.DayNight.Dialog = 0x7f0f00fb
com.focusflow.debug:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0f00fa
com.focusflow.debug:style/Theme.AppCompat = 0x7f0f00f7
com.focusflow.debug:style/TextAppearance.Compat.Notification.Line2 = 0x7f0f00f1
com.focusflow.debug:style/TextAppearance.Compat.Notification.Info = 0x7f0f00f0
com.focusflow.debug:style/TextAppearance.Compat.Notification = 0x7f0f00ef
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f00eb
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f00ea
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f00e6
com.focusflow.debug:style/Theme.AppCompat.DialogWhenLarge = 0x7f0f0103
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.Button = 0x7f0f00e5
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f00e3
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f00e1
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f00e0
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f00df
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f00dc
com.focusflow.debug:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0f00da
com.focusflow.debug:style/TextAppearance.AppCompat.Title = 0x7f0f00d9
com.focusflow.debug:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f00d8
com.focusflow.debug:style/TextAppearance.AppCompat.Subhead = 0x7f0f00d7
com.focusflow.debug:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0f00d6
com.focusflow.debug:style/TextAppearance.AppCompat.Small = 0x7f0f00d5
com.focusflow.debug:style/TextAppearance.AppCompat.Menu = 0x7f0f00d2
com.focusflow.debug:style/TextAppearance.AppCompat.Medium = 0x7f0f00d0
com.focusflow.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f00cf
com.focusflow.debug:style/TextAppearance.AppCompat.Large = 0x7f0f00ca
com.focusflow.debug:style/TextAppearance.AppCompat.Display3 = 0x7f0f00c6
com.focusflow.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0f00be
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0f00ba
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0f00b7
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0f00b6
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0f00b0
com.focusflow.debug:style/Platform.V25.AppCompat.Light = 0x7f0f00ac
com.focusflow.debug:style/Platform.V21.AppCompat.Light = 0x7f0f00aa
com.focusflow.debug:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0f00a8
com.focusflow.debug:style/Platform.ThemeOverlay.AppCompat = 0x7f0f00a6
com.focusflow.debug:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f00a0
com.focusflow.debug:style/Base.Widget.AppCompat.Toolbar = 0x7f0f009f
com.focusflow.debug:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0f009e
com.focusflow.debug:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0f0098
com.focusflow.debug:style/Base.Widget.AppCompat.RatingBar = 0x7f0f0094
com.focusflow.debug:style/Base.Widget.AppCompat.ProgressBar = 0x7f0f0092
com.focusflow.debug:style/Base.Widget.AppCompat.PopupWindow = 0x7f0f0091
com.focusflow.debug:style/Base.Widget.AppCompat.PopupMenu = 0x7f0f008f
com.focusflow.debug:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0f008e
com.focusflow.debug:style/Base.Widget.AppCompat.ListMenuView = 0x7f0f008a
com.focusflow.debug:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0f0088
com.focusflow.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0085
com.focusflow.debug:style/Base.Widget.AppCompat.EditText = 0x7f0f0080
com.focusflow.debug:style/TextAppearance.AppCompat.Display2 = 0x7f0f00c5
com.focusflow.debug:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0f007c
com.focusflow.debug:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f007b
com.focusflow.debug:style/Widget.AppCompat.Toolbar = 0x7f0f015f
com.focusflow.debug:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f007a
com.focusflow.debug:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f0079
com.focusflow.debug:style/Base.Widget.AppCompat.Button.Small = 0x7f0f0077
com.focusflow.debug:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0075
com.focusflow.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f00f5
com.focusflow.debug:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0f0073
com.focusflow.debug:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0f006e
com.focusflow.debug:styleable/FontFamilyFont = 0x7f100018
com.focusflow.debug:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0f006d
com.focusflow.debug:style/Base.Widget.AppCompat.ActionButton = 0x7f0f006c
com.focusflow.debug:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0f006b
com.focusflow.debug:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0f0069
com.focusflow.debug:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0f0068
com.focusflow.debug:style/Base.Widget.AppCompat.ActionBar = 0x7f0f0067
com.focusflow.debug:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0f0066
com.focusflow.debug:style/Base.V7.Widget.AppCompat.EditText = 0x7f0f0065
com.focusflow.debug:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0064
com.focusflow.debug:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0f0063
com.focusflow.debug:style/Platform.AppCompat = 0x7f0f00a4
com.focusflow.debug:style/Base.V28.Theme.AppCompat = 0x7f0f005d
com.focusflow.debug:style/Base.V26.Theme.AppCompat = 0x7f0f005a
com.focusflow.debug:style/Base.V23.Theme.AppCompat.Light = 0x7f0f0059
com.focusflow.debug:style/Base.V23.Theme.AppCompat = 0x7f0f0058
com.focusflow.debug:style/Base.V22.Theme.AppCompat = 0x7f0f0056
com.focusflow.debug:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0f0055
com.focusflow.debug:style/Base.V21.Theme.AppCompat.Light = 0x7f0f0053
com.focusflow.debug:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0f0052
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0f00e4
com.focusflow.debug:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f004f
com.focusflow.debug:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0f004b
com.focusflow.debug:style/Base.ThemeOverlay.AppCompat = 0x7f0f004a
com.focusflow.debug:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0f0047
com.focusflow.debug:style/TextAppearance.AppCompat.Caption = 0x7f0f00c3
com.focusflow.debug:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0f0041
com.focusflow.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f003b
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0038
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f0034
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f0033
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f0031
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0f002f
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f002e
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f002d
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0029
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Title = 0x7f0f0025
com.focusflow.debug:style/Widget.AppCompat.PopupMenu = 0x7f0f014d
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0f00af
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f0024
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0f0023
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0f0022
com.focusflow.debug:styleable/AppCompatTextHelper = 0x7f10000e
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Small = 0x7f0f0021
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f00de
com.focusflow.debug:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0f0020
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0f00b4
com.focusflow.debug:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0f001e
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Menu = 0x7f0f001d
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0f001c
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f001a
com.focusflow.debug:style/ThemeOverlay.AppCompat.Light = 0x7f0f0117
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0019
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0f0018
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0f0014
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0f0013
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0f000d
com.focusflow.debug:style/Base.TextAppearance.AppCompat = 0x7f0f000c
com.focusflow.debug:style/Base.DialogWindowTitle.AppCompat = 0x7f0f000a
com.focusflow.debug:style/Base.Animation.AppCompat.DropDownUp = 0x7f0f0008
com.focusflow.debug:style/Base.Animation.AppCompat.Dialog = 0x7f0f0007
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0f00b8
com.focusflow.debug:style/Base.Widget.AppCompat.ButtonBar = 0x7f0f0078
com.focusflow.debug:style/Base.AlertDialog.AppCompat.Light = 0x7f0f0006
com.focusflow.debug:style/Base.AlertDialog.AppCompat = 0x7f0f0005
com.focusflow.debug:style/Widget.AppCompat.Button.Borderless = 0x7f0f0124
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0f0026
com.focusflow.debug:style/Animation.AppCompat.Tooltip = 0x7f0f0004
com.focusflow.debug:style/AlertDialog.AppCompat = 0x7f0f0000
com.focusflow.debug:string/template_percent = 0x7f0e0047
com.focusflow.debug:string/switch_role = 0x7f0e0045
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar = 0x7f0f0132
com.focusflow.debug:string/range_end = 0x7f0e0040
com.focusflow.debug:string/on = 0x7f0e003f
com.focusflow.debug:string/off = 0x7f0e003e
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0f013a
com.focusflow.debug:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0f0044
com.focusflow.debug:string/navigation_menu = 0x7f0e003a
com.focusflow.debug:string/get_started = 0x7f0e0037
com.focusflow.debug:string/fingerprint_error_hw_not_present = 0x7f0e002f
com.focusflow.debug:string/fingerprint_dialog_touch_sensor = 0x7f0e002d
com.focusflow.debug:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0f015b
com.focusflow.debug:style/Widget.AppCompat.PopupWindow = 0x7f0f014f
com.focusflow.debug:string/default_popup_window_title = 0x7f0e002b
com.focusflow.debug:string/default_error_msg = 0x7f0e0028
com.focusflow.debug:string/default_error_message = 0x7f0e0027
com.focusflow.debug:string/close_sheet = 0x7f0e0025
com.focusflow.debug:string/call_notification_screening_text = 0x7f0e0023
com.focusflow.debug:string/call_notification_decline_action = 0x7f0e001f
com.focusflow.debug:string/call_notification_answer_video_action = 0x7f0e001e
com.focusflow.debug:style/TextAppearance.Compat.Notification.Time = 0x7f0f00f2
com.focusflow.debug:string/abc_toolbar_collapse_description = 0x7f0e001a
com.focusflow.debug:string/abc_shareactionprovider_share_with = 0x7f0e0018
com.focusflow.debug:styleable/AnimatedStateListDrawableCompat = 0x7f100008
com.focusflow.debug:string/abc_searchview_description_voice = 0x7f0e0017
com.focusflow.debug:string/abc_searchview_description_search = 0x7f0e0015
com.focusflow.debug:styleable/Spinner = 0x7f10002e
com.focusflow.debug:string/indeterminate = 0x7f0e0039
com.focusflow.debug:string/abc_menu_space_shortcut_label = 0x7f0e000f
com.focusflow.debug:string/abc_menu_shift_shortcut_label = 0x7f0e000e
com.focusflow.debug:string/abc_menu_meta_shortcut_label = 0x7f0e000d
com.focusflow.debug:string/abc_menu_enter_shortcut_label = 0x7f0e000b
com.focusflow.debug:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0109
com.focusflow.debug:string/abc_menu_delete_shortcut_label = 0x7f0e000a
com.focusflow.debug:string/abc_menu_ctrl_shortcut_label = 0x7f0e0009
com.focusflow.debug:string/abc_menu_alt_shortcut_label = 0x7f0e0008
com.focusflow.debug:string/abc_capital_on = 0x7f0e0007
com.focusflow.debug:string/abc_capital_off = 0x7f0e0006
com.focusflow.debug:styleable/AppCompatTheme = 0x7f100010
com.focusflow.debug:string/abc_activity_chooser_view_see_all = 0x7f0e0004
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f0032
com.focusflow.debug:string/abc_action_menu_overflow_description = 0x7f0e0002
com.focusflow.debug:string/abc_action_bar_up_description = 0x7f0e0001
com.focusflow.debug:style/TextAppearance.Compat.Notification.Title = 0x7f0f00f3
com.focusflow.debug:string/abc_action_bar_home_description = 0x7f0e0000
com.focusflow.debug:mipmap/ic_launcher = 0x7f0d0000
com.focusflow.debug:layout/notification_template_part_time = 0x7f0c0025
com.focusflow.debug:layout/notification_action = 0x7f0c0020
com.focusflow.debug:layout/ime_secondary_split_test_activity = 0x7f0c001f
com.focusflow.debug:layout/custom_dialog = 0x7f0c001c
com.focusflow.debug:mipmap/ic_launcher_round = 0x7f0d0001
com.focusflow.debug:layout/abc_search_view = 0x7f0c0019
com.focusflow.debug:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
com.focusflow.debug:layout/abc_screen_toolbar = 0x7f0c0017
com.focusflow.debug:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
com.focusflow.debug:layout/abc_screen_content_include = 0x7f0c0014
com.focusflow.debug:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0f007d
com.focusflow.debug:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
com.focusflow.debug:style/TextAppearance.AppCompat.Button = 0x7f0f00c2
com.focusflow.debug:layout/abc_list_menu_item_checkbox = 0x7f0c000e
com.focusflow.debug:layout/abc_dialog_title_material = 0x7f0c000c
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Large = 0x7f0f0017
com.focusflow.debug:layout/abc_alert_dialog_title_material = 0x7f0c000a
com.focusflow.debug:layout/abc_alert_dialog_material = 0x7f0c0009
com.focusflow.debug:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
com.focusflow.debug:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0f013f
com.focusflow.debug:layout/abc_activity_chooser_view = 0x7f0c0006
com.focusflow.debug:layout/abc_action_mode_close_item_material = 0x7f0c0005
com.focusflow.debug:layout/abc_action_mode_bar = 0x7f0c0004
com.focusflow.debug:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f00d3
com.focusflow.debug:layout/abc_action_menu_layout = 0x7f0c0003
com.focusflow.debug:string/notification_channel_description = 0x7f0e003c
com.focusflow.debug:interpolator/fast_out_slow_in = 0x7f0b0006
com.focusflow.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
com.focusflow.debug:styleable/MenuItem = 0x7f100021
com.focusflow.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
com.focusflow.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
com.focusflow.debug:string/call_notification_answer_action = 0x7f0e001d
com.focusflow.debug:integer/cancel_button_image_alpha = 0x7f0a0002
com.focusflow.debug:integer/abc_config_activityDefaultDur = 0x7f0a0000
com.focusflow.debug:style/Base.Widget.AppCompat.TextView = 0x7f0f009d
com.focusflow.debug:id/wrapped_composition_tag = 0x7f0900c1
com.focusflow.debug:id/wrap_content = 0x7f0900c0
com.focusflow.debug:style/ThemeOverlay.AppCompat = 0x7f0f010f
com.focusflow.debug:id/withText = 0x7f0900bf
com.focusflow.debug:id/visible_removing_fragment_view_tag = 0x7f0900be
com.focusflow.debug:id/view_tree_saved_state_registry_owner = 0x7f0900bc
com.focusflow.debug:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0900bb
com.focusflow.debug:id/useLogo = 0x7f0900b9
com.focusflow.debug:id/up = 0x7f0900b8
com.focusflow.debug:id/uniform = 0x7f0900b7
com.focusflow.debug:id/unchecked = 0x7f0900b6
com.focusflow.debug:id/topPanel = 0x7f0900b5
com.focusflow.debug:id/top = 0x7f0900b4
com.focusflow.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f003a
com.focusflow.debug:layout/abc_popup_menu_item_layout = 0x7f0c0013
com.focusflow.debug:id/title_template = 0x7f0900b3
com.focusflow.debug:id/titleDividerNoCustom = 0x7f0900b2
com.focusflow.debug:string/abc_searchview_description_clear = 0x7f0e0013
com.focusflow.debug:id/time = 0x7f0900b0
com.focusflow.debug:id/textSpacerNoTitle = 0x7f0900af
com.focusflow.debug:layout/notification_template_part_chronometer = 0x7f0c0024
com.focusflow.debug:id/text = 0x7f0900ac
com.focusflow.debug:id/tag_unhandled_key_listeners = 0x7f0900aa
com.focusflow.debug:id/tag_unhandled_key_event_manager = 0x7f0900a9
com.focusflow.debug:id/tag_transition_group = 0x7f0900a8
com.focusflow.debug:id/tag_screen_reader_focusable = 0x7f0900a6
com.focusflow.debug:string/generic_error_no_keyguard = 0x7f0e0035
com.focusflow.debug:id/tag_accessibility_heading = 0x7f0900a1
com.focusflow.debug:id/tag_accessibility_actions = 0x7f09009f
com.focusflow.debug:styleable/CheckedTextView = 0x7f100013
com.focusflow.debug:id/tabMode = 0x7f09009e
com.focusflow.debug:id/src_in = 0x7f09009a
com.focusflow.debug:id/special_effects_controller_view_tag = 0x7f090097
com.focusflow.debug:id/spacer = 0x7f090096
com.focusflow.debug:id/tag_state_description = 0x7f0900a7
com.focusflow.debug:id/showTitle = 0x7f090095
com.focusflow.debug:id/showHome = 0x7f090094
com.focusflow.debug:id/shortcut = 0x7f090092
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0f00bc
com.focusflow.debug:id/select_dialog_listview = 0x7f090091
com.focusflow.debug:id/search_voice_btn = 0x7f090090
com.focusflow.debug:styleable/StateListDrawable = 0x7f10002f
com.focusflow.debug:style/Platform.Widget.AppCompat.Spinner = 0x7f0f00ad
com.focusflow.debug:id/search_plate = 0x7f09008e
com.focusflow.debug:id/search_mag_icon = 0x7f09008d
com.focusflow.debug:id/search_bar = 0x7f090088
com.focusflow.debug:string/call_notification_ongoing_text = 0x7f0e0022
com.focusflow.debug:id/scrollView = 0x7f090086
com.focusflow.debug:id/scrollIndicatorUp = 0x7f090085
com.focusflow.debug:id/scrollIndicatorDown = 0x7f090084
com.focusflow.debug:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0f00fc
com.focusflow.debug:style/Platform.V25.AppCompat = 0x7f0f00ab
com.focusflow.debug:id/screen = 0x7f090083
com.focusflow.debug:id/report_drawn = 0x7f090080
com.focusflow.debug:id/right_side = 0x7f090082
com.focusflow.debug:id/radio = 0x7f09007f
com.focusflow.debug:id/progress_circular = 0x7f09007d
com.focusflow.debug:style/TextAppearance.AppCompat.Body1 = 0x7f0f00c0
com.focusflow.debug:id/pooling_container_listener_holder_tag = 0x7f09007c
com.focusflow.debug:style/TextAppearance.AppCompat = 0x7f0f00bf
com.focusflow.debug:id/on = 0x7f09007a
com.focusflow.debug:id/off = 0x7f090079
com.focusflow.debug:id/notification_main_column_container = 0x7f090078
com.focusflow.debug:id/notification_background = 0x7f090076
com.focusflow.debug:id/never = 0x7f090073
com.focusflow.debug:id/nav_controller_view_tag = 0x7f090072
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f00e9
com.focusflow.debug:id/list_item = 0x7f09006e
com.focusflow.debug:id/line1 = 0x7f09006b
com.focusflow.debug:id/italic = 0x7f09006a
com.focusflow.debug:style/Widget.AppCompat.ListView = 0x7f0f014a
com.focusflow.debug:id/is_pooling_container_tag = 0x7f090069
com.focusflow.debug:id/inspection_slot_table_set = 0x7f090068
com.focusflow.debug:styleable/View = 0x7f100034
com.focusflow.debug:id/icon_group = 0x7f090064
com.focusflow.debug:id/group_divider = 0x7f09005e
com.focusflow.debug:id/forever = 0x7f09005c
com.focusflow.debug:id/fingerprint_subtitle = 0x7f09005b
com.focusflow.debug:id/fingerprint_description = 0x7f090058
com.focusflow.debug:id/expanded_menu = 0x7f090057
com.focusflow.debug:id/expand_activities_button = 0x7f090056
com.focusflow.debug:id/edit_query = 0x7f090053
com.focusflow.debug:id/dialog_button = 0x7f090051
com.focusflow.debug:id/default_activity_button = 0x7f090050
com.focusflow.debug:id/customPanel = 0x7f09004e
com.focusflow.debug:id/custom = 0x7f09004d
com.focusflow.debug:xml/backup_rules = 0x7f110000
com.focusflow.debug:id/textSpacerNoButtons = 0x7f0900ae
com.focusflow.debug:id/contentPanel = 0x7f09004c
com.focusflow.debug:id/content = 0x7f09004b
com.focusflow.debug:id/consume_window_insets_tag = 0x7f09004a
com.focusflow.debug:id/compose_view_saveable_id_tag = 0x7f090049
com.focusflow.debug:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0039
com.focusflow.debug:id/coil_request_manager = 0x7f090047
com.focusflow.debug:id/checked = 0x7f090045
com.focusflow.debug:id/checkbox = 0x7f090044
com.focusflow.debug:id/buttonPanel = 0x7f090042
com.focusflow.debug:id/bottom = 0x7f090041
com.focusflow.debug:style/Base.Theme.AppCompat.CompactMenu = 0x7f0f003d
com.focusflow.debug:layout/abc_select_dialog_material = 0x7f0c001a
com.focusflow.debug:id/blocking = 0x7f090040
com.focusflow.debug:id/async = 0x7f09003e
com.focusflow.debug:id/androidx_compose_ui_view_composition_context = 0x7f09003d
com.focusflow.debug:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0f011a
com.focusflow.debug:id/alertTitle = 0x7f09003b
com.focusflow.debug:id/activity_chooser_view_content = 0x7f090039
com.focusflow.debug:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
com.focusflow.debug:id/decor_content_parent = 0x7f09004f
com.focusflow.debug:id/action_text = 0x7f090037
com.focusflow.debug:id/action_mode_close_button = 0x7f090036
com.focusflow.debug:id/action_mode_bar_stub = 0x7f090035
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0f00b3
com.focusflow.debug:id/action_divider = 0x7f090030
com.focusflow.debug:id/action_container = 0x7f09002e
com.focusflow.debug:id/action_bar_title = 0x7f09002d
com.focusflow.debug:id/action_bar_subtitle = 0x7f09002c
com.focusflow.debug:id/action_bar_spinner = 0x7f09002b
com.focusflow.debug:id/action_bar_root = 0x7f09002a
com.focusflow.debug:id/accessibility_custom_action_8 = 0x7f090025
com.focusflow.debug:id/accessibility_custom_action_5 = 0x7f090022
com.focusflow.debug:id/tag_on_receive_content_mime_types = 0x7f0900a5
com.focusflow.debug:attr/goIcon = 0x7f040095
com.focusflow.debug:id/accessibility_custom_action_30 = 0x7f09001f
com.focusflow.debug:id/accessibility_custom_action_3 = 0x7f09001e
com.focusflow.debug:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0f0156
com.focusflow.debug:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0f011e
com.focusflow.debug:id/accessibility_custom_action_25 = 0x7f090019
com.focusflow.debug:id/ifRoom = 0x7f090065
com.focusflow.debug:id/accessibility_custom_action_22 = 0x7f090016
com.focusflow.debug:id/accessibility_custom_action_18 = 0x7f090011
com.focusflow.debug:integer/config_tooltipAnimTime = 0x7f0a0003
com.focusflow.debug:drawable/abc_list_focused_holo = 0x7f080026
com.focusflow.debug:color/white = 0x7f06008b
com.focusflow.debug:id/accessibility_custom_action_16 = 0x7f09000f
com.focusflow.debug:id/accessibility_custom_action_15 = 0x7f09000e
com.focusflow.debug:id/accessibility_custom_action_14 = 0x7f09000d
com.focusflow.debug:id/accessibility_custom_action_13 = 0x7f09000c
com.focusflow.debug:style/Widget.AppCompat.Spinner.DropDown = 0x7f0f015a
com.focusflow.debug:string/dropdown_menu = 0x7f0e002c
com.focusflow.debug:id/accessibility_custom_action_12 = 0x7f09000b
com.focusflow.debug:id/accessibility_custom_action_11 = 0x7f09000a
com.focusflow.debug:id/accessibility_custom_action_1 = 0x7f090008
com.focusflow.debug:id/FUNCTION = 0x7f090002
com.focusflow.debug:drawable/tooltip_frame_dark = 0x7f080071
com.focusflow.debug:drawable/notify_panel_notification_icon_bg = 0x7f08006f
com.focusflow.debug:drawable/notification_template_icon_low_bg = 0x7f08006d
com.focusflow.debug:attr/windowMinWidthMajor = 0x7f040134
com.focusflow.debug:attr/navigationIcon = 0x7f0400c1
com.focusflow.debug:drawable/notification_template_icon_bg = 0x7f08006c
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0f0027
com.focusflow.debug:animator/fragment_open_enter = 0x7f020004
com.focusflow.debug:drawable/notification_icon_background = 0x7f08006a
com.focusflow.debug:drawable/notification_bg_normal = 0x7f080068
com.focusflow.debug:drawable/notification_bg_low_pressed = 0x7f080067
com.focusflow.debug:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0f013c
com.focusflow.debug:id/multiply = 0x7f090071
com.focusflow.debug:drawable/notification_bg_low = 0x7f080065
com.focusflow.debug:drawable/ic_notification = 0x7f080062
com.focusflow.debug:id/accessibility_custom_action_23 = 0x7f090017
com.focusflow.debug:drawable/ic_launcher_foreground = 0x7f080061
com.focusflow.debug:string/abc_prepend_shortcut_label = 0x7f0e0011
com.focusflow.debug:drawable/ic_call_decline = 0x7f08005f
com.focusflow.debug:drawable/ic_call_answer_low = 0x7f08005c
com.focusflow.debug:drawable/ic_call_answer = 0x7f08005b
com.focusflow.debug:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0f000b
com.focusflow.debug:dimen/abc_text_size_small_material = 0x7f07004c
com.focusflow.debug:drawable/fingerprint_dialog_fp_icon = 0x7f08005a
com.focusflow.debug:attr/paddingEnd = 0x7f0400c8
com.focusflow.debug:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f080058
com.focusflow.debug:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f08000b
com.focusflow.debug:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080052
com.focusflow.debug:drawable/btn_checkbox_checked_mtrl = 0x7f080051
com.focusflow.debug:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
com.focusflow.debug:drawable/baseline_arrow_drop_up_24 = 0x7f080050
com.focusflow.debug:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f08004b
com.focusflow.debug:drawable/abc_list_divider_mtrl_alpha = 0x7f080025
com.focusflow.debug:drawable/abc_textfield_default_mtrl_alpha = 0x7f08004a
com.focusflow.debug:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080049
com.focusflow.debug:drawable/abc_text_select_handle_left_mtrl = 0x7f080046
com.focusflow.debug:drawable/abc_tab_indicator_material = 0x7f080043
com.focusflow.debug:string/abc_action_mode_done = 0x7f0e0003
com.focusflow.debug:drawable/abc_switch_track_mtrl_alpha = 0x7f080042
com.focusflow.debug:drawable/abc_spinner_textfield_background_material = 0x7f08003e
com.focusflow.debug:attr/actionModePopupWindowStyle = 0x7f040019
com.focusflow.debug:drawable/abc_seekbar_track_material = 0x7f08003c
com.focusflow.debug:string/abc_menu_function_shortcut_label = 0x7f0e000c
com.focusflow.debug:drawable/abc_text_select_handle_middle_mtrl = 0x7f080047
com.focusflow.debug:drawable/abc_star_black_48dp = 0x7f08003f
com.focusflow.debug:styleable/FragmentContainerView = 0x7f10001a
com.focusflow.debug:style/Base.Widget.AppCompat.SearchView = 0x7f0f0097
com.focusflow.debug:drawable/abc_seekbar_thumb_material = 0x7f08003a
com.focusflow.debug:xml/network_security_config = 0x7f110003
com.focusflow.debug:drawable/abc_scrubber_track_mtrl_alpha = 0x7f080039
com.focusflow.debug:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f080038
com.focusflow.debug:style/Widget.AppCompat.ActionBar.Solid = 0x7f0f0119
com.focusflow.debug:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f080037
com.focusflow.debug:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f080036
com.focusflow.debug:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f080035
com.focusflow.debug:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0049
com.focusflow.debug:id/accessibility_custom_action_26 = 0x7f09001a
com.focusflow.debug:style/Animation.AppCompat.DropDownUp = 0x7f0f0003
com.focusflow.debug:animator/fragment_fade_exit = 0x7f020003
com.focusflow.debug:drawable/abc_ratingbar_small_material = 0x7f080034
com.focusflow.debug:attr/autoSizeTextType = 0x7f040034
com.focusflow.debug:drawable/abc_list_selector_holo_light = 0x7f08002f
com.focusflow.debug:drawable/abc_spinner_mtrl_am_alpha = 0x7f08003d
com.focusflow.debug:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.focusflow.debug:drawable/abc_list_selector_holo_dark = 0x7f08002e
com.focusflow.debug:id/accessibility_custom_action_21 = 0x7f090015
com.focusflow.debug:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0074
com.focusflow.debug:dimen/abc_text_size_caption_material = 0x7f070042
com.focusflow.debug:drawable/abc_list_selector_disabled_holo_dark = 0x7f08002c
com.focusflow.debug:string/fingerprint_error_no_fingerprints = 0x7f0e0031
com.focusflow.debug:drawable/abc_list_pressed_holo_light = 0x7f080029
com.focusflow.debug:style/Base.V22.Theme.AppCompat.Light = 0x7f0f0057
com.focusflow.debug:drawable/notification_bg_normal_pressed = 0x7f080069
com.focusflow.debug:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0f008d
com.focusflow.debug:attr/popUpToInclusive = 0x7f0400d1
com.focusflow.debug:drawable/abc_item_background_holo_light = 0x7f080023
com.focusflow.debug:dimen/touch_target_min = 0x7f070092
com.focusflow.debug:drawable/abc_item_background_holo_dark = 0x7f080022
com.focusflow.debug:color/background_floating_material_dark = 0x7f060022
com.focusflow.debug:drawable/abc_ic_voice_search_api_material = 0x7f080021
com.focusflow.debug:id/text2 = 0x7f0900ad
com.focusflow.debug:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f08001d
com.focusflow.debug:attr/actionModeTheme = 0x7f04001e
com.focusflow.debug:attr/listChoiceBackgroundIndicator = 0x7f0400a9
com.focusflow.debug:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f08001a
com.focusflow.debug:attr/thumbTint = 0x7f040110
com.focusflow.debug:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080018
com.focusflow.debug:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0093
com.focusflow.debug:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f080016
com.focusflow.debug:color/abc_secondary_text_material_dark = 0x7f060011
com.focusflow.debug:drawable/abc_ic_ab_back_material = 0x7f080015
com.focusflow.debug:color/biometric_error_color = 0x7f060026
com.focusflow.debug:drawable/abc_edit_text_material = 0x7f080014
com.focusflow.debug:xml/data_extraction_rules = 0x7f110001
com.focusflow.debug:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0f00cc
com.focusflow.debug:drawable/abc_dialog_material_background = 0x7f080013
com.focusflow.debug:attr/autoSizeStepGranularity = 0x7f040033
com.focusflow.debug:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080011
com.focusflow.debug:drawable/abc_cab_background_top_material = 0x7f080010
com.focusflow.debug:id/tag_window_insets_animation_callback = 0x7f0900ab
com.focusflow.debug:drawable/ic_call_answer_video_low = 0x7f08005e
com.focusflow.debug:drawable/notification_tile_bg = 0x7f08006e
com.focusflow.debug:string/status_bar_notification_info_overflow = 0x7f0e0044
com.focusflow.debug:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f08001f
com.focusflow.debug:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f08000e
com.focusflow.debug:drawable/abc_btn_radio_material = 0x7f080009
com.focusflow.debug:drawable/notification_action_background = 0x7f080063
com.focusflow.debug:attr/panelMenuListWidth = 0x7f0400cd
com.focusflow.debug:drawable/abc_btn_colored_material = 0x7f080007
com.focusflow.debug:drawable/abc_btn_check_material_anim = 0x7f080004
com.focusflow.debug:drawable/abc_btn_check_material = 0x7f080003
com.focusflow.debug:drawable/abc_ic_clear_material = 0x7f080017
com.focusflow.debug:drawable/abc_btn_borderless_material = 0x7f080002
com.focusflow.debug:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0f012e
com.focusflow.debug:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0f0114
com.focusflow.debug:id/none = 0x7f090074
com.focusflow.debug:drawable/abc_action_bar_item_background_material = 0x7f080001
com.focusflow.debug:style/Widget.Compat.NotificationActionContainer = 0x7f0f0161
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Headline = 0x7f0f0015
com.focusflow.debug:dimen/text_size_title_xl = 0x7f070087
com.focusflow.debug:style/Theme.FocusFlow = 0x7f0f010d
com.focusflow.debug:color/material_grey_100 = 0x7f060061
com.focusflow.debug:dimen/text_size_title_large = 0x7f070086
com.focusflow.debug:color/abc_secondary_text_material_light = 0x7f060012
com.focusflow.debug:color/dim_foreground_disabled_material_light = 0x7f060039
com.focusflow.debug:dimen/text_size_small = 0x7f070084
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f002a
com.focusflow.debug:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.focusflow.debug:dimen/text_size_headline = 0x7f070081
com.focusflow.debug:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0f00d1
com.focusflow.debug:dimen/text_size_body_xl = 0x7f07007d
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0f00b2
com.focusflow.debug:id/end = 0x7f090055
com.focusflow.debug:dimen/text_size_body_large = 0x7f07007c
com.focusflow.debug:dimen/text_size_body = 0x7f07007b
com.focusflow.debug:attr/queryHint = 0x7f0400da
com.focusflow.debug:dimen/spacing_sm = 0x7f070078
com.focusflow.debug:dimen/spacing_lg = 0x7f070076
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f00ee
com.focusflow.debug:layout/abc_list_menu_item_icon = 0x7f0c000f
com.focusflow.debug:attr/titleMarginTop = 0x7f04011c
com.focusflow.debug:dimen/abc_star_big = 0x7f07003b
com.focusflow.debug:dimen/notification_top_pad_large_text = 0x7f070075
com.focusflow.debug:dimen/notification_small_icon_background_padding = 0x7f070071
com.focusflow.debug:id/accessibility_custom_action_17 = 0x7f090010
com.focusflow.debug:dimen/focus_corner_radius = 0x7f07005f
com.focusflow.debug:dimen/notification_right_icon_size = 0x7f07006f
com.focusflow.debug:attr/alertDialogButtonGroupStyle = 0x7f040025
com.focusflow.debug:dimen/tooltip_vertical_padding = 0x7f07008d
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f0036
com.focusflow.debug:dimen/notification_media_narrow_margin = 0x7f07006e
com.focusflow.debug:layout/abc_action_bar_title_item = 0x7f0c0000
com.focusflow.debug:dimen/notification_main_column_padding_top = 0x7f07006d
com.focusflow.debug:dimen/notification_large_icon_width = 0x7f07006c
com.focusflow.debug:attr/actionModeCutDrawable = 0x7f040016
com.focusflow.debug:id/SYM = 0x7f090005
com.focusflow.debug:dimen/notification_large_icon_height = 0x7f07006b
com.focusflow.debug:dimen/notification_content_margin_start = 0x7f07006a
com.focusflow.debug:attr/contentInsetEnd = 0x7f04005f
com.focusflow.debug:dimen/notification_action_text_size = 0x7f070068
com.focusflow.debug:dimen/notification_action_icon_size = 0x7f070067
com.focusflow.debug:dimen/highlight_alpha_material_light = 0x7f070062
com.focusflow.debug:dimen/highlight_alpha_material_dark = 0x7f070061
com.focusflow.debug:id/homeAsUp = 0x7f090062
com.focusflow.debug:dimen/notification_subtext_size = 0x7f070073
com.focusflow.debug:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0048
com.focusflow.debug:dimen/focus_border_width = 0x7f07005e
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f002c
com.focusflow.debug:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f08001e
com.focusflow.debug:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0f00d4
com.focusflow.debug:attr/dropdownListPreferredItemHeight = 0x7f04007e
com.focusflow.debug:dimen/fingerprint_icon_size = 0x7f07005d
com.focusflow.debug:dimen/disabled_alpha_material_light = 0x7f07005c
com.focusflow.debug:dimen/disabled_alpha_material_dark = 0x7f07005b
com.focusflow.debug:style/Widget.AppCompat.Spinner = 0x7f0f0159
com.focusflow.debug:id/action_context_bar = 0x7f09002f
com.focusflow.debug:dimen/hint_pressed_alpha_material_light = 0x7f070066
com.focusflow.debug:dimen/compat_notification_large_icon_max_width = 0x7f07005a
com.focusflow.debug:integer/abc_config_activityShortDur = 0x7f0a0001
com.focusflow.debug:dimen/abc_text_size_large_material = 0x7f070048
com.focusflow.debug:dimen/compat_notification_large_icon_max_height = 0x7f070059
com.focusflow.debug:style/TextAppearance.AppCompat.Inverse = 0x7f0f00c9
com.focusflow.debug:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0f0045
com.focusflow.debug:dimen/compat_control_corner_material = 0x7f070058
com.focusflow.debug:attr/actionModeCloseContentDescription = 0x7f040013
com.focusflow.debug:dimen/compat_button_padding_vertical_material = 0x7f070057
com.focusflow.debug:string/fingerprint_error_hw_not_available = 0x7f0e002e
com.focusflow.debug:color/high_contrast_text = 0x7f060058
com.focusflow.debug:dimen/compat_button_inset_horizontal_material = 0x7f070054
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0135
com.focusflow.debug:dimen/card_elevation = 0x7f070053
com.focusflow.debug:dimen/card_corner_radius = 0x7f070052
com.focusflow.debug:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0f004e
com.focusflow.debug:dimen/abc_text_size_title_material_toolbar = 0x7f070050
com.focusflow.debug:color/adhd_success_green = 0x7f06001e
com.focusflow.debug:attr/maxButtonHeight = 0x7f0400ba
com.focusflow.debug:dimen/abc_text_size_title_material = 0x7f07004f
com.focusflow.debug:style/Base.Theme.AppCompat.Dialog = 0x7f0f003e
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0f0016
com.focusflow.debug:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004e
com.focusflow.debug:id/icon = 0x7f090063
com.focusflow.debug:dimen/abc_text_size_subhead_material = 0x7f07004d
com.focusflow.debug:attr/arrowHeadLength = 0x7f04002d
com.focusflow.debug:dimen/abc_text_size_menu_header_material = 0x7f07004a
com.focusflow.debug:dimen/abc_text_size_medium_material = 0x7f070049
com.focusflow.debug:dimen/abc_text_size_display_1_material = 0x7f070043
com.focusflow.debug:dimen/text_size_title = 0x7f070085
com.focusflow.debug:dimen/abc_text_size_button_material = 0x7f070041
com.focusflow.debug:dimen/abc_text_size_body_2_material = 0x7f070040
com.focusflow.debug:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0126
com.focusflow.debug:style/Theme.AppCompat.Dialog = 0x7f0f0100
com.focusflow.debug:dimen/abc_text_size_body_1_material = 0x7f07003f
com.focusflow.debug:dimen/abc_switch_padding = 0x7f07003e
com.focusflow.debug:attr/listChoiceIndicatorSingleAnimated = 0x7f0400ab
com.focusflow.debug:attr/popEnterAnim = 0x7f0400ce
com.focusflow.debug:dimen/notification_big_circle_margin = 0x7f070069
com.focusflow.debug:dimen/abc_star_small = 0x7f07003d
com.focusflow.debug:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f08000d
com.focusflow.debug:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.focusflow.debug:dimen/abc_progress_bar_height_material = 0x7f070035
com.focusflow.debug:attr/multiChoiceItemLayout = 0x7f0400be
com.focusflow.debug:dimen/abc_panel_menu_list_width = 0x7f070034
com.focusflow.debug:drawable/abc_btn_radio_material_anim = 0x7f08000a
com.focusflow.debug:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.focusflow.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0f00bd
com.focusflow.debug:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0f0082
com.focusflow.debug:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.focusflow.debug:color/highlighted_text_material_light = 0x7f06005a
com.focusflow.debug:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.focusflow.debug:dimen/abc_dialog_padding_top_material = 0x7f070025
com.focusflow.debug:style/ThemeOverlay.AppCompat.DayNight = 0x7f0f0113
com.focusflow.debug:id/middle = 0x7f090070
com.focusflow.debug:dimen/spacing_xs = 0x7f07007a
com.focusflow.debug:dimen/abc_dialog_padding_material = 0x7f070024
com.focusflow.debug:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.focusflow.debug:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.focusflow.debug:attr/state_above_anchor = 0x7f0400f4
com.focusflow.debug:drawable/abc_ic_menu_overflow_material = 0x7f08001c
com.focusflow.debug:style/Widget.AppCompat.Light.SearchView = 0x7f0f0146
com.focusflow.debug:layout/abc_list_menu_item_radio = 0x7f0c0011
com.focusflow.debug:id/fingerprint_error = 0x7f090059
com.focusflow.debug:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.focusflow.debug:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.focusflow.debug:style/Theme.AppCompat.DayNight = 0x7f0f00f9
com.focusflow.debug:id/accessibility_custom_action_10 = 0x7f090009
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f00ec
com.focusflow.debug:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f080006
com.focusflow.debug:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.focusflow.debug:drawable/abc_ic_go_search_api_material = 0x7f080019
com.focusflow.debug:attr/ratingBarStyle = 0x7f0400dd
com.focusflow.debug:dimen/abc_control_padding_material = 0x7f07001a
com.focusflow.debug:id/accessibility_custom_action_20 = 0x7f090014
com.focusflow.debug:dimen/abc_control_corner_material = 0x7f070018
com.focusflow.debug:attr/buttonTint = 0x7f040047
com.focusflow.debug:dimen/abc_control_inset_material = 0x7f070019
com.focusflow.debug:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.focusflow.debug:dimen/abc_button_inset_vertical_material = 0x7f070013
com.focusflow.debug:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.focusflow.debug:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0f007f
com.focusflow.debug:id/disableHome = 0x7f090052
com.focusflow.debug:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.focusflow.debug:styleable/TextAppearance = 0x7f100032
com.focusflow.debug:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.focusflow.debug:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.focusflow.debug:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.focusflow.debug:attr/showAsAction = 0x7f0400e9
com.focusflow.debug:attr/buttonBarPositiveButtonStyle = 0x7f04003f
com.focusflow.debug:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.focusflow.debug:dimen/abc_action_bar_elevation_material = 0x7f070005
com.focusflow.debug:attr/autoSizeMinTextSize = 0x7f040031
com.focusflow.debug:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.focusflow.debug:id/image = 0x7f090066
com.focusflow.debug:drawable/abc_ic_search_api_material = 0x7f080020
com.focusflow.debug:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.focusflow.debug:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.focusflow.debug:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.focusflow.debug:bool/enable_system_alarm_service_default = 0x7f050002
com.focusflow.debug:id/accessibility_custom_action_28 = 0x7f09001c
com.focusflow.debug:layout/notification_action_tombstone = 0x7f0c0021
com.focusflow.debug:color/vector_tint_theme_color = 0x7f06008a
com.focusflow.debug:color/text_secondary = 0x7f060086
com.focusflow.debug:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.focusflow.debug:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0f00a7
com.focusflow.debug:dimen/abc_text_size_display_3_material = 0x7f070045
com.focusflow.debug:attr/progressBarStyle = 0x7f0400d8
com.focusflow.debug:color/switch_thumb_normal_material_light = 0x7f060082
com.focusflow.debug:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0f00cd
com.focusflow.debug:color/switch_thumb_material_light = 0x7f060080
com.focusflow.debug:color/abc_search_url_text_normal = 0x7f06000e
com.focusflow.debug:color/switch_thumb_material_dark = 0x7f06007f
com.focusflow.debug:id/search_go_btn = 0x7f09008c
com.focusflow.debug:dimen/abc_floating_window_z = 0x7f07002f
com.focusflow.debug:style/Base.Widget.AppCompat.SeekBar = 0x7f0f0099
com.focusflow.debug:color/switch_thumb_disabled_material_dark = 0x7f06007d
com.focusflow.debug:drawable/abc_seekbar_tick_mark_material = 0x7f08003b
com.focusflow.debug:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.focusflow.debug:color/surface_light = 0x7f06007c
com.focusflow.debug:id/tag_on_receive_content_listener = 0x7f0900a4
com.focusflow.debug:color/adhd_error_red = 0x7f06001c
com.focusflow.debug:color/secondary_text_disabled_material_dark = 0x7f06007a
com.focusflow.debug:color/secondary_text_default_material_dark = 0x7f060078
com.focusflow.debug:string/welcome_title = 0x7f0e0049
com.focusflow.debug:id/accessibility_custom_action_29 = 0x7f09001d
com.focusflow.debug:color/ripple_material_dark = 0x7f060076
com.focusflow.debug:anim/abc_slide_in_top = 0x7f010007
com.focusflow.debug:attr/windowNoTitle = 0x7f040136
com.focusflow.debug:drawable/abc_control_background_material = 0x7f080012
com.focusflow.debug:style/Widget.AppCompat.ProgressBar = 0x7f0f0150
com.focusflow.debug:drawable/abc_btn_default_mtrl_shape = 0x7f080008
com.focusflow.debug:color/purple_500 = 0x7f060074
com.focusflow.debug:dimen/abc_button_padding_vertical_material = 0x7f070015
com.focusflow.debug:attr/buttonCompat = 0x7f040041
com.focusflow.debug:color/purple_200 = 0x7f060073
com.focusflow.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
com.focusflow.debug:color/primary_text_default_material_dark = 0x7f06006f
com.focusflow.debug:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.focusflow.debug:color/primary_dark_material_dark = 0x7f06006b
com.focusflow.debug:dimen/notification_small_icon_size_as_large = 0x7f070072
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0f000e
com.focusflow.debug:color/on_surface_light = 0x7f06006a
com.focusflow.debug:style/Base.Widget.AppCompat.Button = 0x7f0f0072
com.focusflow.debug:id/normal = 0x7f090075
com.focusflow.debug:color/notification_action_color_filter = 0x7f060068
com.focusflow.debug:id/search_edit_frame = 0x7f09008b
com.focusflow.debug:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.focusflow.debug:color/material_grey_900 = 0x7f060067
com.focusflow.debug:id/SHIFT = 0x7f090004
com.focusflow.debug:color/material_grey_600 = 0x7f060064
com.focusflow.debug:color/material_grey_50 = 0x7f060063
com.focusflow.debug:color/material_grey_300 = 0x7f060062
com.focusflow.debug:drawable/tooltip_frame_light = 0x7f080072
com.focusflow.debug:color/tooltip_background_light = 0x7f060088
com.focusflow.debug:color/material_deep_teal_500 = 0x7f060060
com.focusflow.debug:attr/popUpToSaveState = 0x7f0400d2
com.focusflow.debug:attr/closeIcon = 0x7f04004e
com.focusflow.debug:color/material_blue_grey_950 = 0x7f06005e
com.focusflow.debug:color/material_blue_grey_900 = 0x7f06005d
com.focusflow.debug:color/secondary_text_disabled_material_light = 0x7f06007b
com.focusflow.debug:color/material_blue_grey_800 = 0x7f06005c
com.focusflow.debug:color/ic_launcher_background = 0x7f06005b
com.focusflow.debug:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0f0090
com.focusflow.debug:layout/notification_template_custom_big = 0x7f0c0022
com.focusflow.debug:color/highlighted_text_material_dark = 0x7f060059
com.focusflow.debug:color/high_contrast_dark_surface = 0x7f060054
com.focusflow.debug:color/high_contrast_dark_primary = 0x7f060053
com.focusflow.debug:color/primary_text_disabled_material_light = 0x7f060072
com.focusflow.debug:color/high_contrast_dark_background = 0x7f060052
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0f00e2
com.focusflow.debug:id/center_vertical = 0x7f090043
com.focusflow.debug:id/accessibility_custom_action_7 = 0x7f090024
com.focusflow.debug:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0f0143
com.focusflow.debug:id/collapseActionView = 0x7f090048
com.focusflow.debug:color/material_grey_800 = 0x7f060065
com.focusflow.debug:string/abc_menu_sym_shortcut_label = 0x7f0e0010
com.focusflow.debug:color/gray_700 = 0x7f06004d
com.focusflow.debug:color/gray_600 = 0x7f06004c
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f002b
com.focusflow.debug:drawable/abc_list_divider_material = 0x7f080024
com.focusflow.debug:color/teal_200 = 0x7f060083
com.focusflow.debug:color/gray_50 = 0x7f06004a
com.focusflow.debug:layout/abc_tooltip = 0x7f0c001b
com.focusflow.debug:color/gray_100 = 0x7f060046
com.focusflow.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f00ce
com.focusflow.debug:attr/actionOverflowMenuStyle = 0x7f040021
com.focusflow.debug:color/foreground_material_light = 0x7f060045
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Button = 0x7f0f000f
com.focusflow.debug:color/focus_green_light = 0x7f060042
com.focusflow.debug:color/focus_green = 0x7f060041
com.focusflow.debug:color/focus_blue_dark = 0x7f06003f
com.focusflow.debug:dimen/abc_dialog_min_width_major = 0x7f070022
com.focusflow.debug:color/focus_blue = 0x7f06003e
com.focusflow.debug:color/dim_foreground_material_light = 0x7f06003b
com.focusflow.debug:style/Widget.AppCompat.ListView.Menu = 0x7f0f014c
com.focusflow.debug:string/not_selected = 0x7f0e003b
com.focusflow.debug:color/dim_foreground_material_dark = 0x7f06003a
com.focusflow.debug:style/DialogWindowTheme = 0x7f0f00a1
com.focusflow.debug:dimen/tooltip_y_offset_touch = 0x7f07008f
com.focusflow.debug:color/gray_900 = 0x7f06004f
com.focusflow.debug:color/dark_text_secondary = 0x7f060037
com.focusflow.debug:color/dark_text_primary = 0x7f060036
com.focusflow.debug:color/primary_material_light = 0x7f06006e
com.focusflow.debug:id/message = 0x7f09006f
com.focusflow.debug:color/dark_surface = 0x7f060035
com.focusflow.debug:layout/notification_template_icon_group = 0x7f0c0023
com.focusflow.debug:attr/colorControlHighlight = 0x7f040057
com.focusflow.debug:dimen/touch_target_large = 0x7f070091
com.focusflow.debug:style/Widget.Compat.NotificationActionText = 0x7f0f0162
com.focusflow.debug:id/edit_text_id = 0x7f090054
com.focusflow.debug:attr/contentInsetStartWithNavigation = 0x7f040064
com.focusflow.debug:color/adhd_calm_blue = 0x7f06001b
com.focusflow.debug:color/dark_background_soft = 0x7f060033
com.focusflow.debug:color/abc_search_url_text_pressed = 0x7f06000f
com.focusflow.debug:drawable/abc_star_half_black_48dp = 0x7f080040
com.focusflow.debug:color/call_notification_decline_color = 0x7f060031
com.focusflow.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0086
com.focusflow.debug:color/accent_material_light = 0x7f06001a
com.focusflow.debug:color/button_material_dark = 0x7f06002e
com.focusflow.debug:attr/actionModeStyle = 0x7f04001d
com.focusflow.debug:color/bright_foreground_inverse_material_light = 0x7f06002b
com.focusflow.debug:styleable/NavHost = 0x7f100027
com.focusflow.debug:attr/subtitle = 0x7f0400f7
com.focusflow.debug:color/background_material_light = 0x7f060025
com.focusflow.debug:color/background_material_dark = 0x7f060024
com.focusflow.debug:styleable/NavAction = 0x7f100023
com.focusflow.debug:string/default_notification_message = 0x7f0e0029
com.focusflow.debug:id/search_button = 0x7f090089
com.focusflow.debug:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.focusflow.debug:style/Base.V21.Theme.AppCompat = 0x7f0f0051
com.focusflow.debug:color/background_floating_material_light = 0x7f060023
com.focusflow.debug:color/androidx_core_secondary_text_default_material_light = 0x7f060021
com.focusflow.debug:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.focusflow.debug:color/androidx_core_ripple_material_light = 0x7f060020
com.focusflow.debug:color/abc_primary_text_material_dark = 0x7f06000b
com.focusflow.debug:color/adhd_warning_amber = 0x7f06001f
com.focusflow.debug:styleable/StateListDrawableItem = 0x7f100030
com.focusflow.debug:string/androidx_startup = 0x7f0e001b
com.focusflow.debug:id/ALT = 0x7f090000
com.focusflow.debug:dimen/highlight_alpha_material_colored = 0x7f070060
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f00e8
com.focusflow.debug:color/adhd_focus_green = 0x7f06001d
com.focusflow.debug:drawable/abc_cab_background_internal_bg = 0x7f08000f
com.focusflow.debug:color/abc_tint_default = 0x7f060014
com.focusflow.debug:id/submenuarrow = 0x7f09009c
com.focusflow.debug:color/teal_700 = 0x7f060084
com.focusflow.debug:attr/emojiCompatEnabled = 0x7f040083
com.focusflow.debug:layout/abc_expanded_menu_layout = 0x7f0c000d
com.focusflow.debug:attr/selectableItemBackgroundBorderless = 0x7f0400e7
com.focusflow.debug:id/accessibility_custom_action_0 = 0x7f090007
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Caption = 0x7f0f0010
com.focusflow.debug:attr/progressBarPadding = 0x7f0400d7
com.focusflow.debug:attr/alpha = 0x7f04002a
com.focusflow.debug:layout/support_simple_spinner_dropdown_item = 0x7f0c0029
com.focusflow.debug:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080054
com.focusflow.debug:id/accessibility_custom_action_6 = 0x7f090023
com.focusflow.debug:id/progress_horizontal = 0x7f09007e
com.focusflow.debug:attr/actionBarTheme = 0x7f04000a
com.focusflow.debug:attr/shortcutMatchRequired = 0x7f0400e8
com.focusflow.debug:color/abc_search_url_text = 0x7f06000d
com.focusflow.debug:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080000
com.focusflow.debug:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.focusflow.debug:color/abc_decor_view_status_guard_light = 0x7f060006
com.focusflow.debug:dimen/button_corner_radius = 0x7f070051
com.focusflow.debug:color/bright_foreground_material_light = 0x7f06002d
com.focusflow.debug:color/abc_color_highlight_material = 0x7f060004
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0f00b1
com.focusflow.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0087
com.focusflow.debug:color/purple_700 = 0x7f060075
com.focusflow.debug:string/notification_channel_name = 0x7f0e003d
com.focusflow.debug:attr/tickMarkTint = 0x7f040113
com.focusflow.debug:attr/showTitle = 0x7f0400ec
com.focusflow.debug:color/abc_btn_colored_text_material = 0x7f060003
com.focusflow.debug:array/crypto_fingerprint_fallback_prefixes = 0x7f030001
com.focusflow.debug:drawable/notification_oversize_large_icon_bg = 0x7f08006b
com.focusflow.debug:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.focusflow.debug:style/Base.Widget.AppCompat.Button.Colored = 0x7f0f0076
com.focusflow.debug:attr/textAppearanceListItem = 0x7f040103
com.focusflow.debug:bool/workmanager_test_configuration = 0x7f050005
com.focusflow.debug:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.focusflow.debug:string/fingerprint_not_recognized = 0x7f0e0033
com.focusflow.debug:attr/targetPackage = 0x7f040100
com.focusflow.debug:bool/abc_action_bar_embed_tabs = 0x7f050000
com.focusflow.debug:attr/windowMinWidthMinor = 0x7f040135
com.focusflow.debug:attr/indeterminateProgressStyle = 0x7f0400a0
com.focusflow.debug:style/Widget.AppCompat.ActionBar.TabText = 0x7f0f011b
com.focusflow.debug:attr/spinnerDropDownItemStyle = 0x7f0400ef
com.focusflow.debug:attr/backgroundTint = 0x7f040038
com.focusflow.debug:attr/windowFixedWidthMinor = 0x7f040133
com.focusflow.debug:drawable/abc_ratingbar_indicator_material = 0x7f080032
com.focusflow.debug:attr/actionButtonStyle = 0x7f04000c
com.focusflow.debug:attr/alphabeticModifiers = 0x7f04002b
com.focusflow.debug:attr/windowFixedWidthMajor = 0x7f040132
com.focusflow.debug:id/hide_in_inspector_tag = 0x7f090060
com.focusflow.debug:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f08001b
com.focusflow.debug:attr/alertDialogTheme = 0x7f040028
com.focusflow.debug:attr/windowFixedHeightMajor = 0x7f040130
com.focusflow.debug:id/action_menu_presenter = 0x7f090033
com.focusflow.debug:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.focusflow.debug:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0f0096
com.focusflow.debug:dimen/abc_dialog_min_width_minor = 0x7f070023
com.focusflow.debug:attr/imageButtonStyle = 0x7f04009f
com.focusflow.debug:attr/windowActionBarOverlay = 0x7f04012e
com.focusflow.debug:attr/voiceIcon = 0x7f04012c
com.focusflow.debug:id/title = 0x7f0900b1
com.focusflow.debug:id/src_atop = 0x7f090099
com.focusflow.debug:drawable/abc_list_longpressed_holo = 0x7f080027
com.focusflow.debug:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.focusflow.debug:style/TextAppearance.AppCompat.Display1 = 0x7f0f00c4
com.focusflow.debug:dimen/abc_list_item_height_material = 0x7f070031
com.focusflow.debug:attr/viewInflaterClass = 0x7f04012b
com.focusflow.debug:attr/titleTextStyle = 0x7f040120
com.focusflow.debug:dimen/text_size_headline_xl = 0x7f070083
com.focusflow.debug:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0f006a
com.focusflow.debug:attr/elevation = 0x7f040082
com.focusflow.debug:dimen/abc_text_size_menu_material = 0x7f07004b
com.focusflow.debug:attr/titleMarginBottom = 0x7f040119
com.focusflow.debug:dimen/notification_right_side_padding_top = 0x7f070070
com.focusflow.debug:attr/tintMode = 0x7f040116
com.focusflow.debug:attr/actionModeCopyDrawable = 0x7f040015
com.focusflow.debug:attr/tickMark = 0x7f040112
com.focusflow.debug:id/home = 0x7f090061
com.focusflow.debug:attr/dividerHorizontal = 0x7f040070
com.focusflow.debug:color/dark_on_surface = 0x7f060034
com.focusflow.debug:style/Widget.AppCompat.SeekBar = 0x7f0f0157
com.focusflow.debug:attr/tooltipForegroundColor = 0x7f040123
com.focusflow.debug:attr/startDestination = 0x7f0400f3
com.focusflow.debug:attr/color = 0x7f040052
com.focusflow.debug:attr/font = 0x7f040088
com.focusflow.debug:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
com.focusflow.debug:color/gray_500 = 0x7f06004b
com.focusflow.debug:attr/ratingBarStyleSmall = 0x7f0400df
com.focusflow.debug:attr/thumbTextPadding = 0x7f04010f
com.focusflow.debug:dimen/abc_text_size_headline_material = 0x7f070047
com.focusflow.debug:attr/thickness = 0x7f04010e
com.focusflow.debug:dimen/notification_top_pad = 0x7f070074
com.focusflow.debug:attr/textColorAlertDialogListItem = 0x7f04010a
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0f0011
com.focusflow.debug:color/abc_search_url_text_selected = 0x7f060010
com.focusflow.debug:attr/submitBackground = 0x7f0400f6
com.focusflow.debug:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f001f
com.focusflow.debug:layout/select_dialog_item_material = 0x7f0c0026
com.focusflow.debug:dimen/tooltip_horizontal_padding = 0x7f070089
com.focusflow.debug:attr/textAppearanceSmallPopupMenu = 0x7f040109
com.focusflow.debug:string/fingerprint_error_user_canceled = 0x7f0e0032
com.focusflow.debug:id/action_bar_activity_content = 0x7f090028
com.focusflow.debug:attr/textAppearanceSearchResultSubtitle = 0x7f040107
com.focusflow.debug:styleable/ViewStubCompat = 0x7f100036
com.focusflow.debug:id/showCustom = 0x7f090093
com.focusflow.debug:attr/textAppearanceListItemSmall = 0x7f040105
com.focusflow.debug:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0f005c
com.focusflow.debug:dimen/tooltip_precise_anchor_threshold = 0x7f07008c
com.focusflow.debug:attr/textAppearanceListItemSecondary = 0x7f040104
com.focusflow.debug:dimen/spacing_md = 0x7f070077
com.focusflow.debug:attr/textAppearanceLargePopupMenu = 0x7f040102
com.focusflow.debug:attr/uri = 0x7f04012a
com.focusflow.debug:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0f0060
com.focusflow.debug:attr/switchStyle = 0x7f0400fe
com.focusflow.debug:styleable/ActionMode = 0x7f100004
com.focusflow.debug:attr/seekBarStyle = 0x7f0400e5
com.focusflow.debug:attr/switchPadding = 0x7f0400fd
com.focusflow.debug:attr/thumbTintMode = 0x7f040111
com.focusflow.debug:attr/switchMinWidth = 0x7f0400fc
com.focusflow.debug:id/accessibility_custom_action_4 = 0x7f090021
com.focusflow.debug:anim/abc_slide_out_bottom = 0x7f010008
com.focusflow.debug:attr/suggestionRowLayout = 0x7f0400fb
com.focusflow.debug:attr/subtitleTextStyle = 0x7f0400fa
com.focusflow.debug:attr/subtitleTextAppearance = 0x7f0400f8
com.focusflow.debug:color/material_deep_teal_200 = 0x7f06005f
com.focusflow.debug:color/ripple_material_light = 0x7f060077
com.focusflow.debug:attr/singleChoiceItemLayout = 0x7f0400ed
com.focusflow.debug:attr/srcCompat = 0x7f0400f2
com.focusflow.debug:style/Platform.AppCompat.Light = 0x7f0f00a5
com.focusflow.debug:attr/commitIcon = 0x7f04005d
com.focusflow.debug:drawable/abc_list_pressed_holo_dark = 0x7f080028
com.focusflow.debug:styleable/LinearLayoutCompat = 0x7f10001d
com.focusflow.debug:attr/textAppearancePopupMenuHeader = 0x7f040106
com.focusflow.debug:attr/splitTrack = 0x7f0400f1
com.focusflow.debug:attr/tooltipFrameBackground = 0x7f040124
com.focusflow.debug:dimen/tooltip_y_offset_non_touch = 0x7f07008e
com.focusflow.debug:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f012b
com.focusflow.debug:attr/titleMarginEnd = 0x7f04011a
com.focusflow.debug:attr/showText = 0x7f0400eb
com.focusflow.debug:attr/colorPrimary = 0x7f04005a
com.focusflow.debug:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0f0062
com.focusflow.debug:attr/showDividers = 0x7f0400ea
com.focusflow.debug:style/TextAppearance.AppCompat.Display4 = 0x7f0f00c7
com.focusflow.debug:string/abc_searchview_description_query = 0x7f0e0014
com.focusflow.debug:drawable/abc_list_selector_background_transition_holo_light = 0x7f08002b
com.focusflow.debug:id/accessibility_custom_action_19 = 0x7f090012
com.focusflow.debug:attr/subMenuArrow = 0x7f0400f5
com.focusflow.debug:dimen/compat_button_inset_vertical_material = 0x7f070055
com.focusflow.debug:attr/searchViewStyle = 0x7f0400e4
com.focusflow.debug:attr/buttonBarNegativeButtonStyle = 0x7f04003d
com.focusflow.debug:attr/fontVariationSettings = 0x7f040092
com.focusflow.debug:color/abc_tint_seek_thumb = 0x7f060016
com.focusflow.debug:styleable/SearchView = 0x7f10002d
com.focusflow.debug:drawable/fingerprint_dialog_error = 0x7f080059
com.focusflow.debug:attr/searchIcon = 0x7f0400e3
com.focusflow.debug:attr/route = 0x7f0400e1
com.focusflow.debug:attr/colorBackgroundFloating = 0x7f040054
com.focusflow.debug:attr/fontProviderPackage = 0x7f04008e
com.focusflow.debug:color/abc_tint_spinner = 0x7f060017
com.focusflow.debug:color/primary_text_default_material_light = 0x7f060070
com.focusflow.debug:attr/preserveIconSpacing = 0x7f0400d6
com.focusflow.debug:attr/restoreState = 0x7f0400e0
com.focusflow.debug:styleable/ListPopupWindow = 0x7f10001f
com.focusflow.debug:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f00f4
com.focusflow.debug:attr/navGraph = 0x7f0400bf
com.focusflow.debug:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f08004c
com.focusflow.debug:dimen/abc_config_prefDialogWidth = 0x7f070017
com.focusflow.debug:attr/ratingBarStyleIndicator = 0x7f0400de
com.focusflow.debug:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0f0070
com.focusflow.debug:attr/radioButtonStyle = 0x7f0400dc
com.focusflow.debug:color/switch_thumb_disabled_material_light = 0x7f06007e
com.focusflow.debug:attr/titleMargins = 0x7f04011d
com.focusflow.debug:attr/queryPatterns = 0x7f0400db
com.focusflow.debug:string/confirm_device_credential_password = 0x7f0e0026
com.focusflow.debug:drawable/abc_tab_indicator_mtrl_alpha = 0x7f080044
com.focusflow.debug:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.focusflow.debug:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.focusflow.debug:attr/drawableRightCompat = 0x7f040076
com.focusflow.debug:color/abc_hint_foreground_material_light = 0x7f060008
com.focusflow.debug:attr/windowActionModeOverlay = 0x7f04012f
com.focusflow.debug:color/high_contrast_surface = 0x7f060057
com.focusflow.debug:drawable/abc_switch_thumb_material = 0x7f080041
com.focusflow.debug:bool/enable_system_job_service_default = 0x7f050004
com.focusflow.debug:attr/alertDialogStyle = 0x7f040027
com.focusflow.debug:attr/listDividerAlertDialog = 0x7f0400ac
com.focusflow.debug:attr/popupTheme = 0x7f0400d4
com.focusflow.debug:attr/popUpTo = 0x7f0400d0
com.focusflow.debug:id/accessibility_custom_action_2 = 0x7f090013
com.focusflow.debug:attr/popExitAnim = 0x7f0400cf
com.focusflow.debug:attr/textAppearanceSearchResultTitle = 0x7f040108
com.focusflow.debug:color/vector_tint_color = 0x7f060089
com.focusflow.debug:color/switch_thumb_normal_material_dark = 0x7f060081
com.focusflow.debug:string/abc_search_hint = 0x7f0e0012
com.focusflow.debug:color/gray_800 = 0x7f06004e
com.focusflow.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0084
com.focusflow.debug:attr/panelMenuListTheme = 0x7f0400cc
com.focusflow.debug:style/Platform.V21.AppCompat = 0x7f0f00a9
com.focusflow.debug:id/action_bar = 0x7f090027
com.focusflow.debug:attr/colorControlActivated = 0x7f040056
com.focusflow.debug:layout/select_dialog_singlechoice_material = 0x7f0c0028
com.focusflow.debug:attr/queryBackground = 0x7f0400d9
com.focusflow.debug:attr/actionDropDownStyle = 0x7f04000d
com.focusflow.debug:style/TextAppearance.AppCompat.Tooltip = 0x7f0f00db
com.focusflow.debug:attr/trackTint = 0x7f040127
com.focusflow.debug:attr/paddingTopNoTitle = 0x7f0400ca
com.focusflow.debug:color/error_color_material_dark = 0x7f06003c
com.focusflow.debug:string/selected = 0x7f0e0043
com.focusflow.debug:dimen/tooltip_precise_anchor_extra_offset = 0x7f07008b
com.focusflow.debug:attr/paddingStart = 0x7f0400c9
com.focusflow.debug:id/CTRL = 0x7f090001
com.focusflow.debug:id/search_src_text = 0x7f09008f
com.focusflow.debug:attr/numericModifiers = 0x7f0400c5
com.focusflow.debug:dimen/spacing_xl = 0x7f070079
com.focusflow.debug:layout/abc_list_menu_item_layout = 0x7f0c0010
com.focusflow.debug:attr/titleTextColor = 0x7f04011f
com.focusflow.debug:attr/listPopupWindowStyle = 0x7f0400b0
com.focusflow.debug:attr/hideOnContentScroll = 0x7f040098
com.focusflow.debug:style/Theme.AppCompat.Dialog.Alert = 0x7f0f0101
com.focusflow.debug:drawable/abc_ratingbar_material = 0x7f080033
com.focusflow.debug:attr/defaultQueryHint = 0x7f040069
com.focusflow.debug:attr/nullable = 0x7f0400c4
com.focusflow.debug:drawable/abc_textfield_search_material = 0x7f08004d
com.focusflow.debug:color/gray_400 = 0x7f060049
com.focusflow.debug:styleable/MenuGroup = 0x7f100020
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0f00b5
com.focusflow.debug:attr/textAllCaps = 0x7f040101
com.focusflow.debug:string/search_menu_title = 0x7f0e0042
com.focusflow.debug:attr/navigationMode = 0x7f0400c2
com.focusflow.debug:dimen/text_size_headline_large = 0x7f070082
com.focusflow.debug:style/Theme.FocusFlow.NoActionBar = 0x7f0f010e
com.focusflow.debug:attr/listLayout = 0x7f0400ae
com.focusflow.debug:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.focusflow.debug:string/welcome_subtitle = 0x7f0e0048
com.focusflow.debug:color/text_primary = 0x7f060085
com.focusflow.debug:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0f003f
com.focusflow.debug:attr/popupMenuStyle = 0x7f0400d3
com.focusflow.debug:attr/autoSizePresetSizes = 0x7f040032
com.focusflow.debug:attr/navigationContentDescription = 0x7f0400c0
com.focusflow.debug:drawable/abc_vector_test = 0x7f08004e
com.focusflow.debug:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0f008b
com.focusflow.debug:string/abc_activitychooserview_choose_application = 0x7f0e0005
com.focusflow.debug:attr/menu = 0x7f0400bc
com.focusflow.debug:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0083
com.focusflow.debug:color/dim_foreground_disabled_material_dark = 0x7f060038
com.focusflow.debug:attr/allowStacking = 0x7f040029
com.focusflow.debug:color/black = 0x7f060027
com.focusflow.debug:attr/actionProviderClass = 0x7f040022
com.focusflow.debug:attr/measureWithLargestChild = 0x7f0400bb
com.focusflow.debug:color/focus_orange = 0x7f060043
com.focusflow.debug:attr/listPreferredItemPaddingStart = 0x7f0400b7
com.focusflow.debug:color/abc_tint_switch_track = 0x7f060018
com.focusflow.debug:layout/ime_base_split_test_activity = 0x7f0c001e
com.focusflow.debug:attr/homeLayout = 0x7f04009a
com.focusflow.debug:attr/listPreferredItemPaddingEnd = 0x7f0400b4
com.focusflow.debug:style/Theme.AppCompat.Light.NoActionBar = 0x7f0f010b
com.focusflow.debug:dimen/text_size_caption_large = 0x7f07007f
com.focusflow.debug:color/high_contrast_dark_text = 0x7f060055
com.focusflow.debug:attr/listPreferredItemHeightSmall = 0x7f0400b3
com.focusflow.debug:id/tag_on_apply_window_listener = 0x7f0900a3
com.focusflow.debug:attr/checkMarkCompat = 0x7f040049
com.focusflow.debug:attr/listPreferredItemHeight = 0x7f0400b1
com.focusflow.debug:anim/abc_tooltip_exit = 0x7f01000b
com.focusflow.debug:string/in_progress = 0x7f0e0038
com.focusflow.debug:id/src_over = 0x7f09009b
com.focusflow.debug:attr/listItemLayout = 0x7f0400ad
com.focusflow.debug:drawable/abc_list_selector_disabled_holo_light = 0x7f08002d
com.focusflow.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f00f6
com.focusflow.debug:id/accessibility_custom_action_27 = 0x7f09001b
com.focusflow.debug:style/Widget.AppCompat.TextView = 0x7f0f015d
com.focusflow.debug:attr/collapseContentDescription = 0x7f040050
com.focusflow.debug:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.focusflow.debug:attr/layout = 0x7f0400a7
com.focusflow.debug:style/Base.V7.Theme.AppCompat = 0x7f0f005f
com.focusflow.debug:dimen/hint_alpha_material_light = 0x7f070064
com.focusflow.debug:styleable/NavInclude = 0x7f100028
com.focusflow.debug:attr/lastBaselineToBottomHeight = 0x7f0400a5
com.focusflow.debug:attr/isLightTheme = 0x7f0400a2
com.focusflow.debug:attr/titleMarginStart = 0x7f04011b
com.focusflow.debug:dimen/abc_list_item_height_small_material = 0x7f070032
com.focusflow.debug:attr/tickMarkTintMode = 0x7f040114
com.focusflow.debug:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f08000c
com.focusflow.debug:drawable/btn_checkbox_unchecked_mtrl = 0x7f080053
com.focusflow.debug:attr/initialActivityCount = 0x7f0400a1
com.focusflow.debug:attr/backgroundTintMode = 0x7f040039
com.focusflow.debug:string/abc_shareactionprovider_share_with_application = 0x7f0e0019
com.focusflow.debug:attr/buttonBarStyle = 0x7f040040
com.focusflow.debug:style/Base.Widget.AppCompat.Spinner = 0x7f0f009b
com.focusflow.debug:attr/iconifiedByDefault = 0x7f04009e
com.focusflow.debug:attr/contentInsetStart = 0x7f040063
com.focusflow.debug:dimen/abc_star_medium = 0x7f07003c
com.focusflow.debug:attr/actionOverflowButtonStyle = 0x7f040020
com.focusflow.debug:color/abc_tint_edittext = 0x7f060015
com.focusflow.debug:bool/enable_system_foreground_service_default = 0x7f050003
com.focusflow.debug:string/range_start = 0x7f0e0041
com.focusflow.debug:drawable/abc_text_cursor_material = 0x7f080045
com.focusflow.debug:string/generic_error_no_device_credential = 0x7f0e0034
com.focusflow.debug:attr/buttonBarButtonStyle = 0x7f04003c
com.focusflow.debug:attr/iconTint = 0x7f04009c
com.focusflow.debug:id/action_mode_bar = 0x7f090034
com.focusflow.debug:attr/checkMarkTintMode = 0x7f04004b
com.focusflow.debug:attr/fontProviderQuery = 0x7f04008f
com.focusflow.debug:attr/ttcIndex = 0x7f040129
com.focusflow.debug:attr/actionBarItemBackground = 0x7f040002
com.focusflow.debug:attr/toolbarStyle = 0x7f040122
com.focusflow.debug:attr/actionBarSize = 0x7f040004
com.focusflow.debug:anim/abc_fade_out = 0x7f010001
com.focusflow.debug:attr/autoCompleteTextViewStyle = 0x7f04002f
com.focusflow.debug:attr/checkedTextViewStyle = 0x7f04004d
com.focusflow.debug:attr/titleTextAppearance = 0x7f04011e
com.focusflow.debug:attr/buttonIconDimen = 0x7f040043
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f0030
com.focusflow.debug:drawable/ic_call_decline_low = 0x7f080060
com.focusflow.debug:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f004d
com.focusflow.debug:attr/graph = 0x7f040096
com.focusflow.debug:string/abc_searchview_description_submit = 0x7f0e0016
com.focusflow.debug:attr/closeItemLayout = 0x7f04004f
com.focusflow.debug:id/action_bar_container = 0x7f090029
com.focusflow.debug:style/Base.V26.Theme.AppCompat.Light = 0x7f0f005b
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f0035
com.focusflow.debug:attr/toolbarNavigationButtonStyle = 0x7f040121
com.focusflow.debug:string/generic_error_user_canceled = 0x7f0e0036
com.focusflow.debug:attr/subtitleTextColor = 0x7f0400f9
com.focusflow.debug:attr/fontWeight = 0x7f040093
com.focusflow.debug:attr/spinBars = 0x7f0400ee
com.focusflow.debug:id/action_image = 0x7f090031
com.focusflow.debug:anim/abc_popup_enter = 0x7f010003
com.focusflow.debug:id/hide_ime_id = 0x7f09005f
com.focusflow.debug:dimen/abc_action_button_min_height_material = 0x7f07000d
com.focusflow.debug:color/button_material_light = 0x7f06002f
com.focusflow.debug:attr/textColorSearchUrl = 0x7f04010b
com.focusflow.debug:id/accessibility_custom_action_24 = 0x7f090018
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0028
com.focusflow.debug:attr/fontProviderFetchTimeout = 0x7f04008d
com.focusflow.debug:style/TextAppearance.AppCompat.Headline = 0x7f0f00c8
com.focusflow.debug:attr/listPreferredItemPaddingRight = 0x7f0400b6
com.focusflow.debug:attr/activityChooserViewStyle = 0x7f040024
com.focusflow.debug:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.focusflow.debug:attr/gapBetweenBars = 0x7f040094
com.focusflow.debug:attr/listMenuViewStyle = 0x7f0400af
com.focusflow.debug:attr/fontFamily = 0x7f040089
com.focusflow.debug:style/Widget.AppCompat.SearchView = 0x7f0f0155
com.focusflow.debug:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f080056
com.focusflow.debug:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0f0042
com.focusflow.debug:attr/drawableBottomCompat = 0x7f040073
com.focusflow.debug:attr/editTextStyle = 0x7f040081
com.focusflow.debug:drawable/abc_popup_background_mtrl_mult = 0x7f080031
com.focusflow.debug:attr/editTextBackground = 0x7f04007f
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0138
com.focusflow.debug:id/view_tree_lifecycle_owner = 0x7f0900ba
com.focusflow.debug:attr/drawableTintMode = 0x7f04007a
com.focusflow.debug:color/dark_background = 0x7f060032
com.focusflow.debug:attr/logo = 0x7f0400b8
com.focusflow.debug:styleable/NavDeepLink = 0x7f100025
com.focusflow.debug:id/search_close_btn = 0x7f09008a
com.focusflow.debug:attr/dropDownListViewStyle = 0x7f04007d
com.focusflow.debug:id/tag_accessibility_clickable_spans = 0x7f0900a0
com.focusflow.debug:attr/controlBackground = 0x7f040065
com.focusflow.debug:attr/divider = 0x7f04006f
com.focusflow.debug:attr/itemPadding = 0x7f0400a3
com.focusflow.debug:attr/displayOptions = 0x7f04006e
com.focusflow.debug:attr/iconTintMode = 0x7f04009d
com.focusflow.debug:attr/drawableStartCompat = 0x7f040078
com.focusflow.debug:drawable/abc_list_selector_background_transition_holo_dark = 0x7f08002a
com.focusflow.debug:dimen/compat_button_padding_horizontal_material = 0x7f070056
com.focusflow.debug:style/Base.Widget.AppCompat.ListView = 0x7f0f008c
com.focusflow.debug:attr/actionModeCloseDrawable = 0x7f040014
com.focusflow.debug:attr/drawableSize = 0x7f040077
com.focusflow.debug:styleable/NavArgument = 0x7f100024
com.focusflow.debug:animator/fragment_open_exit = 0x7f020005
com.focusflow.debug:attr/drawableEndCompat = 0x7f040074
com.focusflow.debug:attr/dividerVertical = 0x7f040072
com.focusflow.debug:attr/fontProviderFetchStrategy = 0x7f04008c
com.focusflow.debug:attr/dividerPadding = 0x7f040071
com.focusflow.debug:attr/paddingBottomNoButtons = 0x7f0400c7
com.focusflow.debug:attr/windowFixedHeightMinor = 0x7f040131
com.focusflow.debug:attr/actionBarSplitStyle = 0x7f040005
com.focusflow.debug:attr/drawableTopCompat = 0x7f04007b
com.focusflow.debug:id/fingerprint_icon = 0x7f09005a
com.focusflow.debug:attr/drawableTint = 0x7f040079
com.focusflow.debug:attr/fontProviderAuthority = 0x7f04008a
com.focusflow.debug:array/crypto_fingerprint_fallback_vendors = 0x7f030002
com.focusflow.debug:attr/background = 0x7f040035
com.focusflow.debug:style/Theme.AppCompat.CompactMenu = 0x7f0f00f8
com.focusflow.debug:style/Base.Theme.AppCompat.Light = 0x7f0f0043
com.focusflow.debug:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0f0040
com.focusflow.debug:id/right_icon = 0x7f090081
com.focusflow.debug:attr/data = 0x7f040067
com.focusflow.debug:attr/titleMargin = 0x7f040118
com.focusflow.debug:attr/searchHintIcon = 0x7f0400e2
com.focusflow.debug:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.focusflow.debug:color/high_contrast_dark_accent = 0x7f060051
com.focusflow.debug:attr/dialogTheme = 0x7f04006d
com.focusflow.debug:style/TextAppearance.AppCompat.Body2 = 0x7f0f00c1
com.focusflow.debug:id/beginning = 0x7f09003f
com.focusflow.debug:attr/actionModeShareDrawable = 0x7f04001b
com.focusflow.debug:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.focusflow.debug:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.focusflow.debug:color/foreground_material_dark = 0x7f060044
com.focusflow.debug:attr/dialogPreferredPadding = 0x7f04006c
com.focusflow.debug:styleable/AppCompatSeekBar = 0x7f10000d
com.focusflow.debug:color/notification_icon_bg_color = 0x7f060069
com.focusflow.debug:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0f013e
com.focusflow.debug:attr/destination = 0x7f04006a
com.focusflow.debug:drawable/test_level_drawable = 0x7f080070
com.focusflow.debug:attr/customNavigationLayout = 0x7f040066
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f00dd
com.focusflow.debug:attr/launchSingleTop = 0x7f0400a6
com.focusflow.debug:array/hide_fingerprint_instantly_prefixes = 0x7f030004
com.focusflow.debug:id/split_action_bar = 0x7f090098
com.focusflow.debug:color/primary_dark_material_light = 0x7f06006c
com.focusflow.debug:style/Widget.AppCompat.ActionBar = 0x7f0f0118
com.focusflow.debug:attr/actionViewClass = 0x7f040023
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0f0037
com.focusflow.debug:id/line3 = 0x7f09006c
com.focusflow.debug:attr/expandActivityOverflowButtonDrawable = 0x7f040086
com.focusflow.debug:style/FloatingDialogWindowTheme = 0x7f0f00a3
com.focusflow.debug:attr/colorControlNormal = 0x7f040058
com.focusflow.debug:attr/textLocale = 0x7f04010c
com.focusflow.debug:dimen/abc_action_button_min_width_material = 0x7f07000e
com.focusflow.debug:id/accessibility_action_clickable_span = 0x7f090006
com.focusflow.debug:attr/switchTextAppearance = 0x7f0400ff
com.focusflow.debug:attr/colorButtonNormal = 0x7f040055
com.focusflow.debug:id/META = 0x7f090003
com.focusflow.debug:attr/actionBarTabBarStyle = 0x7f040007
com.focusflow.debug:attr/checkboxStyle = 0x7f04004c
com.focusflow.debug:attr/actionBarDivider = 0x7f040001
com.focusflow.debug:drawable/abc_text_select_handle_right_mtrl = 0x7f080048
com.focusflow.debug:string/close_drawer = 0x7f0e0024
com.focusflow.debug:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.focusflow.debug:attr/listChoiceIndicatorMultipleAnimated = 0x7f0400aa
com.focusflow.debug:color/call_notification_answer_color = 0x7f060030
com.focusflow.debug:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0f00ae
com.focusflow.debug:attr/exitAnim = 0x7f040085
com.focusflow.debug:attr/dataPattern = 0x7f040068
com.focusflow.debug:dimen/abc_action_bar_default_height_material = 0x7f070002
com.focusflow.debug:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0071
com.focusflow.debug:animator/fragment_fade_enter = 0x7f020002
com.focusflow.debug:id/listMode = 0x7f09006d
com.focusflow.debug:attr/fontProviderSystemFontFamily = 0x7f040090
com.focusflow.debug:attr/editTextColor = 0x7f040080
com.focusflow.debug:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0f007e
com.focusflow.debug:id/chronometer = 0x7f090046
com.focusflow.debug:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f080005
com.focusflow.debug:attr/tooltipText = 0x7f040125
com.focusflow.debug:attr/buttonStyleSmall = 0x7f040046
com.focusflow.debug:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0f0140
com.focusflow.debug:layout/abc_action_menu_item_layout = 0x7f0c0002
com.focusflow.debug:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.focusflow.debug:string/call_notification_hang_up_action = 0x7f0e0020
com.focusflow.debug:attr/popupWindowStyle = 0x7f0400d5
com.focusflow.debug:color/primary_text_disabled_material_dark = 0x7f060071
com.focusflow.debug:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.focusflow.debug:drawable/ic_call_answer_video = 0x7f08005d
com.focusflow.debug:attr/buttonPanelSideLayout = 0x7f040044
com.focusflow.debug:style/Base.Widget.AppCompat.ImageButton = 0x7f0f0081
com.focusflow.debug:attr/buttonStyle = 0x7f040045
com.focusflow.debug:id/actions = 0x7f090038
com.focusflow.debug:attr/firstBaselineToTopHeight = 0x7f040087
com.focusflow.debug:attr/barLength = 0x7f04003a
com.focusflow.debug:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.focusflow.debug:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0f009c
com.focusflow.debug:attr/lStar = 0x7f0400a4
com.focusflow.debug:attr/drawerArrowStyle = 0x7f04007c
com.focusflow.debug:style/Base.Theme.AppCompat = 0x7f0f003c
com.focusflow.debug:attr/spinnerStyle = 0x7f0400f0
com.focusflow.debug:styleable/DrawerArrowToggle = 0x7f100016
com.focusflow.debug:attr/buttonGravity = 0x7f040042
com.focusflow.debug:color/gray_300 = 0x7f060048
com.focusflow.debug:style/ThemeOverlay.AppCompat.Dialog = 0x7f0f0115
com.focusflow.debug:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0f0050
com.focusflow.debug:attr/homeAsUpIndicator = 0x7f040099
com.focusflow.debug:string/tab = 0x7f0e0046
com.focusflow.debug:id/notification_main_column = 0x7f090077
com.focusflow.debug:color/material_grey_850 = 0x7f060066
com.focusflow.debug:attr/selectableItemBackground = 0x7f0400e6
com.focusflow.debug:attr/actionBarWidgetTheme = 0x7f04000b
com.focusflow.debug:attr/collapseIcon = 0x7f040051
com.focusflow.debug:attr/colorSwitchThumbNormal = 0x7f04005c
com.focusflow.debug:attr/overlapAnchor = 0x7f0400c6
com.focusflow.debug:attr/colorError = 0x7f040059
com.focusflow.debug:attr/checkMarkTint = 0x7f04004a
com.focusflow.debug:style/Widget.AppCompat.Light.ActionButton = 0x7f0f013b
com.focusflow.debug:attr/backgroundSplit = 0x7f040036
com.focusflow.debug:drawable/btn_radio_off_mtrl = 0x7f080055
com.focusflow.debug:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.focusflow.debug:attr/windowActionBar = 0x7f04012d
com.focusflow.debug:string/call_notification_incoming_text = 0x7f0e0021
com.focusflow.debug:id/accessibility_custom_action_31 = 0x7f090020
com.focusflow.debug:layout/abc_screen_simple = 0x7f0c0015
com.focusflow.debug:attr/actionModeFindDrawable = 0x7f040017
com.focusflow.debug:layout/select_dialog_multichoice_material = 0x7f0c0027
com.focusflow.debug:layout/fingerprint_dialog_layout = 0x7f0c001d
com.focusflow.debug:dimen/text_size_caption_xl = 0x7f070080
com.focusflow.debug:color/primary_material_dark = 0x7f06006d
com.focusflow.debug:attr/autoSizeMaxTextSize = 0x7f040030
com.focusflow.debug:anim/abc_slide_in_bottom = 0x7f010006
com.focusflow.debug:color/focus_blue_light = 0x7f060040
com.focusflow.debug:layout/abc_action_bar_up_container = 0x7f0c0001
com.focusflow.debug:attr/buttonTintMode = 0x7f040048
com.focusflow.debug:attr/mimeType = 0x7f0400bd
com.focusflow.debug:attr/arrowShaftLength = 0x7f04002e
com.focusflow.debug:attr/enterAnim = 0x7f040084
com.focusflow.debug:attr/listPreferredItemHeightLarge = 0x7f0400b2
com.focusflow.debug:id/action_menu_divider = 0x7f090032
com.focusflow.debug:dimen/abc_list_item_height_large_material = 0x7f070030
com.focusflow.debug:dimen/tooltip_margin = 0x7f07008a
com.focusflow.debug:dimen/touch_target_comfortable = 0x7f070090
com.focusflow.debug:attr/actionMenuTextColor = 0x7f040010
com.focusflow.debug:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0f0110
com.focusflow.debug:style/Base.Animation.AppCompat.Tooltip = 0x7f0f0009
com.focusflow.debug:attr/argType = 0x7f04002c
com.focusflow.debug:string/fingerprint_error_lockout = 0x7f0e0030
com.focusflow.debug:dimen/hint_pressed_alpha_material_dark = 0x7f070065
com.focusflow.debug:dimen/hint_alpha_material_dark = 0x7f070063
com.focusflow.debug:id/view_tree_view_model_store_owner = 0x7f0900bd
com.focusflow.debug:attr/fontProviderCerts = 0x7f04008b
com.focusflow.debug:id/accessibility_custom_action_9 = 0x7f090026
com.focusflow.debug:color/bright_foreground_disabled_material_dark = 0x7f060028
com.focusflow.debug:attr/fontStyle = 0x7f040091
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0f00ed
com.focusflow.debug:style/FloatingDialogTheme = 0x7f0f00a2
com.focusflow.debug:attr/actionModeWebSearchDrawable = 0x7f04001f
com.focusflow.debug:drawable/btn_radio_on_mtrl = 0x7f080057
com.focusflow.debug:styleable/SwitchCompat = 0x7f100031
com.focusflow.debug:color/bright_foreground_inverse_material_dark = 0x7f06002a
com.focusflow.debug:attr/tint = 0x7f040115
com.focusflow.debug:id/tag_accessibility_pane_title = 0x7f0900a2
com.focusflow.debug:attr/actionModeSplitBackground = 0x7f04001c
com.focusflow.debug:attr/dialogCornerRadius = 0x7f04006b
com.focusflow.debug:array/assume_strong_biometrics_models = 0x7f030000
com.focusflow.debug:attr/borderlessButtonStyle = 0x7f04003b
com.focusflow.debug:attr/trackTintMode = 0x7f040128
com.focusflow.debug:styleable/AnimatedStateListDrawableItem = 0x7f100009
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0f00bb
com.focusflow.debug:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.focusflow.debug:attr/actionBarStyle = 0x7f040006
com.focusflow.debug:dimen/abc_text_size_display_2_material = 0x7f070044
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Medium = 0x7f0f001b
com.focusflow.debug:attr/actionModePasteDrawable = 0x7f040018
com.focusflow.debug:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0f0054
com.focusflow.debug:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.focusflow.debug:style/Base.V28.Theme.AppCompat.Light = 0x7f0f005e
com.focusflow.debug:dimen/text_size_caption = 0x7f07007e
com.focusflow.debug:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0089
com.focusflow.debug:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0f0012
com.focusflow.debug:array/delay_showing_prompt_models = 0x7f030003
com.focusflow.debug:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.focusflow.debug:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.focusflow.debug:color/gray_200 = 0x7f060047
com.focusflow.debug:string/app_name = 0x7f0e001c
com.focusflow.debug:attr/contentInsetLeft = 0x7f040061
com.focusflow.debug:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0f00cb
com.focusflow.debug:string/default_notification_title = 0x7f0e002a
com.focusflow.debug:color/abc_hint_foreground_material_dark = 0x7f060007
com.focusflow.debug:attr/lineHeight = 0x7f0400a8
com.focusflow.debug:attr/actionModeCloseButtonStyle = 0x7f040012
com.focusflow.debug:id/parentPanel = 0x7f09007b
com.focusflow.debug:id/always = 0x7f09003c
com.focusflow.debug:attr/title = 0x7f040117
com.focusflow.debug:attr/actionModeBackground = 0x7f040011
com.focusflow.debug:style/ThemeOverlay.AppCompat.Dark = 0x7f0f0111
com.focusflow.debug:attr/backgroundStacked = 0x7f040037
com.focusflow.debug:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0046
com.focusflow.debug:dimen/abc_text_size_display_4_material = 0x7f070046
com.focusflow.debug:attr/actionMenuTextAppearance = 0x7f04000f
com.focusflow.debug:color/bright_foreground_disabled_material_light = 0x7f060029
com.focusflow.debug:bool/abc_config_actionMenuItemAllCaps = 0x7f050001
com.focusflow.debug:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f0112
com.focusflow.debug:attr/actionLayout = 0x7f04000e
com.focusflow.debug:dimen/abc_search_view_preferred_width = 0x7f070037
com.focusflow.debug:anim/fragment_fast_out_extra_slow_in = 0x7f010018
com.focusflow.debug:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0f0095
com.focusflow.debug:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080030
com.focusflow.debug:attr/actionModeSelectAllDrawable = 0x7f04001a
com.focusflow.debug:dimen/tooltip_corner_radius = 0x7f070088
com.focusflow.debug:color/abc_primary_text_material_light = 0x7f06000c
com.focusflow.debug:id/submit_area = 0x7f09009d
com.focusflow.debug:attr/contentInsetEndWithActions = 0x7f040060
com.focusflow.debug:attr/actionBarTabStyle = 0x7f040008
com.focusflow.debug:attr/alertDialogCenterButtons = 0x7f040026
com.focusflow.debug:attr/colorPrimaryDark = 0x7f04005b
com.focusflow.debug:style/AlertDialog.AppCompat.Light = 0x7f0f0001
com.focusflow.debug:attr/action = 0x7f040000
com.focusflow.debug:drawable/baseline_arrow_drop_down_24 = 0x7f08004f
com.focusflow.debug:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0133
com.focusflow.debug:id/search_badge = 0x7f090087
com.focusflow.debug:attr/actionBarPopupTheme = 0x7f040003
com.focusflow.debug:color/abc_tint_btn_checkable = 0x7f060013
com.focusflow.debug:drawable/notification_bg_low_normal = 0x7f080066
com.focusflow.debug:style/Animation.AppCompat.Dialog = 0x7f0f0002
com.focusflow.debug:attr/track = 0x7f040126
com.focusflow.debug:attr/icon = 0x7f04009b
com.focusflow.debug:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0f004c
com.focusflow.debug:attr/theme = 0x7f04010d
com.focusflow.debug:attr/nestedScrollViewStyle = 0x7f0400c3
com.focusflow.debug:attr/colorAccent = 0x7f040053
com.focusflow.debug:style/Base.Widget.AppCompat.ActionMode = 0x7f0f006f
com.focusflow.debug:animator/fragment_close_exit = 0x7f020001
com.focusflow.debug:dimen/abc_search_view_preferred_height = 0x7f070036
com.focusflow.debug:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0108
com.focusflow.debug:dimen/abc_dialog_title_divider_material = 0x7f070026
com.focusflow.debug:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.focusflow.debug:anim/abc_slide_out_top = 0x7f010009
com.focusflow.debug:color/high_contrast_accent = 0x7f060050
com.focusflow.debug:style/Base.V7.Theme.AppCompat.Light = 0x7f0f0061
com.focusflow.debug:animator/fragment_close_enter = 0x7f020000
com.focusflow.debug:attr/height = 0x7f040097
com.focusflow.debug:layout/abc_cascading_menu_item_layout = 0x7f0c000b
com.focusflow.debug:color/error_color_material_light = 0x7f06003d
com.focusflow.debug:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0f009a
com.focusflow.debug:attr/buttonBarNeutralButtonStyle = 0x7f04003e
com.focusflow.debug:color/accent_material_dark = 0x7f060019
com.focusflow.debug:color/tooltip_background_dark = 0x7f060087
com.focusflow.debug:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.focusflow.debug:attr/listPreferredItemPaddingLeft = 0x7f0400b5
com.focusflow.debug:attr/drawableLeftCompat = 0x7f040075
com.focusflow.debug:anim/abc_tooltip_enter = 0x7f01000a
com.focusflow.debug:id/fragment_container_view_tag = 0x7f09005d
com.focusflow.debug:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.focusflow.debug:color/high_contrast_primary = 0x7f060056
com.focusflow.debug:attr/logoDescription = 0x7f0400b9
com.focusflow.debug:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.focusflow.debug:styleable/GradientColor = 0x7f10001b
com.focusflow.debug:color/abc_decor_view_status_guard = 0x7f060005
com.focusflow.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0f00b9
com.focusflow.debug:attr/actionBarTabTextStyle = 0x7f040009
com.focusflow.debug:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f00e7
com.focusflow.debug:id/add = 0x7f09003a
com.focusflow.debug:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.focusflow.debug:attr/panelBackground = 0x7f0400cb
com.focusflow.debug:drawable/notification_bg = 0x7f080064
com.focusflow.debug:color/secondary_text_default_material_light = 0x7f060079
com.focusflow.debug:attr/contentInsetRight = 0x7f040062
com.focusflow.debug:attr/contentDescription = 0x7f04005e
com.focusflow.debug:id/info = 0x7f090067
com.focusflow.debug:color/bright_foreground_material_dark = 0x7f06002c
com.focusflow.debug:anim/abc_fade_in = 0x7f010000
com.focusflow.debug:integer/status_bar_notification_info_maxnum = 0x7f0a0004
com.focusflow.debug:anim/abc_popup_exit = 0x7f010004
