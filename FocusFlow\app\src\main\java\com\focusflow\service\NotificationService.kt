package com.focusflow.service

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.focusflow.MainActivity
import com.focusflow.R
import com.focusflow.data.model.WishlistItem
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationService @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        const val CHANNEL_ID = "focus_flow_notifications"
        const val NOTIFICATION_ID = 1
        
        fun createNotificationChannel(context: Context) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val name = "FocusFlow Notifications"
                val descriptionText = "Notifications for tasks, reminders and focus sessions"
                val importance = NotificationManager.IMPORTANCE_DEFAULT
                val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                    description = descriptionText
                }
                val notificationManager: NotificationManager =
                    context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.createNotificationChannel(channel)
            }
        }
    }

    init {
        createNotificationChannel(context)
    }

    fun showNotification(title: String, message: String) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)

        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, builder.build())
    }

    fun sendDelayEndNotification(item: WishlistItem) {
        val title = "Delay Period Ended"
        val message = "Your ${item.itemName} delay period has ended. Still want to buy it?"
        showNotification(title, message)
    }

    fun scheduleDelayEndNotification(itemId: Long, itemName: String, price: Double, triggerTime: Long) {
        // In a real implementation, this would schedule a notification using AlarmManager
        // For now, we'll just log it
        println("Scheduled notification for item $itemName ($$price) at $triggerTime")
    }

    fun cancelDelayNotification(itemId: Long) {
        // In a real implementation, this would cancel the scheduled notification
        println("Cancelled notification for item $itemId")
    }
}
