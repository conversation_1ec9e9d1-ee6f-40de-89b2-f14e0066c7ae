package com.focusflow.utils;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\u0006\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u000e\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\u0006J\u000e\u0010\t\u001a\u00020\u00042\u0006\u0010\n\u001a\u00020\u0006J\u000e\u0010\u000b\u001a\u00020\u00062\u0006\u0010\f\u001a\u00020\u0006J\u0010\u0010\r\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0005\u001a\u00020\u000eJ\u0010\u0010\u000f\u001a\u0004\u0018\u00010\u00062\u0006\u0010\u0005\u001a\u00020\u000e\u00a8\u0006\u0010"}, d2 = {"Lcom/focusflow/utils/ValidationUtils;", "", "()V", "isValidAmount", "", "amount", "", "isValidCreditCardNumber", "cardNumber", "isValidEmail", "email", "sanitizeInput", "input", "validateBudgetAmount", "", "validateExpenseAmount", "app_debug"})
public final class ValidationUtils {
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.utils.ValidationUtils INSTANCE = null;
    
    private ValidationUtils() {
        super();
    }
    
    public final boolean isValidAmount(@org.jetbrains.annotations.NotNull
    java.lang.String amount) {
        return false;
    }
    
    public final boolean isValidEmail(@org.jetbrains.annotations.NotNull
    java.lang.String email) {
        return false;
    }
    
    public final boolean isValidCreditCardNumber(@org.jetbrains.annotations.NotNull
    java.lang.String cardNumber) {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String sanitizeInput(@org.jetbrains.annotations.NotNull
    java.lang.String input) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String validateBudgetAmount(double amount) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String validateExpenseAmount(double amount) {
        return null;
    }
}