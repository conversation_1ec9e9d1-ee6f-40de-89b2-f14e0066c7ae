package com.focusflow.service

import com.focusflow.data.dao.AchievementDao
import com.focusflow.data.dao.UserStatsDao
import com.focusflow.data.dao.VirtualPetDao
import com.focusflow.data.model.Achievement
import com.focusflow.data.model.UserStats
import com.focusflow.data.model.VirtualPet
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GamificationService @Inject constructor(
    private val achievementDao: AchievementDao,
    private val userStatsDao: UserStatsDao,
    private val virtualPetDao: VirtualPetDao
) {
    
    suspend fun initializeGamification() {
        // Initialize user stats if not exists
        val userStats = userStatsDao.getUserStatsSync()
        if (userStats == null) {
            userStatsDao.insertUserStats(UserStats())
        }
        
        // Initialize virtual pet if not exists
        val virtualPet = virtualPetDao.getVirtualPetSync()
        if (virtualPet == null) {
            virtualPetDao.insertVirtualPet(VirtualPet())
        }
        
        // Initialize achievements if not exists
        initializeAchievements()
    }
    
    private suspend fun initializeAchievements() {
        val achievements = listOf(
            Achievement(
                type = "expense_logging",
                title = "First Step",
                description = "Log your first expense",
                iconEmoji = "🎯",
                pointsAwarded = 10,
                targetValue = 1
            ),
            Achievement(
                type = "expense_logging",
                title = "Getting Started",
                description = "Log 5 expenses",
                iconEmoji = "📝",
                pointsAwarded = 25,
                targetValue = 5
            ),
            Achievement(
                type = "expense_logging",
                title = "Tracking Master",
                description = "Log 25 expenses",
                iconEmoji = "📊",
                pointsAwarded = 100,
                targetValue = 25
            ),
            Achievement(
                type = "streak",
                title = "Consistency Champion",
                description = "Log expenses for 7 days in a row",
                iconEmoji = "🔥",
                pointsAwarded = 50,
                targetValue = 7
            ),
            Achievement(
                type = "streak",
                title = "Habit Hero",
                description = "Log expenses for 30 days in a row",
                iconEmoji = "⭐",
                pointsAwarded = 200,
                targetValue = 30
            ),
            Achievement(
                type = "budget_adherence",
                title = "Budget Buddy",
                description = "Stay within budget for a week",
                iconEmoji = "💰",
                pointsAwarded = 75,
                targetValue = 1
            ),
            Achievement(
                type = "debt_payment",
                title = "Debt Destroyer",
                description = "Make your first extra debt payment",
                iconEmoji = "💪",
                pointsAwarded = 100,
                targetValue = 1
            ),
            Achievement(
                type = "milestone",
                title = "Level Up!",
                description = "Reach level 5",
                iconEmoji = "🚀",
                pointsAwarded = 150,
                targetValue = 5
            )
        )
        
        achievements.forEach { achievement ->
            achievementDao.insertAchievement(achievement)
        }
    }
    
    suspend fun onExpenseLogged() {
        // Update stats
        userStatsDao.incrementExpensesLogged()
        val stats = userStatsDao.getUserStatsSync() ?: return
        
        // Update streak - simplified approach
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val today = now.date
        val lastActivity = stats.lastActivityDate?.date

        val newStreak = when {
            lastActivity == null -> 1 // First time logging
            lastActivity == today -> stats.expenseLoggingStreak // Same day, don't increment
            lastActivity.dayOfYear == today.dayOfYear - 1 && lastActivity.year == today.year -> {
                stats.expenseLoggingStreak + 1 // Yesterday, continue streak
            }
            lastActivity.dayOfYear == 365 && today.dayOfYear == 1 && today.year == lastActivity.year + 1 -> {
                stats.expenseLoggingStreak + 1 // New Year's Day continuation
            }
            else -> 1 // Reset streak
        }
        
        userStatsDao.updateExpenseLoggingStreak(newStreak)

        // Update last activity date
        val updatedStats = stats.copy(lastActivityDate = now)
        userStatsDao.updateUserStats(updatedStats)
        
        // Check achievements
        checkExpenseLoggingAchievements(stats.totalExpensesLogged + 1)
        checkStreakAchievements(newStreak)
        
        // Feed virtual pet
        feedVirtualPet(5) // 5 experience points for logging expense
        
        // Award points
        awardPoints(5)
    }
    
    suspend fun onBudgetAdherence() {
        val stats = userStatsDao.getUserStatsSync() ?: return
        userStatsDao.updateBudgetAdherenceStreak(stats.budgetAdherenceStreak + 1)
        
        checkBudgetAchievements(stats.budgetAdherenceStreak + 1)
        feedVirtualPet(10)
        awardPoints(10)
    }
    
    suspend fun onDebtPayment(amount: Double) {
        userStatsDao.addDebtPaid(amount)
        
        checkDebtAchievements()
        feedVirtualPet(15)
        awardPoints(20)
    }
    
    private suspend fun checkExpenseLoggingAchievements(totalLogged: Int) {
        val milestones = listOf(1, 5, 25, 50, 100)
        milestones.forEach { milestone ->
            if (totalLogged >= milestone) {
                // Check if achievement exists and unlock it
                // This is simplified - in real implementation, you'd query for specific achievements
            }
        }
    }
    
    private suspend fun checkStreakAchievements(streak: Int) {
        val streakMilestones = listOf(7, 30, 60, 100)
        streakMilestones.forEach { milestone ->
            if (streak >= milestone) {
                // Unlock streak achievement
            }
        }
    }
    
    private suspend fun checkBudgetAchievements(streak: Int) {
        if (streak >= 1) {
            // Unlock budget adherence achievement
        }
    }
    
    private suspend fun checkDebtAchievements() {
        // Unlock debt payment achievement
    }
    
    private suspend fun awardPoints(points: Int) {
        userStatsDao.addPoints(points)
        
        // Check for level up
        val stats = userStatsDao.getUserStatsSync() ?: return
        val newLevel = calculateLevel(stats.totalPoints + points)
        
        if (newLevel > stats.currentLevel) {
            userStatsDao.updateUserStats(stats.copy(currentLevel = newLevel))
            // Trigger level up celebration
        }
    }
    
    private suspend fun feedVirtualPet(experience: Int) {
        val pet = virtualPetDao.getVirtualPetSync() ?: return
        
        val newExperience = pet.experience + experience
        val newLevel = calculatePetLevel(newExperience)
        val newHappiness = minOf(100, pet.happiness + 5)
        
        virtualPetDao.updateVirtualPet(
            pet.copy(
                experience = newExperience,
                level = newLevel,
                happiness = newHappiness,
                lastFed = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            )
        )
    }
    
    private fun calculateLevel(totalPoints: Int): Int {
        return when {
            totalPoints < 100 -> 1
            totalPoints < 300 -> 2
            totalPoints < 600 -> 3
            totalPoints < 1000 -> 4
            totalPoints < 1500 -> 5
            totalPoints < 2100 -> 6
            totalPoints < 2800 -> 7
            totalPoints < 3600 -> 8
            totalPoints < 4500 -> 9
            else -> 10
        }
    }
    
    private fun calculatePetLevel(experience: Int): Int {
        return (experience / 100) + 1
    }
    
    suspend fun getMotivationalMessage(): String {
        val stats = userStatsDao.getUserStatsSync()
        val pet = virtualPetDao.getVirtualPetSync()

        return when {
            stats?.expenseLoggingStreak ?: 0 >= 7 -> "🔥 Amazing! You're on a ${stats?.expenseLoggingStreak}-day streak!"
            stats?.totalPoints ?: 0 >= 500 -> "⭐ You're doing fantastic! ${stats?.totalPoints} points earned!"
            pet?.happiness ?: 0 >= 90 -> "😸 ${pet?.name} is super happy with your progress!"
            else -> "💪 Keep going! Every small step counts!"
        }
    }

    // Phase 3: Enhanced gamification features
    suspend fun onFocusSessionCompleted(durationMinutes: Int, qualityRating: Int) {
        val basePoints = durationMinutes / 5 // 1 point per 5 minutes
        val qualityBonus = qualityRating * 2 // Bonus based on quality rating
        val totalPoints = basePoints + qualityBonus

        awardPoints(totalPoints)
        feedVirtualPet(totalPoints)

        // Check for focus-related achievements
        checkFocusAchievements(durationMinutes)
    }

    suspend fun onDelaySuccessful(delayHours: Int, amountSaved: Double) {
        val delayPoints = when {
            delayHours >= 168 -> 50 // 1 week delay
            delayHours >= 48 -> 30  // 2+ days delay
            delayHours >= 24 -> 20  // 1+ day delay
            delayHours >= 1 -> 10   // 1+ hour delay
            else -> 5
        }

        val savingsBonus = (amountSaved / 10).toInt().coerceAtMost(25) // Max 25 bonus points
        val totalPoints = delayPoints + savingsBonus

        awardPoints(totalPoints)
        feedVirtualPet(totalPoints)

        // Check for impulse control achievements
        checkDelayAchievements()
    }

    suspend fun onBudgetGoalAchieved(categoryName: String, savingsAmount: Double) {
        val basePoints = 25
        val savingsBonus = (savingsAmount / 20).toInt().coerceAtMost(50)
        val totalPoints = basePoints + savingsBonus

        awardPoints(totalPoints)
        feedVirtualPet(totalPoints)

        // Award special achievement
        unlockAchievement("budget_master_$categoryName")
    }

    suspend fun onSpendingPatternBroken(patternName: String, streakDays: Int) {
        val basePoints = 30
        val streakBonus = streakDays * 5
        val totalPoints = basePoints + streakBonus

        awardPoints(totalPoints)
        feedVirtualPet(totalPoints)

        unlockAchievement("pattern_breaker")
    }

    private suspend fun checkFocusAchievements(durationMinutes: Int) {
        when {
            durationMinutes >= 120 -> unlockAchievement("deep_focus_master") // 2+ hours
            durationMinutes >= 60 -> unlockAchievement("focus_champion") // 1+ hour
            durationMinutes >= 25 -> unlockAchievement("pomodoro_warrior") // Standard pomodoro
        }
    }

    private suspend fun checkDelayAchievements() {
        // This would check delay statistics and unlock appropriate achievements
        unlockAchievement("impulse_control_novice")
    }

    private suspend fun unlockAchievement(achievementId: String) {
        // Check if achievement already exists
        val existingAchievements = achievementDao.getAllAchievements()
        // For now, just create the achievement - in a real implementation you'd check for duplicates

        // Create and unlock the achievement
        val achievement = Achievement(
            type = "focus",
            title = achievementId.replace("_", " ").capitalize(),
            description = getAchievementDescription(achievementId),
            iconEmoji = "🏆",
            pointsAwarded = 50,
            isUnlocked = true,
            unlockedAt = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        )
        achievementDao.insertAchievement(achievement)

        // Award bonus points for achievement
        awardPoints(50)
    }

    private fun getAchievementDescription(achievementId: String): String {
        return when (achievementId) {
            "deep_focus_master" -> "Completed a 2+ hour focus session"
            "focus_champion" -> "Completed a 1+ hour focus session"
            "pomodoro_warrior" -> "Completed a standard 25-minute pomodoro"
            "impulse_control_novice" -> "Successfully delayed a purchase"
            "pattern_breaker" -> "Broke a negative spending pattern"
            else -> "Achievement unlocked!"
        }
    }
}
