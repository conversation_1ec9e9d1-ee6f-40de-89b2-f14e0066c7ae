package com.focusflow.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\b\n\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\u00a8\u0006\u000b"}, d2 = {"Purple200", "Landroidx/compose/ui/graphics/Color;", "getPurple200", "()J", "J", "Purple500", "getPurple500", "Purple700", "getPurple700", "Teal200", "getTeal200", "app_debug"})
public final class ColorKt {
    private static final long Purple200 = 0L;
    private static final long Purple500 = 0L;
    private static final long Purple700 = 0L;
    private static final long Teal200 = 0L;
    
    public static final long getPurple200() {
        return 0L;
    }
    
    public static final long getPurple500() {
        return 0L;
    }
    
    public static final long getPurple700() {
        return 0L;
    }
    
    public static final long getTeal200() {
        return 0L;
    }
}