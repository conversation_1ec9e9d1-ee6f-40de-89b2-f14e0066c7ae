package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.FocusSession
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface FocusSessionDao {
    
    @Query("SELECT * FROM focus_sessions ORDER BY startTime DESC")
    fun getAllFocusSessions(): Flow<List<FocusSession>>
    
    @Query("SELECT * FROM focus_sessions WHERE taskType = :taskType ORDER BY startTime DESC")
    fun getFocusSessionsByTaskType(taskType: String): Flow<List<FocusSession>>
    
    @Query("SELECT * FROM focus_sessions WHERE isCompleted = 1 ORDER BY startTime DESC")
    fun getCompletedFocusSessions(): Flow<List<FocusSession>>
    
    @Query("SELECT * FROM focus_sessions WHERE isCompleted = 0 AND endTime IS NULL ORDER BY startTime DESC LIMIT 1")
    suspend fun getActiveFocusSession(): FocusSession?
    
    @Query("SELECT * FROM focus_sessions WHERE startTime >= :startDate AND startTime <= :endDate ORDER BY startTime DESC")
    fun getFocusSessionsByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): Flow<List<FocusSession>>
    
    @Query("SELECT * FROM focus_sessions WHERE id = :id")
    suspend fun getFocusSessionById(id: Long): FocusSession?
    
    @Query("SELECT COUNT(*) FROM focus_sessions WHERE isCompleted = 1")
    suspend fun getCompletedSessionCount(): Int
    
    @Query("SELECT AVG(actualDurationMinutes) FROM focus_sessions WHERE isCompleted = 1 AND actualDurationMinutes IS NOT NULL")
    suspend fun getAverageSessionDuration(): Double?
    
    @Query("SELECT AVG(focusQuality) FROM focus_sessions WHERE focusQuality IS NOT NULL")
    suspend fun getAverageFocusQuality(): Double?
    
    @Query("SELECT AVG(productivityScore) FROM focus_sessions WHERE productivityScore IS NOT NULL")
    suspend fun getAverageProductivityScore(): Double?
    
    @Query("SELECT SUM(actualDurationMinutes) FROM focus_sessions WHERE isCompleted = 1 AND actualDurationMinutes IS NOT NULL")
    suspend fun getTotalFocusTime(): Long?
    
    @Query("SELECT COUNT(*) FROM focus_sessions WHERE isCompleted = 1 AND startTime >= :startDate")
    suspend fun getSessionCountSince(startDate: LocalDateTime): Int
    
    @Query("SELECT * FROM focus_sessions WHERE isCompleted = 1 ORDER BY startTime DESC LIMIT :limit")
    suspend fun getRecentCompletedSessions(limit: Int): List<FocusSession>
    
    @Query("SELECT taskType, COUNT(*) as count FROM focus_sessions WHERE isCompleted = 1 GROUP BY taskType ORDER BY count DESC")
    suspend fun getSessionCountByTaskType(): List<TaskTypeCount>
    
    @Query("SELECT AVG(actualDurationMinutes) FROM focus_sessions WHERE taskType = :taskType AND isCompleted = 1 AND actualDurationMinutes IS NOT NULL")
    suspend fun getAverageDurationByTaskType(taskType: String): Double?
    
    @Query("SELECT COUNT(*) FROM focus_sessions WHERE wasInterrupted = 1")
    suspend fun getInterruptedSessionCount(): Int
    
    @Query("SELECT AVG(interruptionCount) FROM focus_sessions WHERE isCompleted = 1")
    suspend fun getAverageInterruptionCount(): Double?
    
    @Query("SELECT * FROM focus_sessions WHERE sessionType = :sessionType ORDER BY startTime DESC")
    fun getFocusSessionsByType(sessionType: String): Flow<List<FocusSession>>
    
    @Query("SELECT COUNT(*) FROM focus_sessions WHERE isSuccessful = 1")
    suspend fun getSuccessfulSessionCount(): Int
    
    @Insert
    suspend fun insertFocusSession(focusSession: FocusSession): Long
    
    @Update
    suspend fun updateFocusSession(focusSession: FocusSession)
    
    @Delete
    suspend fun deleteFocusSession(focusSession: FocusSession)
    
    @Query("UPDATE focus_sessions SET endTime = :endTime, actualDurationMinutes = :duration, isCompleted = 1 WHERE id = :id")
    suspend fun completeFocusSession(id: Long, endTime: LocalDateTime, duration: Int)
    
    @Query("UPDATE focus_sessions SET wasInterrupted = 1, interruptionCount = interruptionCount + 1 WHERE id = :id")
    suspend fun recordInterruption(id: Long)
    
    @Query("UPDATE focus_sessions SET breaksTaken = breaksTaken + 1 WHERE id = :id")
    suspend fun recordBreak(id: Long)
    
    @Query("UPDATE focus_sessions SET focusQuality = :quality, productivityScore = :productivity WHERE id = :id")
    suspend fun updateSessionRatings(id: Long, quality: Int?, productivity: Int?)
    
    @Query("UPDATE focus_sessions SET pomodoroCount = pomodoroCount + 1 WHERE id = :id")
    suspend fun incrementPomodoroCount(id: Long)
    
    @Query("DELETE FROM focus_sessions WHERE startTime < :cutoffDate")
    suspend fun deleteFocusSessionsOlderThan(cutoffDate: LocalDateTime)
}

data class TaskTypeCount(
    val taskType: String,
    val count: Int
)
