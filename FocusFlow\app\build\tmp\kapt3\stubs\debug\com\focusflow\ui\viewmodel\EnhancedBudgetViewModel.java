package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000r\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\b\u0007\u0018\u00002\u00020\u0001B/\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017J\u0016\u0010\u0018\u001a\u00020\u00152\u0006\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u001b\u001a\u00020\u001cJ\u0006\u0010\u001d\u001a\u00020\u0015J\u000e\u0010\u001e\u001a\u00020\u00152\u0006\u0010\u001f\u001a\u00020 J\b\u0010!\u001a\u00020\u0015H\u0002J\u0006\u0010\"\u001a\u00020\u0015J.\u0010#\u001a\u0018\u0012\u0004\u0012\u00020%\u0012\u0006\u0012\u0004\u0018\u00010%\u0012\u0006\u0012\u0004\u0018\u00010%0$2\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u001aH\u0002J\b\u0010)\u001a\u00020\u0015H\u0002J\b\u0010*\u001a\u00020\u0015H\u0002J\u001a\u0010+\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00172\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u001aJ\u000e\u0010-\u001a\u00020\u00152\u0006\u0010.\u001a\u00020\u001aR\u0014\u0010\r\u001a\b\u0012\u0004\u0012\u00020\u000f0\u000eX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006/"}, d2 = {"Lcom/focusflow/ui/viewmodel/EnhancedBudgetViewModel;", "Landroidx/lifecycle/ViewModel;", "budgetCategoryRepository", "Lcom/focusflow/data/repository/BudgetCategoryRepository;", "budgetRecommendationRepository", "Lcom/focusflow/data/repository/BudgetRecommendationRepository;", "expenseRepository", "Lcom/focusflow/data/repository/ExpenseRepository;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "budgetRecommendationService", "Lcom/focusflow/service/BudgetRecommendationService;", "(Lcom/focusflow/data/repository/BudgetCategoryRepository;Lcom/focusflow/data/repository/BudgetRecommendationRepository;Lcom/focusflow/data/repository/ExpenseRepository;Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/service/BudgetRecommendationService;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/ui/viewmodel/EnhancedBudgetUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "acceptRecommendation", "", "recommendationId", "", "addBudgetCategory", "name", "", "allocatedAmount", "", "clearError", "deleteBudgetCategory", "budgetCategory", "Lcom/focusflow/data/model/BudgetCategory;", "generateInsights", "generateNewRecommendations", "getCurrentPeriodValues", "Lkotlin/Triple;", "", "now", "Lkotlinx/datetime/LocalDateTime;", "period", "loadBudgetData", "loadRecommendations", "rejectRecommendation", "feedback", "switchBudgetPeriod", "newPeriod", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel
public final class EnhancedBudgetViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.BudgetRecommendationRepository budgetRecommendationRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.ExpenseRepository expenseRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.BudgetRecommendationService budgetRecommendationService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.ui.viewmodel.EnhancedBudgetUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.EnhancedBudgetUiState> uiState = null;
    
    @javax.inject.Inject
    public EnhancedBudgetViewModel(@org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.BudgetCategoryRepository budgetCategoryRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.BudgetRecommendationRepository budgetRecommendationRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.ExpenseRepository expenseRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.BudgetRecommendationService budgetRecommendationService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.ui.viewmodel.EnhancedBudgetUiState> getUiState() {
        return null;
    }
    
    private final void loadBudgetData() {
    }
    
    private final void loadRecommendations() {
    }
    
    public final void switchBudgetPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String newPeriod) {
    }
    
    public final void acceptRecommendation(long recommendationId) {
    }
    
    public final void rejectRecommendation(long recommendationId, @org.jetbrains.annotations.Nullable
    java.lang.String feedback) {
    }
    
    public final void generateNewRecommendations() {
    }
    
    private final void generateInsights() {
    }
    
    public final void addBudgetCategory(@org.jetbrains.annotations.NotNull
    java.lang.String name, double allocatedAmount) {
    }
    
    public final void deleteBudgetCategory(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetCategory budgetCategory) {
    }
    
    public final void clearError() {
    }
    
    private final kotlin.Triple<java.lang.Integer, java.lang.Integer, java.lang.Integer> getCurrentPeriodValues(kotlinx.datetime.LocalDateTime now, java.lang.String period) {
        return null;
    }
}