package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.Achievement
import com.focusflow.data.model.UserStats
import com.focusflow.data.model.VirtualPet
import kotlinx.coroutines.flow.Flow

@Dao
interface AchievementDao {
    @Query("SELECT * FROM achievements ORDER BY isUnlocked DESC, pointsAwarded DESC")
    fun getAllAchievements(): Flow<List<Achievement>>

    @Query("SELECT * FROM achievements WHERE isUnlocked = 1 ORDER BY unlockedAt DESC")
    fun getUnlockedAchievements(): Flow<List<Achievement>>

    @Query("SELECT * FROM achievements WHERE type = :type")
    fun getAchievementsByType(type: String): Flow<List<Achievement>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAchievement(achievement: Achievement): Long

    @Update
    suspend fun updateAchievement(achievement: Achievement)

    @Query("UPDATE achievements SET isUnlocked = 1, unlockedAt = :unlockedAt WHERE id = :achievementId")
    suspend fun unlockAchievement(achievementId: Long, unlockedAt: kotlinx.datetime.LocalDateTime)

    @Query("UPDATE achievements SET currentProgress = :progress WHERE id = :achievementId")
    suspend fun updateAchievementProgress(achievementId: Long, progress: Int)
}

@Dao
interface UserStatsDao {
    @Query("SELECT * FROM user_stats WHERE id = 1")
    fun getUserStats(): Flow<UserStats?>

    @Query("SELECT * FROM user_stats WHERE id = 1")
    suspend fun getUserStatsSync(): UserStats?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserStats(userStats: UserStats)

    @Update
    suspend fun updateUserStats(userStats: UserStats)

    @Query("UPDATE user_stats SET totalPoints = totalPoints + :points WHERE id = 1")
    suspend fun addPoints(points: Int)

    @Query("UPDATE user_stats SET expenseLoggingStreak = :streak WHERE id = 1")
    suspend fun updateExpenseLoggingStreak(streak: Int)

    @Query("UPDATE user_stats SET budgetAdherenceStreak = :streak WHERE id = 1")
    suspend fun updateBudgetAdherenceStreak(streak: Int)

    @Query("UPDATE user_stats SET totalExpensesLogged = totalExpensesLogged + 1 WHERE id = 1")
    suspend fun incrementExpensesLogged()

    @Query("UPDATE user_stats SET totalDebtPaid = totalDebtPaid + :amount WHERE id = 1")
    suspend fun addDebtPaid(amount: Double)

    @Query("UPDATE user_stats SET achievementsUnlocked = achievementsUnlocked + 1 WHERE id = 1")
    suspend fun incrementAchievementsUnlocked()
}

@Dao
interface VirtualPetDao {
    @Query("SELECT * FROM virtual_pet WHERE id = 1")
    fun getVirtualPet(): Flow<VirtualPet?>

    @Query("SELECT * FROM virtual_pet WHERE id = 1")
    suspend fun getVirtualPetSync(): VirtualPet?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertVirtualPet(virtualPet: VirtualPet)

    @Update
    suspend fun updateVirtualPet(virtualPet: VirtualPet)

    @Query("UPDATE virtual_pet SET happiness = :happiness WHERE id = 1")
    suspend fun updateHappiness(happiness: Int)

    @Query("UPDATE virtual_pet SET health = :health WHERE id = 1")
    suspend fun updateHealth(health: Int)

    @Query("UPDATE virtual_pet SET experience = experience + :exp WHERE id = 1")
    suspend fun addExperience(exp: Int)

    @Query("UPDATE virtual_pet SET level = :level WHERE id = 1")
    suspend fun updateLevel(level: Int)

    @Query("UPDATE virtual_pet SET lastFed = :timestamp WHERE id = 1")
    suspend fun updateLastFed(timestamp: kotlinx.datetime.LocalDateTime)

    @Query("UPDATE virtual_pet SET lastPlayed = :timestamp WHERE id = 1")
    suspend fun updateLastPlayed(timestamp: kotlinx.datetime.LocalDateTime)
}
