<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Test Summary</title>
<link href="css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="css/style.css" rel="stylesheet" type="text/css"/>
<script src="js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Test Summary</h1>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">36</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.091s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Packages</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Packages</h2>
<table>
<thead>
<tr>
<th>Package</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="packages/com.focusflow.html">com.focusflow</a>
</td>
<td>36</td>
<td>0</td>
<td>0</td>
<td>0.091s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tbody>
<tr>
<td class="success">
<a href="classes/com.focusflow.AlgorithmTest.html">com.focusflow.AlgorithmTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0.039s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.focusflow.BusinessLogicTest.html">com.focusflow.BusinessLogicTest</a>
</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>0.001s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.focusflow.CriticalIssuesFixedTest.html">com.focusflow.CriticalIssuesFixedTest</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.016s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.focusflow.DataModelTest.html">com.focusflow.DataModelTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.005s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.focusflow.OnboardingCompletionTest.html">com.focusflow.OnboardingCompletionTest</a>
</td>
<td>4</td>
<td>0</td>
<td>0</td>
<td>0.006s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.focusflow.Phase4Tests.html">com.focusflow.Phase4Tests</a>
</td>
<td>16</td>
<td>0</td>
<td>0</td>
<td>0.024s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="success">
<a href="classes/com.focusflow.ValidationTest.html">com.focusflow.ValidationTest</a>
</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>0s</td>
<td class="success">100%</td>
</tr>
</tbody>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.12</a> at 16 June 2025, 12:31:13 am</p>
</div>
</div>
</body>
</html>
