package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "dashboard_widgets")
data class DashboardWidget(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val widgetType: String, // "safe_to_spend", "credit_card_summary", "tasks", "habits", etc.
    val displayName: String,
    val position: Int, // Order on dashboard
    val isVisible: Boolean = true,
    val isEnabled: Boolean = true,
    val size: String = "medium", // "small", "medium", "large"
    val configuration: String? = null, // JSON object with widget-specific settings
    val refreshInterval: Int = 300, // Seconds between updates
    val lastUpdated: LocalDateTime? = null,
    val dataSource: String? = null, // Source of widget data
    val customTitle: String? = null, // User-customized title
    val colorScheme: String = "default", // "default", "primary", "secondary", "custom"
    val customColors: String? = null, // JSON object with custom color settings
    val showHeader: Boolean = true,
    val showFooter: Boolean = false,
    val headerText: String? = null,
    val footerText: String? = null,
    val iconName: String? = null,
    val animationEnabled: Boolean = true,
    val clickAction: String? = null, // Action when widget is tapped
    val longPressAction: String? = null, // Action when widget is long-pressed
    val swipeActions: String? = null, // JSON array of swipe actions
    val accessibilityLabel: String? = null,
    val accessibilityHint: String? = null,
    val isCustomizable: Boolean = true,
    val requiresPermission: Boolean = false,
    val permissionType: String? = null,
    val dataRetentionDays: Int = 30,
    val cacheEnabled: Boolean = true,
    val offlineSupport: Boolean = true,
    val errorFallback: String? = null, // Fallback content when data unavailable
    val loadingIndicator: String = "spinner", // "spinner", "skeleton", "none"
    val updateAnimation: String = "fade", // "fade", "slide", "none"
    val priority: Int = 0, // Higher priority widgets load first
    val dependencies: String? = null, // JSON array of required services/data
    val createdDate: LocalDateTime,
    val modifiedDate: LocalDateTime? = null,
    val userNotes: String? = null
)
