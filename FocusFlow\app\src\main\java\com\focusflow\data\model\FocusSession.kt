package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "focus_sessions")
data class FocusSession(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val taskType: String, // "budget_review", "bill_paying", "expense_entry", "debt_planning"
    val sessionName: String,
    val plannedDurationMinutes: Int,
    val actualDurationMinutes: Int? = null,
    val startTime: LocalDateTime,
    val endTime: LocalDateTime? = null,
    val isCompleted: Boolean = false,
    val wasInterrupted: Boolean = false,
    val interruptionCount: Int = 0,
    val breaksTaken: Int = 0,
    val focusQuality: Int? = null, // 1-5 scale, user-rated
    val productivityScore: Int? = null, // 1-5 scale, user-rated
    val notes: String? = null,
    val tasksCompleted: String? = null, // JSON array of completed tasks
    val distractions: String? = null, // JSON array of distraction types
    val sessionGoal: String? = null,
    val goalAchieved: <PERSON>olean? = null,
    val energyLevelBefore: Int? = null, // 1-5 scale
    val energyLevelAfter: Int? = null, // 1-5 scale
    val moodBefore: String? = null, // "focused", "anxious", "motivated", etc.
    val moodAfter: String? = null,
    val pomodoroCount: Int = 0, // Number of completed pomodoros
    val sessionType: String = "standard", // "pomodoro", "deep_work", "quick_task"
    val backgroundSound: String? = null, // "white_noise", "nature", "silence"
    val isSuccessful: Boolean? = null // Overall session success rating
)
