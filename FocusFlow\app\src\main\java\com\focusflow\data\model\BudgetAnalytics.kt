package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "budget_analytics")
data class BudgetAnalytics(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val categoryName: String,
    val budgetPeriod: String, // "weekly" or "monthly"
    val budgetYear: Int,
    val budgetMonth: Int? = null,
    val budgetWeek: Int? = null,
    val plannedAmount: Double,
    val actualSpent: Double,
    val variance: Double, // actualSpent - plannedAmount
    val variancePercentage: Double, // (variance / plannedAmount) * 100
    val trendDirection: String, // "increasing", "decreasing", "stable"
    val averageTransactionSize: Double,
    val transactionCount: Int,
    val largestTransaction: Double,
    val smallestTransaction: Double,
    val mostFrequentMerchant: String? = null,
    val calculatedDate: LocalDateTime,
    val daysInPeriod: Int,
    val projectedEndAmount: Double? = null, // Projection based on current trend
    val recommendedAdjustment: Double? = null,
    val seasonalityFactor: Double = 1.0,
    val isOutlierPeriod: Boolean = false // Unusual spending pattern detected
)
