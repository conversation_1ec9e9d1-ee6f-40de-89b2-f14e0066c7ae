[{"merged": "com.focusflow.app-debug-70:/xml_backup_rules.xml.flat", "source": "com.focusflow.app-main-72:/xml/backup_rules.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-xxxhdpi_ic_launcher.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-xxxhdpi/ic_launcher.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-mdpi_ic_launcher.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-mdpi/ic_launcher.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-hdpi_ic_launcher_round.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-hdpi/ic_launcher_round.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-hdpi_ic_launcher.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-hdpi/ic_launcher.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-xxhdpi_ic_launcher.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-xxhdpi/ic_launcher.xml"}, {"merged": "com.focusflow.app-debug-70:/xml_network_security_config.xml.flat", "source": "com.focusflow.app-main-72:/xml/network_security_config.xml"}, {"merged": "com.focusflow.app-debug-70:/drawable_ic_notification.xml.flat", "source": "com.focusflow.app-main-72:/drawable/ic_notification.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-mdpi_ic_launcher_round.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-mdpi/ic_launcher_round.xml"}, {"merged": "com.focusflow.app-debug-70:/xml_data_extraction_rules.xml.flat", "source": "com.focusflow.app-main-72:/xml/data_extraction_rules.xml"}, {"merged": "com.focusflow.app-debug-70:/xml_file_paths.xml.flat", "source": "com.focusflow.app-main-72:/xml/file_paths.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-xhdpi_ic_launcher.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-xhdpi/ic_launcher.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-xhdpi_ic_launcher_round.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-xhdpi/ic_launcher_round.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-xxhdpi_ic_launcher_round.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-xxhdpi/ic_launcher_round.xml"}, {"merged": "com.focusflow.app-debug-70:/mipmap-xxxhdpi_ic_launcher_round.xml.flat", "source": "com.focusflow.app-main-72:/mipmap-xxxhdpi/ic_launcher_round.xml"}, {"merged": "com.focusflow.app-debug-70:/drawable_ic_launcher_foreground.xml.flat", "source": "com.focusflow.app-main-72:/drawable/ic_launcher_foreground.xml"}]