<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Dark theme for FocusFlow -->
    <style name="Theme.FocusFlow" parent="Theme.AppCompat.DayNight">
        <!-- Primary brand color - adjusted for dark mode -->
        <item name="colorPrimary">@color/focus_blue_light</item>
        <item name="colorPrimaryDark">@color/focus_blue</item>
        <item name="colorAccent">@color/focus_green_light</item>
        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/dark_surface</item>
        <!-- Background colors -->
        <item name="android:windowBackground">@color/dark_background</item>
    </style>

    <style name="Theme.FocusFlow.NoActionBar" parent="Theme.FocusFlow">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>
