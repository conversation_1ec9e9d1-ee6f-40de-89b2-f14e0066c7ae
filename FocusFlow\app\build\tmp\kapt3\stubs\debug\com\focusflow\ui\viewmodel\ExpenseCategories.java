package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0017\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\b"}, d2 = {"Lcom/focusflow/ui/viewmodel/ExpenseCategories;", "", "()V", "categories", "", "", "getCategories", "()Ljava/util/List;", "app_debug"})
public final class ExpenseCategories {
    @org.jetbrains.annotations.NotNull
    private static final java.util.List<java.lang.String> categories = null;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.ui.viewmodel.ExpenseCategories INSTANCE = null;
    
    private ExpenseCategories() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<java.lang.String> getCategories() {
        return null;
    }
}