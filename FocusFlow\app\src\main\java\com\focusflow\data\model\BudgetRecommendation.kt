package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "budget_recommendations")
data class BudgetRecommendation(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val categoryName: String,
    val recommendedAmount: Double,
    val currentAmount: Double,
    val confidenceScore: Double, // 0.0 to 1.0
    val reasonCode: String, // "spending_pattern", "seasonal", "income_change", etc.
    val reasonDescription: String,
    val generatedDate: LocalDateTime,
    val isAccepted: Boolean? = null, // null = pending, true = accepted, false = rejected
    val userFeedback: String? = null,
    val basedOnDays: Int = 30, // Number of days of data used for recommendation
    val seasonalFactor: Double = 1.0,
    val trendFactor: Double = 1.0,
    val varianceFactor: Double = 1.0,
    val isActive: Boolean = true
)
