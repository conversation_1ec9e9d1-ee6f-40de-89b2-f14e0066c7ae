package com.focusflow.ui.onboarding;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000V\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a@\u0010\u0004\u001a\u00020\u00012\u0006\u0010\u0005\u001a\u00020\u00062\u0012\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0016\u0010\n\u001a\u00020\u00012\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aG\u0010\f\u001a\u00020\u00012\b\u0010\r\u001a\u0004\u0018\u00010\u000e2\u0012\u0010\u000f\u001a\u000e\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u00a2\u0006\u0002\u0010\u0010\u001a>\u0010\u0011\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00060\u00132\u0018\u0010\u0014\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u0013\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a@\u0010\u0015\u001a\u00020\u00012\u0006\u0010\u0016\u001a\u00020\u00062\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001aN\u0010\u0018\u001a\u00020\u00012\u0006\u0010\u0019\u001a\u00020\u000e2\u0006\u0010\u001a\u001a\u00020\u00062\u0018\u0010\u001b\u001a\u0014\u0012\u0004\u0012\u00020\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00010\u001c2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0018\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u001e\u001a\u00020\u001f2\u0006\u0010 \u001a\u00020\u001fH\u0007\u001a \u0010!\u001a\u00020\u00012\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\b\b\u0002\u0010#\u001a\u00020$H\u0007\u001au\u0010%\u001a\u00020\u00012\u0006\u0010&\u001a\u00020\'2\u0006\u0010(\u001a\u00020\u00062\u0006\u0010)\u001a\u00020\u00062\u0006\u0010*\u001a\u00020\u00062\f\u0010+\u001a\b\u0012\u0004\u0012\u00020\u00010\u00032\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u00062\u0010\b\u0002\u0010-\u001a\n\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u00032\u0015\b\u0002\u0010.\u001a\u000f\u0012\u0004\u0012\u00020\u0001\u0018\u00010\u0003\u00a2\u0006\u0002\b/2\b\b\u0002\u00100\u001a\u00020\u000eH\u0007\u001a>\u00101\u001a\u00020\u00012\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00060\u00132\u0018\u0010\u0014\u001a\u0014\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u0013\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u001a\u0016\u00102\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u00a8\u00063"}, d2 = {"ADHDFriendlyStep", "", "onNext", "Lkotlin/Function0;", "BudgetSetupStep", "weeklyBudget", "", "onBudgetChanged", "Lkotlin/Function1;", "onSkip", "CompleteStep", "onFinish", "DebtSetupStep", "hasDebt", "", "onDebtChanged", "(Ljava/lang/Boolean;Lkotlin/jvm/functions/Function1;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V", "FinancialGoalsStep", "selectedGoals", "", "onGoalsChanged", "IncomeSetupStep", "monthlyIncome", "onIncomeChanged", "NotificationSetupStep", "enableNotifications", "notificationTime", "onSettingsChanged", "Lkotlin/Function2;", "OnboardingProgressIndicator", "currentStep", "", "totalSteps", "OnboardingScreen", "onOnboardingComplete", "viewModel", "Lcom/focusflow/ui/viewmodel/OnboardingViewModel;", "OnboardingStepLayout", "icon", "Landroidx/compose/ui/graphics/vector/ImageVector;", "title", "description", "primaryButtonText", "onPrimaryClick", "secondaryButtonText", "onSecondaryClick", "content", "Landroidx/compose/runtime/Composable;", "primaryButtonEnabled", "PersonalGoalsStep", "WelcomeStep", "app_debug"})
public final class OnboardingScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void OnboardingScreen(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onOnboardingComplete, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.OnboardingViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void WelcomeStep(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ADHDFriendlyStep(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void FinancialGoalsStep(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> selectedGoals, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.util.List<java.lang.String>, kotlin.Unit> onGoalsChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PersonalGoalsStep(@org.jetbrains.annotations.NotNull
    java.util.List<java.lang.String> selectedGoals, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.util.List<java.lang.String>, kotlin.Unit> onGoalsChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void IncomeSetupStep(@org.jetbrains.annotations.NotNull
    java.lang.String monthlyIncome, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onIncomeChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSkip) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void DebtSetupStep(@org.jetbrains.annotations.Nullable
    java.lang.Boolean hasDebt, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onDebtChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSkip) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void NotificationSetupStep(boolean enableNotifications, @org.jetbrains.annotations.NotNull
    java.lang.String notificationTime, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function2<? super java.lang.Boolean, ? super java.lang.String, kotlin.Unit> onSettingsChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSkip) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void BudgetSetupStep(@org.jetbrains.annotations.NotNull
    java.lang.String weeklyBudget, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onBudgetChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNext, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onSkip) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CompleteStep(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onFinish) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void OnboardingStepLayout(@org.jetbrains.annotations.NotNull
    androidx.compose.ui.graphics.vector.ImageVector icon, @org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.NotNull
    java.lang.String primaryButtonText, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onPrimaryClick, @org.jetbrains.annotations.Nullable
    java.lang.String secondaryButtonText, @org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function0<kotlin.Unit> onSecondaryClick, @org.jetbrains.annotations.Nullable
    kotlin.jvm.functions.Function0<kotlin.Unit> content, boolean primaryButtonEnabled) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void OnboardingProgressIndicator(int currentStep, int totalSteps) {
    }
}