package com.focusflow.service

import android.app.AlarmManager
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.focusflow.MainActivity
import com.focusflow.R
import com.focusflow.receiver.AlarmReceiver
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FocusFlowNotificationManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        // Notification Channels
        const val CHANNEL_SPENDING = "spending_notifications"
        const val CHANNEL_BILLS = "bill_notifications"
        const val CHANNEL_HABITS = "habit_notifications"
        const val CHANNEL_ACHIEVEMENTS = "achievement_notifications"
        const val CHANNEL_BUDGET = "budget_notifications"
        
        // Notification IDs
        const val NOTIFICATION_DAILY_SPENDING = 1001
        const val NOTIFICATION_BILL_REMINDER = 1002
        const val NOTIFICATION_HABIT_REMINDER = 1003
        const val NOTIFICATION_ACHIEVEMENT = 1004
        const val NOTIFICATION_BUDGET_SUMMARY = 1005
        const val NOTIFICATION_BUDGET_WARNING = 1006
        
        // Request Codes for PendingIntents
        const val REQUEST_DAILY_SPENDING = 2001
        const val REQUEST_BILL_REMINDER = 2002
        const val REQUEST_HABIT_REMINDER = 2003
        const val REQUEST_BUDGET_SUMMARY = 2004
    }
    
    private val notificationManager = NotificationManagerCompat.from(context)
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    
    init {
        createNotificationChannels()
    }
    
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_SPENDING,
                    "Daily Spending Check-ins",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Daily reminders to log expenses and check spending"
                },
                NotificationChannel(
                    CHANNEL_BILLS,
                    "Bill Payment Reminders",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "Reminders for upcoming bill payments"
                },
                NotificationChannel(
                    CHANNEL_HABITS,
                    "Habit Tracking",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Reminders to log daily habits"
                },
                NotificationChannel(
                    CHANNEL_ACHIEVEMENTS,
                    "Achievements & Progress",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "Notifications for achievements and milestones"
                },
                NotificationChannel(
                    CHANNEL_BUDGET,
                    "Budget Updates",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "Budget summaries and spending alerts"
                }
            )
            
            val systemNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            channels.forEach { channel ->
                systemNotificationManager.createNotificationChannel(channel)
            }
        }
    }
    
    // Daily Spending Check-in
    fun scheduleDailySpendingReminder(hour: Int, minute: Int) {
        val intent = Intent(context, AlarmReceiver::class.java).apply {
            putExtra("notification_type", "daily_spending")
            putExtra("title", "Daily Spending Check-in")
            putExtra("message", "How did your spending go today? Tap to log expenses.")
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            REQUEST_DAILY_SPENDING,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val calendar = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            
            // If the time has already passed today, schedule for tomorrow
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_MONTH, 1)
            }
        }
        
        alarmManager.setRepeating(
            AlarmManager.RTC_WAKEUP,
            calendar.timeInMillis,
            AlarmManager.INTERVAL_DAY,
            pendingIntent
        )
    }
    
    // Bill Payment Reminders
    fun scheduleBillReminder(billName: String, dueDate: Long, daysBeforeList: List<Int>) {
        daysBeforeList.forEach { daysBefore ->
            val reminderTime = dueDate - (daysBefore * 24 * 60 * 60 * 1000)
            
            if (reminderTime > System.currentTimeMillis()) {
                val intent = Intent(context, AlarmReceiver::class.java).apply {
                    putExtra("notification_type", "bill_reminder")
                    putExtra("title", "Bill Payment Reminder")
                    putExtra("message", "$billName is due in $daysBefore day${if (daysBefore != 1) "s" else ""}!")
                }
                
                val pendingIntent = PendingIntent.getBroadcast(
                    context,
                    REQUEST_BILL_REMINDER + billName.hashCode() + daysBefore,
                    intent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    reminderTime,
                    pendingIntent
                )
            }
        }
    }
    
    // Weekly Budget Summary
    fun scheduleWeeklyBudgetSummary() {
        val intent = Intent(context, AlarmReceiver::class.java).apply {
            putExtra("notification_type", "budget_summary")
            putExtra("title", "Weekly Budget Summary")
            putExtra("message", "See how you did this week and plan for next week!")
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            REQUEST_BUDGET_SUMMARY,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val calendar = Calendar.getInstance().apply {
            set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
            set(Calendar.HOUR_OF_DAY, 18)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            
            // If this Sunday has passed, schedule for next Sunday
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.WEEK_OF_YEAR, 1)
            }
        }
        
        alarmManager.setRepeating(
            AlarmManager.RTC_WAKEUP,
            calendar.timeInMillis,
            AlarmManager.INTERVAL_DAY * 7,
            pendingIntent
        )
    }
    
    // Habit Tracking Reminders
    fun scheduleHabitReminder(habitName: String, hour: Int, minute: Int) {
        val intent = Intent(context, AlarmReceiver::class.java).apply {
            putExtra("notification_type", "habit_reminder")
            putExtra("title", "Habit Reminder")
            putExtra("message", "Time to log your $habitName!")
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            REQUEST_HABIT_REMINDER + habitName.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val calendar = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_MONTH, 1)
            }
        }
        
        alarmManager.setRepeating(
            AlarmManager.RTC_WAKEUP,
            calendar.timeInMillis,
            AlarmManager.INTERVAL_DAY,
            pendingIntent
        )
    }
    
    // Immediate Notifications
    fun showAchievementNotification(title: String, message: String) {
        showNotification(
            title = title,
            message = message,
            channelId = CHANNEL_ACHIEVEMENTS,
            notificationId = NOTIFICATION_ACHIEVEMENT + Random().nextInt(1000),
            priority = NotificationCompat.PRIORITY_LOW
        )
    }
    
    fun showBudgetWarning(message: String) {
        showNotification(
            title = "Budget Alert",
            message = message,
            channelId = CHANNEL_BUDGET,
            notificationId = NOTIFICATION_BUDGET_WARNING,
            priority = NotificationCompat.PRIORITY_HIGH
        )
    }
    
    private fun showNotification(
        title: String,
        message: String,
        channelId: String,
        notificationId: Int,
        priority: Int = NotificationCompat.PRIORITY_DEFAULT
    ) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            notificationId,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(priority)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .build()
        
        notificationManager.notify(notificationId, notification)
    }
    
    // Cancel specific notifications
    fun cancelDailySpendingReminder() {
        val intent = Intent(context, AlarmReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            REQUEST_DAILY_SPENDING,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        alarmManager.cancel(pendingIntent)
    }
    
    fun cancelAllNotifications() {
        notificationManager.cancelAll()
        
        // Cancel all scheduled alarms
        val requestCodes = listOf(
            REQUEST_DAILY_SPENDING,
            REQUEST_BILL_REMINDER,
            REQUEST_HABIT_REMINDER,
            REQUEST_BUDGET_SUMMARY
        )
        
        requestCodes.forEach { requestCode ->
            val intent = Intent(context, AlarmReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            alarmManager.cancel(pendingIntent)
        }
    }
}
