package com.focusflow

import com.focusflow.data.model.*
import kotlinx.coroutines.runBlocking
import kotlinx.datetime.*
import org.junit.Test
import org.junit.Assert.*

// Unit tests for data models and business logic
class DataModelTest {

    @Test
    fun testExpenseCreation() {
        val expense = Expense(
            amount = 25.50,
            category = "Food & Dining",
            description = "Lunch",
            merchant = "Restaurant",
            date = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()),
            receiptPath = null
        )

        assertEquals("Amount should match", 25.50, expense.amount, 0.01)
        assertEquals("Category should match", "Food & Dining", expense.category)
        assertEquals("Description should match", "Lunch", expense.description)
        assertNotNull("Date should not be null", expense.date)
    }

    @Test
    fun testCreditCardCreation() {
        val creditCard = CreditCard(
            name = "Test Card",
            currentBalance = 1000.0,
            creditLimit = 5000.0,
            minimumPayment = 50.0,
            dueDate = LocalDate(2024, 1, 15),
            interestRate = 18.99
        )

        assertEquals("Name should match", "Test Card", creditCard.name)
        assertEquals("Balance should match", 1000.0, creditCard.currentBalance, 0.01)
        assertEquals("Credit limit should match", 5000.0, creditCard.creditLimit, 0.01)
        assertTrue("Should have available credit", creditCard.creditLimit > creditCard.currentBalance)
    }

    @Test
    fun testBudgetCategoryCreation() {
        val budgetCategory = BudgetCategory(
            name = "Groceries",
            allocatedAmount = 200.0,
            spentAmount = 0.0,
            budgetPeriod = "weekly",
            budgetYear = 2024,
            budgetMonth = 1,
            budgetWeek = 1
        )

        assertEquals("Name should match", "Groceries", budgetCategory.name)
        assertEquals("Allocated amount should match", 200.0, budgetCategory.allocatedAmount, 0.01)
        assertEquals("Spent amount should be zero", 0.0, budgetCategory.spentAmount, 0.01)
        assertEquals("Budget period should match", "weekly", budgetCategory.budgetPeriod)
        assertTrue("Should have remaining budget", budgetCategory.allocatedAmount > budgetCategory.spentAmount)
    }

    @Test
    fun testDateTimeOperations() {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val yesterday = now.date.minus(DatePeriod(days = 1)).atTime(now.hour, now.minute)
        val lastWeek = now.date.minus(DatePeriod(days = 7)).atTime(now.hour, now.minute)

        assertTrue("Yesterday should be before now", yesterday < now)
        assertTrue("Last week should be before yesterday", lastWeek < yesterday)
        assertTrue("Last week should be before now", lastWeek < now)
    }
}

// Unit tests for business logic calculations
class BusinessLogicTest {

    @Test
    fun testBudgetCalculations() {
        val totalBudget = 500.0
        val totalSpent = 350.0
        val remaining = totalBudget - totalSpent

        assertEquals("Remaining should be calculated correctly", 150.0, remaining, 0.01)

        val percentage = totalSpent / totalBudget
        assertEquals("Percentage should be calculated correctly", 0.7, percentage, 0.01)
    }

    @Test
    fun testCreditUtilizationCalculation() {
        val currentBalance = 1500.0
        val creditLimit = 5000.0
        val utilization = currentBalance / creditLimit

        assertEquals("Utilization should be 30%", 0.3, utilization, 0.01)
        assertTrue("Utilization should be under recommended 30%", utilization <= 0.3)
    }

    @Test
    fun testInterestCalculation() {
        val principal = 1000.0
        val annualRate = 18.99
        val monthlyRate = annualRate / 12 / 100
        val monthlyInterest = principal * monthlyRate

        assertEquals("Monthly interest should be calculated correctly", 15.825, monthlyInterest, 0.01)
    }
}

// Performance and algorithm tests
class AlgorithmTest {

    @Test
    fun testCollectionPerformance() {
        // Test list operations performance
        val startTime = System.currentTimeMillis()

        val expenses = mutableListOf<Expense>()
        repeat(1000) { i ->
            val expense = Expense(
                amount = i.toDouble(),
                category = "Test",
                description = "Performance test $i",
                date = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            )
            expenses.add(expense)
        }

        val endTime = System.currentTimeMillis()
        val duration = endTime - startTime

        assertTrue("List operations should complete quickly", duration < 1000)
        assertEquals("Should have 1000 expenses", 1000, expenses.size)
    }

    @Test
    fun testSortingPerformance() {
        val expenses = (1..1000).map { i ->
            Expense(
                amount = (1000 - i).toDouble(),
                category = "Test",
                description = "Test $i",
                date = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            )
        }

        val startTime = System.currentTimeMillis()
        val sortedExpenses = expenses.sortedBy { it.amount }
        val endTime = System.currentTimeMillis()

        assertTrue("Sorting should complete quickly", endTime - startTime < 100)
        assertEquals("First expense should have lowest amount", 0.0, sortedExpenses.first().amount, 0.01)
        assertEquals("Last expense should have highest amount", 999.0, sortedExpenses.last().amount, 0.01)
    }
}

// Validation and utility tests
class ValidationTest {

    @Test
    fun testAmountValidation() {
        // Test valid amounts
        assertTrue("Valid decimal should pass", isValidAmount("25.50"))
        assertTrue("Valid integer should pass", isValidAmount("100"))
        assertTrue("Valid zero should pass", isValidAmount("0"))
        assertTrue("Valid small decimal should pass", isValidAmount("0.01"))

        // Test invalid amounts
        assertFalse("Negative amount should fail", isValidAmount("-10"))
        assertFalse("Text should fail", isValidAmount("abc"))
        assertFalse("Empty string should fail", isValidAmount(""))
        assertFalse("Multiple decimals should fail", isValidAmount("10.50.25"))
    }

    @Test
    fun testCategoryValidation() {
        assertTrue("Valid category should pass", isValidCategory("Food & Dining"))
        assertTrue("Valid category should pass", isValidCategory("Transportation"))
        assertFalse("Empty category should fail", isValidCategory(""))
        assertFalse("Whitespace only should fail", isValidCategory("   "))
    }

    private fun isValidAmount(amount: String): Boolean {
        return try {
            val value = amount.toDouble()
            value >= 0
        } catch (e: NumberFormatException) {
            false
        }
    }

    private fun isValidCategory(category: String): Boolean {
        return category.isNotBlank()
    }
}

