package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.repository.CreditCardRepository
import com.focusflow.data.repository.ExpenseRepository
import com.focusflow.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val expenseRepository: ExpenseRepository,
    private val creditCardRepository: CreditCardRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    init {
        loadDashboardData()
    }

    private fun loadDashboardData() {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val period = preferences?.budgetPeriod ?: "weekly"
                val weeklyBudget = preferences?.weeklyBudget ?: 300.0
                val monthlyBudget = preferences?.monthlyBudget ?: 1200.0
                
                val (startDate, endDate) = getCurrentPeriodDates(period)
                val totalSpent = expenseRepository.getTotalSpentInPeriod(startDate, endDate)
                
                val budget = if (period == "weekly") weeklyBudget else monthlyBudget
                val safeToSpend = budget - totalSpent
                
                // Get credit card data
                val totalDebt = creditCardRepository.getTotalDebt()
                val today = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
                val nextWeek = today.plus(7, DateTimeUnit.DAY)
                val nextPayment = creditCardRepository.getTotalMinimumPaymentsDue(nextWeek)
                
                _uiState.value = _uiState.value.copy(
                    safeToSpend = safeToSpend,
                    totalSpent = totalSpent,
                    budget = budget,
                    budgetPeriod = period,
                    totalDebt = totalDebt,
                    nextPayment = nextPayment,
                    isLoading = false
                )
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load dashboard data: ${e.message}"
                )
            }
        }
    }

    fun refreshData() {
        _uiState.value = _uiState.value.copy(isLoading = true)
        loadDashboardData()
    }

    private fun getCurrentPeriodDates(period: String): Pair<LocalDateTime, LocalDateTime> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        val today = now.date
        
        return when (period) {
            "weekly" -> {
                val startOfWeek = today.minus(today.dayOfWeek.ordinal, DateTimeUnit.DAY)
                val endOfWeek = startOfWeek.plus(6, DateTimeUnit.DAY)
                Pair(
                    startOfWeek.atTime(0, 0),
                    endOfWeek.atTime(23, 59, 59)
                )
            }
            "monthly" -> {
                val startOfMonth = LocalDate(today.year, today.month, 1)
                val endOfMonth = startOfMonth.plus(1, DateTimeUnit.MONTH).minus(1, DateTimeUnit.DAY)
                Pair(
                    startOfMonth.atTime(0, 0),
                    endOfMonth.atTime(23, 59, 59)
                )
            }
            else -> {
                val startOfWeek = today.minus(today.dayOfWeek.ordinal, DateTimeUnit.DAY)
                val endOfWeek = startOfWeek.plus(6, DateTimeUnit.DAY)
                Pair(
                    startOfWeek.atTime(0, 0),
                    endOfWeek.atTime(23, 59, 59)
                )
            }
        }
    }
}

data class DashboardUiState(
    val safeToSpend: Double = 0.0,
    val totalSpent: Double = 0.0,
    val budget: Double = 0.0,
    val budgetPeriod: String = "weekly",
    val totalDebt: Double = 0.0,
    val nextPayment: Double = 0.0,
    val isLoading: Boolean = true,
    val error: String? = null
)

