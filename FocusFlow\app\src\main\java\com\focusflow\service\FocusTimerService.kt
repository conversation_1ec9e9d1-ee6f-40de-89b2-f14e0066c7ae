package com.focusflow.service

import com.focusflow.data.dao.FocusSessionDao
import com.focusflow.data.model.FocusSession
import kotlinx.coroutines.flow.*
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.*

@Singleton
class FocusTimerService @Inject constructor(
    private val focusSessionDao: FocusSessionDao,
    private val notificationService: NotificationService,
    private val gamificationService: GamificationService
) {
    
    private val _currentSession = MutableStateFlow<FocusSession?>(null)
    val currentSession: StateFlow<FocusSession?> = _currentSession.asStateFlow()
    
    private val _timerState = MutableStateFlow(TimerState.STOPPED)
    val timerState: StateFlow<TimerState> = _timerState.asStateFlow()
    
    private val _remainingTime = MutableStateFlow(0L)
    val remainingTime: StateFlow<Long> = _remainingTime.asStateFlow()
    
    private val _isBreakTime = MutableStateFlow(false)
    val isBreakTime: StateFlow<Boolean> = _isBreakTime.asStateFlow()
    
    private var timerJob: Job? = null
    private var sessionStartTime: LocalDateTime? = null
    
    companion object {
        const val DEFAULT_WORK_DURATION = 25 * 60 // 25 minutes in seconds
        const val DEFAULT_SHORT_BREAK = 5 * 60 // 5 minutes in seconds
        const val DEFAULT_LONG_BREAK = 15 * 60 // 15 minutes in seconds
        const val POMODOROS_BEFORE_LONG_BREAK = 4
    }
    
    suspend fun startFocusSession(
        taskType: String,
        sessionName: String,
        plannedDurationMinutes: Int = 25,
        sessionType: String = "pomodoro",
        sessionGoal: String? = null,
        backgroundSound: String? = null
    ): Long {
        
        // End any existing session
        endCurrentSession()
        
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        sessionStartTime = now
        
        val session = FocusSession(
            taskType = taskType,
            sessionName = sessionName,
            plannedDurationMinutes = plannedDurationMinutes,
            startTime = now,
            sessionGoal = sessionGoal,
            sessionType = sessionType,
            backgroundSound = backgroundSound
        )
        
        val sessionId = focusSessionDao.insertFocusSession(session)
        val savedSession = focusSessionDao.getFocusSessionById(sessionId)
        
        _currentSession.value = savedSession
        _timerState.value = TimerState.RUNNING
        _isBreakTime.value = false
        
        startTimer(plannedDurationMinutes * 60L)
        
        // Send notification
        notificationService.showNotification(
            "Focus Session Started",
            "Working on: $sessionName for ${plannedDurationMinutes}min"
        )
        
        return sessionId
    }
    
    suspend fun pauseSession() {
        _timerState.value = TimerState.PAUSED
        timerJob?.cancel()
        
        _currentSession.value?.let { session ->
            focusSessionDao.recordInterruption(session.id)
        }
    }
    
    suspend fun resumeSession() {
        if (_currentSession.value != null && _timerState.value == TimerState.PAUSED) {
            _timerState.value = TimerState.RUNNING
            startTimer(_remainingTime.value)
        }
    }
    
    suspend fun endCurrentSession(
        focusQuality: Int? = null,
        productivityScore: Int? = null,
        notes: String? = null
    ) {
        _currentSession.value?.let { session ->
            val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            val actualDuration = sessionStartTime?.let { start ->
                val duration = now.toInstant(TimeZone.currentSystemDefault()) - 
                              start.toInstant(TimeZone.currentSystemDefault())
                duration.inWholeMinutes.toInt()
            } ?: session.plannedDurationMinutes
            
            focusSessionDao.completeFocusSession(session.id, now, actualDuration)
            
            if (focusQuality != null || productivityScore != null) {
                focusSessionDao.updateSessionRatings(session.id, focusQuality, productivityScore)
            }
            
            // Award gamification points
            gamificationService.onFocusSessionCompleted(actualDuration, focusQuality ?: 3)
            
            // Send completion notification
            notificationService.showNotification(
                "Focus Session Complete!",
                "Great job! You focused for ${actualDuration} minutes."
            )
        }
        
        stopTimer()
        _currentSession.value = null
    }
    
    suspend fun startBreak(isLongBreak: Boolean = false) {
        val breakDuration = if (isLongBreak) DEFAULT_LONG_BREAK else DEFAULT_SHORT_BREAK
        
        _currentSession.value?.let { session ->
            focusSessionDao.recordBreak(session.id)
        }
        
        _isBreakTime.value = true
        _timerState.value = TimerState.RUNNING
        
        startTimer(breakDuration.toLong())
        
        val breakType = if (isLongBreak) "long" else "short"
        notificationService.showNotification(
            "Break Time!",
            "Take a ${breakType} break for ${breakDuration / 60} minutes"
        )
    }
    
    suspend fun skipBreak() {
        if (_isBreakTime.value) {
            _isBreakTime.value = false
            stopTimer()
            
            // Suggest starting another focus session
            notificationService.showNotification(
                "Break Skipped",
                "Ready for another focus session?"
            )
        }
    }
    
    private fun startTimer(durationSeconds: Long) {
        timerJob?.cancel()
        _remainingTime.value = durationSeconds
        
        timerJob = CoroutineScope(Dispatchers.Default).launch {
            while (_remainingTime.value > 0 && _timerState.value == TimerState.RUNNING) {
                delay(1000)
                _remainingTime.value = _remainingTime.value - 1
            }
            
            if (_remainingTime.value <= 0) {
                onTimerComplete()
            }
        }
    }
    
    private suspend fun onTimerComplete() {
        if (_isBreakTime.value) {
            // Break is over, ready for next focus session
            _isBreakTime.value = false
            _timerState.value = TimerState.STOPPED
            
            notificationService.showNotification(
                "Break Complete!",
                "Ready to focus again?"
            )
        } else {
            // Focus session is complete
            _currentSession.value?.let { session ->
                focusSessionDao.incrementPomodoroCount(session.id)
                
                val shouldTakeLongBreak = (session.pomodoroCount + 1) % POMODOROS_BEFORE_LONG_BREAK == 0
                
                notificationService.showNotification(
                    "Focus Session Complete!",
                    if (shouldTakeLongBreak) "Time for a long break!" else "Time for a short break!"
                )
                
                // Automatically start break
                startBreak(shouldTakeLongBreak)
            }
        }
    }
    
    private fun stopTimer() {
        timerJob?.cancel()
        _timerState.value = TimerState.STOPPED
        _remainingTime.value = 0L
    }
    
    suspend fun getSessionStatistics(): FocusSessionStatistics {
        val totalSessions = focusSessionDao.getCompletedSessionCount()
        val totalFocusTime = focusSessionDao.getTotalFocusTime() ?: 0L
        val averageDuration = focusSessionDao.getAverageSessionDuration() ?: 0.0
        val averageFocusQuality = focusSessionDao.getAverageFocusQuality() ?: 0.0
        val averageProductivity = focusSessionDao.getAverageProductivityScore() ?: 0.0
        val interruptedSessions = focusSessionDao.getInterruptedSessionCount()
        val successfulSessions = focusSessionDao.getSuccessfulSessionCount()
        
        val last7Days = Clock.System.now()
            .minus(DateTimePeriod(days = 7), TimeZone.currentSystemDefault())
            .toLocalDateTime(TimeZone.currentSystemDefault())
        
        val recentSessions = focusSessionDao.getSessionCountSince(last7Days)
        
        return FocusSessionStatistics(
            totalSessions = totalSessions,
            totalFocusTimeMinutes = totalFocusTime,
            averageDurationMinutes = averageDuration,
            averageFocusQuality = averageFocusQuality,
            averageProductivityScore = averageProductivity,
            interruptionRate = if (totalSessions > 0) interruptedSessions.toDouble() / totalSessions else 0.0,
            successRate = if (totalSessions > 0) successfulSessions.toDouble() / totalSessions else 0.0,
            sessionsLast7Days = recentSessions
        )
    }
    
    fun getPresetTimerOptions(): List<TimerPreset> {
        return listOf(
            TimerPreset("Pomodoro", 25, "Standard 25-minute focus session"),
            TimerPreset("Short Focus", 15, "Quick 15-minute session for small tasks"),
            TimerPreset("Deep Work", 45, "Extended focus for complex tasks"),
            TimerPreset("Quick Task", 10, "10-minute sprint for urgent items"),
            TimerPreset("Review Session", 20, "20 minutes for budget/expense review"),
            TimerPreset("Planning", 30, "30 minutes for financial planning")
        )
    }
}

enum class TimerState {
    STOPPED, RUNNING, PAUSED
}

data class FocusSessionStatistics(
    val totalSessions: Int,
    val totalFocusTimeMinutes: Long,
    val averageDurationMinutes: Double,
    val averageFocusQuality: Double,
    val averageProductivityScore: Double,
    val interruptionRate: Double,
    val successRate: Double,
    val sessionsLast7Days: Int
)

data class TimerPreset(
    val name: String,
    val durationMinutes: Int,
    val description: String
)
