package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0002\ba\b\u0087\b\u0018\u00002\u00020\u0001B\u00c7\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\u0005\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\u0005\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\u0006\u0010\r\u001a\u00020\u000e\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0011\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0019\u001a\u00020\u0005\u0012\u0006\u0010\u001a\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u001d\u001a\u00020\n\u0012\b\b\u0002\u0010\u001e\u001a\u00020\n\u0012\b\b\u0002\u0010\u001f\u001a\u00020\n\u0012\b\b\u0002\u0010 \u001a\u00020\n\u0012\b\b\u0002\u0010!\u001a\u00020\n\u0012\b\b\u0002\u0010\"\u001a\u00020\n\u0012\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010$\u001a\u00020\n\u0012\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u000e\u0012\b\b\u0002\u0010\'\u001a\u00020\u0005\u00a2\u0006\u0002\u0010(J\t\u0010P\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010Q\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\t\u0010R\u001a\u00020\u0011H\u00c6\u0003J\t\u0010S\u001a\u00020\u0011H\u00c6\u0003J\t\u0010T\u001a\u00020\u0011H\u00c6\u0003J\t\u0010U\u001a\u00020\u0015H\u00c6\u0003J\u0010\u0010V\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003\u00a2\u0006\u0002\u0010,J\u000b\u0010W\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010X\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010Y\u001a\u00020\u0005H\u00c6\u0003J\t\u0010Z\u001a\u00020\u0005H\u00c6\u0003J\t\u0010[\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\\\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010]\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010^\u001a\u00020\nH\u00c6\u0003J\t\u0010_\u001a\u00020\nH\u00c6\u0003J\t\u0010`\u001a\u00020\nH\u00c6\u0003J\t\u0010a\u001a\u00020\nH\u00c6\u0003J\t\u0010b\u001a\u00020\nH\u00c6\u0003J\t\u0010c\u001a\u00020\nH\u00c6\u0003J\u000b\u0010d\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010e\u001a\u00020\nH\u00c6\u0003J\t\u0010f\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010g\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\u000b\u0010h\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003J\t\u0010i\u001a\u00020\u0005H\u00c6\u0003J\t\u0010j\u001a\u00020\u0005H\u00c6\u0003J\t\u0010k\u001a\u00020\u0005H\u00c6\u0003J\t\u0010l\u001a\u00020\nH\u00c6\u0003J\t\u0010m\u001a\u00020\u0005H\u00c6\u0003J\t\u0010n\u001a\u00020\u0005H\u00c6\u0003J\t\u0010o\u001a\u00020\u000eH\u00c6\u0003J\u00e0\u0002\u0010p\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\u00052\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u000e2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\u0010\u001a\u00020\u00112\b\b\u0002\u0010\u0012\u001a\u00020\u00112\b\b\u0002\u0010\u0013\u001a\u00020\u00112\b\b\u0002\u0010\u0014\u001a\u00020\u00152\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0019\u001a\u00020\u00052\b\b\u0002\u0010\u001a\u001a\u00020\u00052\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u001d\u001a\u00020\n2\b\b\u0002\u0010\u001e\u001a\u00020\n2\b\b\u0002\u0010\u001f\u001a\u00020\n2\b\b\u0002\u0010 \u001a\u00020\n2\b\b\u0002\u0010!\u001a\u00020\n2\b\b\u0002\u0010\"\u001a\u00020\n2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010$\u001a\u00020\n2\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u000e2\b\b\u0002\u0010\'\u001a\u00020\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010qJ\u0013\u0010r\u001a\u00020\n2\b\u0010s\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010t\u001a\u00020\u0011H\u00d6\u0001J\t\u0010u\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010*R\u0015\u0010\u0016\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\n\n\u0002\u0010-\u001a\u0004\b+\u0010,R\u0011\u0010\u001f\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010/R\u0011\u0010!\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010/R\u0011\u0010 \u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u0010/R\u0011\u0010\u001e\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010/R\u0013\u0010%\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010*R\u0011\u0010$\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010/R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00106R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u00106R\u0011\u0010\u001d\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010/R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010:R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010/R\u0013\u0010&\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010*R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010*R\u0011\u0010\"\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010/R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u00106R\u0013\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u00106R\u0011\u0010\u0019\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u00106R\u0013\u0010#\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u00106R\u0011\u0010\u000b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u00106R\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u00106R\u0011\u0010\'\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u00106R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u00106R\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010GR\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u00106R\u0013\u0010\u001b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u00106R\u0011\u0010\u0012\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u0010KR\u0011\u0010\u001a\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u00106R\u0013\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bM\u00106R\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010KR\u0011\u0010\u0013\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u0010K\u00a8\u0006v"}, d2 = {"Lcom/focusflow/data/model/AccountabilityContact;", "", "id", "", "name", "", "relationship", "contactMethod", "contactInfo", "isActive", "", "permissionLevel", "sharingPreferences", "addedDate", "Lkotlinx/datetime/LocalDateTime;", "lastContactDate", "totalInteractions", "", "successfulInterventions", "trustLevel", "responseRate", "", "averageResponseTime", "preferredContactTime", "timeZone", "notificationFrequency", "supportType", "specializations", "notes", "emergencyContact", "canReceiveSpendingAlerts", "canReceiveBudgetUpdates", "canReceiveGoalProgress", "canReceiveEmergencyAlerts", "mutualAccountability", "partnerUserId", "consentGiven", "consentDate", "lastConsentUpdate", "privacyLevel", "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;IIIDLjava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZZZLjava/lang/String;ZLkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;)V", "getAddedDate", "()Lkotlinx/datetime/LocalDateTime;", "getAverageResponseTime", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getCanReceiveBudgetUpdates", "()Z", "getCanReceiveEmergencyAlerts", "getCanReceiveGoalProgress", "getCanReceiveSpendingAlerts", "getConsentDate", "getConsentGiven", "getContactInfo", "()Ljava/lang/String;", "getContactMethod", "getEmergencyContact", "getId", "()J", "getLastConsentUpdate", "getLastContactDate", "getMutualAccountability", "getName", "getNotes", "getNotificationFrequency", "getPartnerUserId", "getPermissionLevel", "getPreferredContactTime", "getPrivacyLevel", "getRelationship", "getResponseRate", "()D", "getSharingPreferences", "getSpecializations", "getSuccessfulInterventions", "()I", "getSupportType", "getTimeZone", "getTotalInteractions", "getTrustLevel", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;IIIDLjava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZZZZZLjava/lang/String;ZLkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;)Lcom/focusflow/data/model/AccountabilityContact;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "accountability_contacts")
public final class AccountabilityContact {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String name = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String relationship = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String contactMethod = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String contactInfo = null;
    private final boolean isActive = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String permissionLevel = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String sharingPreferences = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime addedDate = null;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime lastContactDate = null;
    private final int totalInteractions = 0;
    private final int successfulInterventions = 0;
    private final int trustLevel = 0;
    private final double responseRate = 0.0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer averageResponseTime = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String preferredContactTime = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String timeZone = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String notificationFrequency = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String supportType = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String specializations = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String notes = null;
    private final boolean emergencyContact = false;
    private final boolean canReceiveSpendingAlerts = false;
    private final boolean canReceiveBudgetUpdates = false;
    private final boolean canReceiveGoalProgress = false;
    private final boolean canReceiveEmergencyAlerts = false;
    private final boolean mutualAccountability = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String partnerUserId = null;
    private final boolean consentGiven = false;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime consentDate = null;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime lastConsentUpdate = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String privacyLevel = null;
    
    public AccountabilityContact(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String relationship, @org.jetbrains.annotations.NotNull
    java.lang.String contactMethod, @org.jetbrains.annotations.NotNull
    java.lang.String contactInfo, boolean isActive, @org.jetbrains.annotations.NotNull
    java.lang.String permissionLevel, @org.jetbrains.annotations.NotNull
    java.lang.String sharingPreferences, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime addedDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastContactDate, int totalInteractions, int successfulInterventions, int trustLevel, double responseRate, @org.jetbrains.annotations.Nullable
    java.lang.Integer averageResponseTime, @org.jetbrains.annotations.Nullable
    java.lang.String preferredContactTime, @org.jetbrains.annotations.Nullable
    java.lang.String timeZone, @org.jetbrains.annotations.NotNull
    java.lang.String notificationFrequency, @org.jetbrains.annotations.NotNull
    java.lang.String supportType, @org.jetbrains.annotations.Nullable
    java.lang.String specializations, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean emergencyContact, boolean canReceiveSpendingAlerts, boolean canReceiveBudgetUpdates, boolean canReceiveGoalProgress, boolean canReceiveEmergencyAlerts, boolean mutualAccountability, @org.jetbrains.annotations.Nullable
    java.lang.String partnerUserId, boolean consentGiven, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime consentDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastConsentUpdate, @org.jetbrains.annotations.NotNull
    java.lang.String privacyLevel) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getRelationship() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getContactMethod() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getContactInfo() {
        return null;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPermissionLevel() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSharingPreferences() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getAddedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getLastContactDate() {
        return null;
    }
    
    public final int getTotalInteractions() {
        return 0;
    }
    
    public final int getSuccessfulInterventions() {
        return 0;
    }
    
    public final int getTrustLevel() {
        return 0;
    }
    
    public final double getResponseRate() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getAverageResponseTime() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getPreferredContactTime() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getTimeZone() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getNotificationFrequency() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSupportType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSpecializations() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNotes() {
        return null;
    }
    
    public final boolean getEmergencyContact() {
        return false;
    }
    
    public final boolean getCanReceiveSpendingAlerts() {
        return false;
    }
    
    public final boolean getCanReceiveBudgetUpdates() {
        return false;
    }
    
    public final boolean getCanReceiveGoalProgress() {
        return false;
    }
    
    public final boolean getCanReceiveEmergencyAlerts() {
        return false;
    }
    
    public final boolean getMutualAccountability() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getPartnerUserId() {
        return null;
    }
    
    public final boolean getConsentGiven() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getConsentDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getLastConsentUpdate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getPrivacyLevel() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component10() {
        return null;
    }
    
    public final int component11() {
        return 0;
    }
    
    public final int component12() {
        return 0;
    }
    
    public final int component13() {
        return 0;
    }
    
    public final double component14() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component21() {
        return null;
    }
    
    public final boolean component22() {
        return false;
    }
    
    public final boolean component23() {
        return false;
    }
    
    public final boolean component24() {
        return false;
    }
    
    public final boolean component25() {
        return false;
    }
    
    public final boolean component26() {
        return false;
    }
    
    public final boolean component27() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component28() {
        return null;
    }
    
    public final boolean component29() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component30() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component31() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component32() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.AccountabilityContact copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, @org.jetbrains.annotations.NotNull
    java.lang.String relationship, @org.jetbrains.annotations.NotNull
    java.lang.String contactMethod, @org.jetbrains.annotations.NotNull
    java.lang.String contactInfo, boolean isActive, @org.jetbrains.annotations.NotNull
    java.lang.String permissionLevel, @org.jetbrains.annotations.NotNull
    java.lang.String sharingPreferences, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime addedDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastContactDate, int totalInteractions, int successfulInterventions, int trustLevel, double responseRate, @org.jetbrains.annotations.Nullable
    java.lang.Integer averageResponseTime, @org.jetbrains.annotations.Nullable
    java.lang.String preferredContactTime, @org.jetbrains.annotations.Nullable
    java.lang.String timeZone, @org.jetbrains.annotations.NotNull
    java.lang.String notificationFrequency, @org.jetbrains.annotations.NotNull
    java.lang.String supportType, @org.jetbrains.annotations.Nullable
    java.lang.String specializations, @org.jetbrains.annotations.Nullable
    java.lang.String notes, boolean emergencyContact, boolean canReceiveSpendingAlerts, boolean canReceiveBudgetUpdates, boolean canReceiveGoalProgress, boolean canReceiveEmergencyAlerts, boolean mutualAccountability, @org.jetbrains.annotations.Nullable
    java.lang.String partnerUserId, boolean consentGiven, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime consentDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastConsentUpdate, @org.jetbrains.annotations.NotNull
    java.lang.String privacyLevel) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}