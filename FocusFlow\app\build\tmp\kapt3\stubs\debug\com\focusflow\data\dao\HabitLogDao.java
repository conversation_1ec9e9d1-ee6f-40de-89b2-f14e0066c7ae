package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001e\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u000f0\u000eH\'J$\u0010\u0010\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000e2\u0006\u0010\u0011\u001a\u00020\u000b2\u0006\u0010\u0012\u001a\u00020\u000bH\'J\u001c\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000e2\u0006\u0010\b\u001a\u00020\tH\'J,\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000e2\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0011\u001a\u00020\u000b2\u0006\u0010\u0012\u001a\u00020\u000bH\'J\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u000f0\u000e2\u0006\u0010\n\u001a\u00020\u000bH\'J&\u0010\u0016\u001a\u00020\u00172\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\u0011\u001a\u00020\u000b2\u0006\u0010\u0012\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u0018J\u0016\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006\u001c"}, d2 = {"Lcom/focusflow/data/dao/HabitLogDao;", "", "deleteHabitLog", "", "habitLog", "Lcom/focusflow/data/model/HabitLog;", "(Lcom/focusflow/data/model/HabitLog;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteHabitLogByTypeAndDate", "habitType", "", "date", "Lkotlinx/datetime/LocalDate;", "(Ljava/lang/String;Lkotlinx/datetime/LocalDate;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllHabitTypes", "Lkotlinx/coroutines/flow/Flow;", "", "getHabitLogsByDateRange", "startDate", "endDate", "getHabitLogsByType", "getHabitLogsByTypeAndDateRange", "getHabitLogsForDate", "getHabitStreakCount", "", "(Ljava/lang/String;Lkotlinx/datetime/LocalDate;Lkotlinx/datetime/LocalDate;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertHabitLog", "", "updateHabitLog", "app_debug"})
@androidx.room.Dao
public abstract interface HabitLogDao {
    
    @androidx.room.Query(value = "SELECT * FROM habit_logs WHERE habitType = :habitType ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsByType(@org.jetbrains.annotations.NotNull
    java.lang.String habitType);
    
    @androidx.room.Query(value = "SELECT * FROM habit_logs WHERE date = :date ORDER BY habitType ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsForDate(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date);
    
    @androidx.room.Query(value = "SELECT * FROM habit_logs WHERE date >= :startDate AND date <= :endDate ORDER BY date DESC, habitType ASC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsByDateRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate endDate);
    
    @androidx.room.Query(value = "SELECT * FROM habit_logs WHERE habitType = :habitType AND date >= :startDate AND date <= :endDate ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.HabitLog>> getHabitLogsByTypeAndDateRange(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate endDate);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM habit_logs WHERE habitType = :habitType AND date >= :startDate AND date <= :endDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getHabitStreakCount(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate endDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT DISTINCT habitType FROM habit_logs ORDER BY habitType")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<java.lang.String>> getAllHabitTypes();
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertHabitLog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HabitLog habitLog, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateHabitLog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HabitLog habitLog, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteHabitLog(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.HabitLog habitLog, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM habit_logs WHERE habitType = :habitType AND date = :date")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteHabitLogByTypeAndDate(@org.jetbrains.annotations.NotNull
    java.lang.String habitType, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDate date, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}