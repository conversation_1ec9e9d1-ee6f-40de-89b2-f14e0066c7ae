{"logs": [{"outputFile": "com.focusflow.app-mergeDebugResources-68:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7c7c5a30567eb3e2d1c39db198a41f8c\\transformed\\jetified-activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "362,383", "startColumns": "4,4", "startOffsets": "21554,22641", "endColumns": "41,59", "endOffsets": "21591,22696"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\dc6d5b5f2df2f7220830487457b3fd87\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "41,42,43,44", "startColumns": "4,4,4,4", "startOffsets": "1566,1631,1701,1765", "endColumns": "64,69,63,60", "endOffsets": "1626,1696,1760,1821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b11534671a794edd3e02caf79988e112\\transformed\\jetified-appcompat-resources-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2127,2143,2149,3153,3169", "startColumns": "4,4,4,4,4", "startOffsets": "136384,136809,136987,170997,171408", "endLines": "2142,2148,2158,3168,3172", "endColumns": "24,24,24,24,24", "endOffsets": "136804,136982,137266,171403,171530"}}, {"source": "C:\\Users\\<USER>\\Downloads\\Understanding Content in pasted_content (1)\\FocusFlow\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "61,64,62,65,63,18,40,41,43,42,44,45,9,10,32,11,33,12,21,22,23,24,20,25,26,27,28,29,49,55,58,54,57,56,48,51,50,15,37,3,4,5,36,6,7,34,35,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2626,2788,2678,2840,2732,703,1633,1686,1794,1744,1847,1902,368,416,1283,469,1337,518,834,880,926,972,789,1018,1064,1110,1156,1202,2055,2334,2522,2270,2458,2397,1996,2169,2113,609,1545,92,140,188,1494,236,282,1392,1442,746", "endColumns": "50,50,52,54,54,41,51,56,51,48,53,55,46,51,52,47,53,48,44,44,44,44,43,44,44,44,44,44,56,61,65,62,62,59,57,57,54,58,52,46,46,46,49,44,44,48,50,41", "endOffsets": "2672,2834,2726,2890,2782,740,1680,1738,1841,1788,1896,1953,410,463,1331,512,1386,562,874,920,966,1012,828,1058,1104,1150,1196,1242,2107,2391,2583,2328,2516,2452,2049,2222,2163,663,1593,134,182,230,1539,276,322,1436,1488,783"}, "to": {"startLines": "52,53,54,55,56,64,75,76,77,78,79,80,87,88,89,90,91,92,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,116,131,140,141,142,149,154,155,156,157,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2327,2378,2429,2482,2537,3127,3941,3993,4050,4102,4151,4205,4662,4709,4761,4814,4862,4916,5110,5155,5200,5245,5290,5334,5379,5424,5469,5514,5559,5616,5678,5744,5807,5870,5930,5988,6046,6236,7099,7740,7787,7834,8292,8628,8673,8718,8767,8939", "endColumns": "50,50,52,54,54,41,51,56,51,48,53,55,46,51,52,47,53,48,44,44,44,44,43,44,44,44,44,44,56,61,65,62,62,59,57,57,54,58,52,46,46,46,49,44,44,48,50,41", "endOffsets": "2373,2424,2477,2532,2587,3164,3988,4045,4097,4146,4200,4256,4704,4756,4809,4857,4911,4960,5150,5195,5240,5285,5329,5374,5419,5464,5509,5554,5611,5673,5739,5802,5865,5925,5983,6041,6096,6290,7147,7782,7829,7876,8337,8668,8713,8762,8813,8976"}}, {"source": "C:\\Users\\<USER>\\Downloads\\Understanding Content in pasted_content (1)\\FocusFlow\\app\\src\\main\\res\\values\\dimens.xml", "from": {"startLines": "36,34,35,39,40,25,24,23,26,22,5,12,18,6,13,19,3,10,16,7,4,11,17,30,31,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1487,1392,1442,1573,1622,1051,1009,968,1093,927,190,494,774,236,546,823,93,385,671,285,143,441,724,1238,1294,1190", "endColumns": "50,49,44,48,49,41,41,40,41,40,45,51,48,48,54,51,49,55,52,46,46,52,49,55,49,47", "endOffsets": "1533,1437,1482,1617,1667,1088,1046,1004,1130,963,231,541,818,280,596,870,138,436,719,327,185,489,769,1289,1339,1233"}, "to": {"startLines": "242,243,244,255,256,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,305,306,307", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14430,14481,14531,15290,15339,16978,17020,17062,17103,17145,17186,17232,17284,17333,17382,17437,17489,17539,17595,17648,17695,17742,17795,18298,18354,18404", "endColumns": "50,49,44,48,49,41,41,40,41,40,45,51,48,48,54,51,49,55,52,46,46,52,49,55,49,47", "endOffsets": "14476,14526,14571,15334,15384,17015,17057,17098,17140,17181,17227,17279,17328,17377,17432,17484,17534,17590,17643,17690,17737,17790,17840,18349,18399,18447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\21a6a6d8b8d5c5045da238ec9be68151\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "384", "startColumns": "4", "startOffsets": "22701", "endColumns": "53", "endOffsets": "22750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e1dc919567705f737931e90f7aead7b\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,63,254,431,433,438,439,440,441,442,443,444,445,446,447", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,289,372,483,618,3071,15237,25861,26003,26379,26468,26567,26675,26772,26860,26960,27030,27127,27237", "endLines": "5,7,10,14,33,63,254,431,433,438,439,440,441,442,443,444,445,446,447", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "284,367,478,613,1194,3122,15285,25932,26058,26463,26562,26670,26767,26855,26955,27025,27122,27232,27321"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6d9025e15716a4d8a0cfcec38b189854\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "351,363,386,2802,2807", "startColumns": "4,4,4,4,4", "startOffsets": "21017,21596,22805,159688,159858", "endLines": "351,363,386,2806,2810", "endColumns": "56,64,63,24,24", "endOffsets": "21069,21656,22864,159853,160002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\904fd36595e0135e8fdca5c15906c24d\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "37,57,58,73,74,129,130,245,246,247,248,249,250,251,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,308,309,310,356,357,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,392,422,423,424,425,426,427,428,461,1836,1837,1841,1842,1846,2004,2005,2658,2692,2748,2781,2811,2844", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1348,2592,2664,3810,3875,6967,7036,14576,14646,14714,14786,14856,14917,14991,16000,16061,16122,16184,16248,16310,16371,16439,16539,16599,16665,16738,16807,16864,16916,18452,18524,18600,21268,21303,21707,21762,21825,21880,21938,21996,22057,22120,22177,22228,22278,22339,22396,22462,22496,22531,23165,25231,25298,25370,25439,25508,25582,25654,28072,119231,119348,119549,119659,119860,131901,131973,153473,155046,157276,159007,160007,160689", "endLines": "37,57,58,73,74,129,130,245,246,247,248,249,250,251,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,308,309,310,356,357,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,392,422,423,424,425,426,427,428,461,1836,1840,1841,1845,1846,2004,2005,2663,2701,2780,2801,2843,2849", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1403,2659,2747,3870,3936,7031,7094,14641,14709,14781,14851,14912,14986,15059,16056,16117,16179,16243,16305,16366,16434,16534,16594,16660,16733,16802,16859,16911,16973,18519,18595,18660,21298,21333,21757,21820,21875,21933,21991,22052,22115,22172,22223,22273,22334,22391,22457,22491,22526,22561,23230,25293,25365,25434,25503,25577,25649,25737,28138,119343,119544,119654,119855,119984,131968,132035,153671,155342,159002,159683,160684,160851"}}, {"source": "C:\\Users\\<USER>\\Downloads\\Understanding Content in pasted_content (1)\\FocusFlow\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,8,7,4,6,5,3,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "16,553,479,234,364,286,126,63", "endColumns": "46,82,73,51,114,77,107,62", "endOffsets": "58,631,548,281,474,359,229,121"}, "to": {"startLines": "421,434,435,448,453,454,465,466", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "25184,26063,26146,27326,27606,27721,28316,28424", "endColumns": "46,82,73,51,114,77,107,62", "endOffsets": "25226,26141,26215,27373,27716,27794,28419,28482"}}, {"source": "C:\\Users\\<USER>\\Downloads\\Understanding Content in pasted_content (1)\\FocusFlow\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "142,670", "endLines": "12,17", "endColumns": "12,12", "endOffsets": "662,850"}, "to": {"startLines": "1883,1893", "startColumns": "4,4", "startOffsets": "122625,123060", "endLines": "1892,1896", "endColumns": "12,12", "endOffsets": "123055,123237"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d5221fa0308d516e8dafbc8d9244b53\\transformed\\jetified-coil-base-2.5.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "348", "startColumns": "4", "startOffsets": "20854", "endColumns": "49", "endOffsets": "20899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b7575953e232ac886e0021593ed04f20\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "34,35,36,38,39,40,45,46,47,48,49,50,51,59,60,61,62,65,66,67,68,69,70,71,72,81,82,83,84,85,86,93,94,114,115,117,118,119,120,121,122,123,124,125,126,127,128,132,133,134,135,136,137,138,139,143,144,145,146,147,148,150,151,152,153,158,159,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,252,253,257,258,259,260,261,262,263,297,298,299,300,301,302,303,304,343,344,345,346,353,360,361,364,381,388,389,390,391,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,459,467,468,469,470,471,472,480,481,485,489,493,498,504,511,515,519,524,528,532,536,540,544,548,554,558,564,568,574,578,583,587,590,594,600,604,610,614,620,623,627,631,635,639,643,644,645,646,649,652,655,658,662,663,664,665,666,669,671,673,675,680,681,685,691,695,696,698,710,711,715,721,725,726,727,731,758,762,763,767,795,967,993,1164,1190,1221,1229,1235,1251,1273,1278,1283,1293,1302,1311,1315,1322,1341,1348,1349,1358,1361,1364,1368,1372,1376,1379,1380,1385,1390,1400,1405,1412,1418,1419,1422,1426,1431,1433,1435,1438,1441,1443,1447,1450,1457,1460,1463,1467,1469,1473,1475,1477,1479,1483,1491,1499,1511,1517,1526,1529,1540,1543,1544,1549,1550,1565,1634,1704,1705,1715,1724,1725,1727,1731,1734,1737,1740,1743,1746,1749,1752,1756,1759,1762,1765,1769,1772,1776,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1802,1804,1805,1806,1807,1808,1809,1810,1811,1813,1814,1816,1817,1819,1821,1822,1824,1825,1826,1827,1828,1829,1831,1832,1833,1834,1835,1847,1849,1851,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1867,1868,1869,1870,1871,1872,1873,1875,1879,1897,1898,1899,1900,1901,1902,1906,1907,1908,1909,1911,1913,1915,1917,1919,1920,1921,1922,1924,1926,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1942,1943,1944,1945,1947,1949,1950,1952,1953,1955,1957,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1972,1973,1974,1975,1977,1978,1979,1980,1981,1983,1985,1987,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2006,2081,2084,2087,2090,2104,2117,2159,2162,2191,2218,2227,2291,2654,2664,2702,2730,2850,2874,2880,2886,2907,3031,3090,3096,3100,3106,3141,3173,3239,3259,3314,3326,3352", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1199,1254,1299,1408,1449,1504,1826,1890,1960,2021,2096,2172,2249,2752,2837,2919,2995,3169,3246,3324,3430,3536,3615,3695,3752,4261,4335,4410,4475,4541,4601,4965,5037,6101,6168,6295,6354,6413,6472,6531,6590,6644,6698,6751,6805,6859,6913,7152,7226,7305,7378,7452,7523,7595,7667,7881,7938,7996,8069,8143,8217,8342,8414,8487,8557,8818,8878,8981,9050,9119,9189,9263,9339,9403,9480,9556,9633,9698,9767,9844,9919,9988,10056,10133,10199,10260,10357,10422,10491,10590,10661,10720,10778,10835,10894,10958,11029,11101,11173,11245,11317,11384,11452,11520,11579,11642,11706,11796,11887,11947,12013,12080,12146,12216,12280,12333,12400,12461,12528,12641,12699,12762,12827,12892,12967,13040,13112,13156,13203,13249,13298,13359,13420,13481,13543,13607,13671,13735,13800,13863,13923,13984,14050,14109,14169,14231,14302,14362,15064,15150,15389,15479,15566,15654,15736,15819,15909,17845,17897,17955,18000,18066,18130,18187,18244,20575,20632,20680,20729,21125,21458,21505,21661,22566,22922,22986,23048,23108,23235,23309,23379,23457,23511,23581,23666,23714,23760,23821,23884,23950,24014,24085,24148,24213,24277,24338,24399,24451,24524,24598,24667,24742,24816,24890,25031,27973,28487,28565,28655,28743,28839,28929,29511,29600,29847,30128,30380,30665,31058,31535,31757,31979,32255,32482,32712,32942,33172,33402,33629,34048,34274,34699,34929,35357,35576,35859,36067,36198,36425,36851,37076,37503,37724,38149,38269,38545,38846,39170,39461,39775,39912,40043,40148,40390,40557,40761,40969,41240,41352,41464,41569,41686,41900,42046,42186,42272,42620,42708,42954,43372,43621,43703,43801,44458,44558,44810,45234,45489,45583,45672,45909,47933,48175,48277,48530,50686,61367,62883,73578,75106,76863,77489,77909,79170,80435,80691,80927,81474,81968,82573,82771,83351,84719,85094,85212,85750,85907,86103,86376,86632,86802,86943,87007,87372,87739,88415,88679,89017,89370,89464,89650,89956,90218,90343,90470,90709,90920,91039,91232,91409,91864,92045,92167,92426,92539,92726,92828,92935,93064,93339,93847,94343,95220,95514,96084,96233,96965,97137,97221,97557,97649,98345,103576,108947,109009,109587,110171,110262,110375,110604,110764,110916,111087,111253,111422,111589,111752,111995,112165,112338,112509,112783,112982,113187,113517,113601,113697,113793,113891,113991,114093,114195,114297,114399,114501,114601,114697,114809,114938,115061,115192,115323,115421,115535,115629,115769,115903,115999,116111,116211,116327,116423,116535,116635,116775,116911,117075,117205,117363,117513,117654,117798,117933,118045,118195,118323,118451,118587,118719,118849,118979,119091,119989,120135,120279,120417,120483,120573,120649,120753,120843,120945,121053,121161,121261,121341,121433,121531,121641,121693,121771,121877,121969,122073,122183,122305,122468,123242,123322,123422,123512,123622,123712,123953,124047,124153,124245,124345,124457,124571,124687,124803,124897,125011,125123,125225,125345,125467,125549,125653,125773,125899,125997,126091,126179,126291,126407,126529,126641,126816,126932,127018,127110,127222,127346,127413,127539,127607,127735,127879,128007,128076,128171,128286,128399,128498,128607,128718,128829,128930,129035,129135,129265,129356,129479,129573,129685,129771,129875,129971,130059,130177,130281,130385,130511,130599,130707,130807,130897,131007,131091,131193,131277,131331,131395,131501,131587,131697,131781,132040,134656,134774,134889,134969,135330,135867,137271,137349,138693,140054,140442,143285,153338,153676,155347,156704,160856,161607,161869,162069,162448,166726,169007,169236,169387,169602,170685,171535,174561,175305,177436,177776,179087", "endLines": "34,35,36,38,39,40,45,46,47,48,49,50,51,59,60,61,62,65,66,67,68,69,70,71,72,81,82,83,84,85,86,93,94,114,115,117,118,119,120,121,122,123,124,125,126,127,128,132,133,134,135,136,137,138,139,143,144,145,146,147,148,150,151,152,153,158,159,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,252,253,257,258,259,260,261,262,263,297,298,299,300,301,302,303,304,343,344,345,346,353,360,361,364,381,388,389,390,391,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,459,467,468,469,470,471,479,480,484,488,492,497,503,510,514,518,523,527,531,535,539,543,547,553,557,563,567,573,577,582,586,589,593,599,603,609,613,619,622,626,630,634,638,642,643,644,645,648,651,654,657,661,662,663,664,665,668,670,672,674,679,680,684,690,694,695,697,709,710,714,720,724,725,726,730,757,761,762,766,794,966,992,1163,1189,1220,1228,1234,1250,1272,1277,1282,1292,1301,1310,1314,1321,1340,1347,1348,1357,1360,1363,1367,1371,1375,1378,1379,1384,1389,1399,1404,1411,1417,1418,1421,1425,1430,1432,1434,1437,1440,1442,1446,1449,1456,1459,1462,1466,1468,1472,1474,1476,1478,1482,1490,1498,1510,1516,1525,1528,1539,1542,1543,1548,1549,1554,1633,1703,1704,1714,1723,1724,1726,1730,1733,1736,1739,1742,1745,1748,1751,1755,1758,1761,1764,1768,1771,1775,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1801,1803,1804,1805,1806,1807,1808,1809,1810,1812,1813,1815,1816,1818,1820,1821,1823,1824,1825,1826,1827,1828,1830,1831,1832,1833,1834,1835,1848,1850,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1866,1867,1868,1869,1870,1871,1872,1874,1878,1882,1897,1898,1899,1900,1901,1905,1906,1907,1908,1910,1912,1914,1916,1918,1919,1920,1921,1923,1925,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1938,1941,1942,1943,1944,1946,1948,1949,1951,1952,1954,1956,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1971,1972,1973,1974,1976,1977,1978,1979,1980,1982,1984,1986,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2080,2083,2086,2089,2103,2109,2126,2161,2190,2217,2226,2290,2653,2657,2691,2729,2747,2873,2879,2885,2906,3030,3050,3095,3099,3105,3140,3152,3238,3258,3313,3325,3351,3358", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1249,1294,1343,1444,1499,1561,1885,1955,2016,2091,2167,2244,2322,2832,2914,2990,3066,3241,3319,3425,3531,3610,3690,3747,3805,4330,4405,4470,4536,4596,4657,5032,5105,6163,6231,6349,6408,6467,6526,6585,6639,6693,6746,6800,6854,6908,6962,7221,7300,7373,7447,7518,7590,7662,7735,7933,7991,8064,8138,8212,8287,8409,8482,8552,8623,8873,8934,9045,9114,9184,9258,9334,9398,9475,9551,9628,9693,9762,9839,9914,9983,10051,10128,10194,10255,10352,10417,10486,10585,10656,10715,10773,10830,10889,10953,11024,11096,11168,11240,11312,11379,11447,11515,11574,11637,11701,11791,11882,11942,12008,12075,12141,12211,12275,12328,12395,12456,12523,12636,12694,12757,12822,12887,12962,13035,13107,13151,13198,13244,13293,13354,13415,13476,13538,13602,13666,13730,13795,13858,13918,13979,14045,14104,14164,14226,14297,14357,14425,15145,15232,15474,15561,15649,15731,15814,15904,15995,17892,17950,17995,18061,18125,18182,18239,18293,20627,20675,20724,20775,21154,21500,21549,21702,22593,22981,23043,23103,23160,23304,23374,23452,23506,23576,23661,23709,23755,23816,23879,23945,24009,24080,24143,24208,24272,24333,24394,24446,24519,24593,24662,24737,24811,24885,25026,25096,28021,28560,28650,28738,28834,28924,29506,29595,29842,30123,30375,30660,31053,31530,31752,31974,32250,32477,32707,32937,33167,33397,33624,34043,34269,34694,34924,35352,35571,35854,36062,36193,36420,36846,37071,37498,37719,38144,38264,38540,38841,39165,39456,39770,39907,40038,40143,40385,40552,40756,40964,41235,41347,41459,41564,41681,41895,42041,42181,42267,42615,42703,42949,43367,43616,43698,43796,44453,44553,44805,45229,45484,45578,45667,45904,47928,48170,48272,48525,50681,61362,62878,73573,75101,76858,77484,77904,79165,80430,80686,80922,81469,81963,82568,82766,83346,84714,85089,85207,85745,85902,86098,86371,86627,86797,86938,87002,87367,87734,88410,88674,89012,89365,89459,89645,89951,90213,90338,90465,90704,90915,91034,91227,91404,91859,92040,92162,92421,92534,92721,92823,92930,93059,93334,93842,94338,95215,95509,96079,96228,96960,97132,97216,97552,97644,97922,103571,108942,109004,109582,110166,110257,110370,110599,110759,110911,111082,111248,111417,111584,111747,111990,112160,112333,112504,112778,112977,113182,113512,113596,113692,113788,113886,113986,114088,114190,114292,114394,114496,114596,114692,114804,114933,115056,115187,115318,115416,115530,115624,115764,115898,115994,116106,116206,116322,116418,116530,116630,116770,116906,117070,117200,117358,117508,117649,117793,117928,118040,118190,118318,118446,118582,118714,118844,118974,119086,119226,120130,120274,120412,120478,120568,120644,120748,120838,120940,121048,121156,121256,121336,121428,121526,121636,121688,121766,121872,121964,122068,122178,122300,122463,122620,123317,123417,123507,123617,123707,123948,124042,124148,124240,124340,124452,124566,124682,124798,124892,125006,125118,125220,125340,125462,125544,125648,125768,125894,125992,126086,126174,126286,126402,126524,126636,126811,126927,127013,127105,127217,127341,127408,127534,127602,127730,127874,128002,128071,128166,128281,128394,128493,128602,128713,128824,128925,129030,129130,129260,129351,129474,129568,129680,129766,129870,129966,130054,130172,130276,130380,130506,130594,130702,130802,130892,131002,131086,131188,131272,131326,131390,131496,131582,131692,131776,131896,134651,134769,134884,134964,135325,135558,136379,137344,138688,140049,140437,143280,153333,153468,155041,156699,157271,161602,161864,162064,162443,166721,167327,169231,169382,169597,170680,170992,174556,175300,177431,177771,179082,179285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a787f09ed2bafcdbe799a8b9000e6968\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "385", "startColumns": "4", "startOffsets": "22755", "endColumns": "49", "endOffsets": "22800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0939b5539bd9819785f5853c9457da19\\transformed\\navigation-common-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3051,3064,3070,3076,3085", "startColumns": "4,4,4,4,4", "startOffsets": "167332,167971,168215,168462,168825", "endLines": "3063,3069,3075,3078,3089", "endColumns": "24,24,24,24,24", "endOffsets": "167966,168210,168457,168590,169002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5e956e8c4b9ff7ae956b1bc7abb3c361\\transformed\\navigation-runtime-2.7.5\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "358,2110,3079,3082", "startColumns": "4,4,4,4", "startOffsets": "21338,135563,168595,168710", "endLines": "358,2116,3081,3084", "endColumns": "52,24,24,24", "endOffsets": "21386,135862,168705,168820"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d10faef53a64c4cc4f5661f98403ffe\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "420", "startColumns": "4", "startOffsets": "25101", "endColumns": "82", "endOffsets": "25179"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8cd39096ff5e90b2ee8900d321becb0c\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "355,359", "startColumns": "4,4", "startOffsets": "21214,21391", "endColumns": "53,66", "endOffsets": "21263,21453"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1b3539420545eba470a6ac4d8c725246\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "382", "startColumns": "4", "startOffsets": "22598", "endColumns": "42", "endOffsets": "22636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1f170a70d96fcd26f910cf5fef71c6ab\\transformed\\jetified-ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,347,349,350,352,354,387,429,430,432,436,437,449,450,451,452,455,456,457,458,460,462,463,464,1555,1558,1561", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18665,18724,18783,18843,18903,18963,19023,19083,19143,19203,19263,19323,19383,19442,19502,19562,19622,19682,19742,19802,19862,19922,19982,20042,20101,20161,20221,20280,20339,20398,20457,20516,20780,20904,20962,21074,21159,22869,25742,25807,25937,26220,26321,27378,27430,27490,27552,27799,27835,27869,27919,28026,28143,28190,28226,97927,98039,98150", "endLines": "311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,347,349,350,352,354,387,429,430,432,436,437,449,450,451,452,455,456,457,458,460,462,463,464,1557,1560,1564", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "18719,18778,18838,18898,18958,19018,19078,19138,19198,19258,19318,19378,19437,19497,19557,19617,19677,19737,19797,19857,19917,19977,20037,20096,20156,20216,20275,20334,20393,20452,20511,20570,20849,20957,21012,21120,21209,22917,25802,25856,25998,26316,26374,27425,27485,27547,27601,27830,27864,27914,27968,28067,28185,28221,28311,98034,98145,98340"}}]}]}