package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0019\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0000\b\u0087\b\u0018\u00002\u00020\u0001Ba\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0005\u0012\b\b\u0002\u0010\n\u001a\u00020\u000b\u0012\b\b\u0002\u0010\f\u001a\u00020\u0005\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001e\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\"\u001a\u00020\u0005H\u00c6\u0003J\t\u0010#\u001a\u00020\u000bH\u00c6\u0003J\t\u0010$\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010%\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003Je\u0010&\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u000b2\b\b\u0002\u0010\f\u001a\u00020\u00052\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001J\u0013\u0010\'\u001a\u00020(2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020\u0005H\u00d6\u0001J\t\u0010+\u001a\u00020,H\u00d6\u0001R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0011R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u001aR\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0011R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0011\u00a8\u0006-"}, d2 = {"Lcom/focusflow/data/model/UserStats;", "", "id", "", "totalPoints", "", "currentLevel", "expenseLoggingStreak", "budgetAdherenceStreak", "totalExpensesLogged", "totalDebtPaid", "", "achievementsUnlocked", "lastActivityDate", "Lkotlinx/datetime/LocalDateTime;", "(JIIIIIDILkotlinx/datetime/LocalDateTime;)V", "getAchievementsUnlocked", "()I", "getBudgetAdherenceStreak", "getCurrentLevel", "getExpenseLoggingStreak", "getId", "()J", "getLastActivityDate", "()Lkotlinx/datetime/LocalDateTime;", "getTotalDebtPaid", "()D", "getTotalExpensesLogged", "getTotalPoints", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "", "other", "hashCode", "toString", "", "app_debug"})
@androidx.room.Entity(tableName = "user_stats")
public final class UserStats {
    @androidx.room.PrimaryKey
    private final long id = 0L;
    private final int totalPoints = 0;
    private final int currentLevel = 0;
    private final int expenseLoggingStreak = 0;
    private final int budgetAdherenceStreak = 0;
    private final int totalExpensesLogged = 0;
    private final double totalDebtPaid = 0.0;
    private final int achievementsUnlocked = 0;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime lastActivityDate = null;
    
    public UserStats(long id, int totalPoints, int currentLevel, int expenseLoggingStreak, int budgetAdherenceStreak, int totalExpensesLogged, double totalDebtPaid, int achievementsUnlocked, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastActivityDate) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    public final int getTotalPoints() {
        return 0;
    }
    
    public final int getCurrentLevel() {
        return 0;
    }
    
    public final int getExpenseLoggingStreak() {
        return 0;
    }
    
    public final int getBudgetAdherenceStreak() {
        return 0;
    }
    
    public final int getTotalExpensesLogged() {
        return 0;
    }
    
    public final double getTotalDebtPaid() {
        return 0.0;
    }
    
    public final int getAchievementsUnlocked() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getLastActivityDate() {
        return null;
    }
    
    public UserStats() {
        super();
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final int component2() {
        return 0;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final int component5() {
        return 0;
    }
    
    public final int component6() {
        return 0;
    }
    
    public final double component7() {
        return 0.0;
    }
    
    public final int component8() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.UserStats copy(long id, int totalPoints, int currentLevel, int expenseLoggingStreak, int budgetAdherenceStreak, int totalExpensesLogged, double totalDebtPaid, int achievementsUnlocked, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastActivityDate) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}