package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\n\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u000e\u0010\u000b\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0014\u0010\u000e\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000fH\'J\u0010\u0010\u0011\u001a\u0004\u0018\u00010\u0012H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0010\u0010\u0013\u001a\u0004\u0018\u00010\u0012H\u00a7@\u00a2\u0006\u0002\u0010\rJ\u0018\u0010\u0014\u001a\u0004\u0018\u00010\t2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u001c\u0010\u0015\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000f2\u0006\u0010\u0016\u001a\u00020\u0017H\'J\u0014\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000fH\'J\u0014\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00100\u000fH\'J\u0016\u0010\u001a\u001a\u00020\u00052\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001e\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u001c\u001a\u00020\u001dH\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010 \u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001e\u0010!\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\"\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010#J\u001e\u0010$\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010%\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010&\u00a8\u0006\'"}, d2 = {"Lcom/focusflow/data/dao/AccountabilityContactDao;", "", "deactivateContact", "", "id", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteContact", "contact", "Lcom/focusflow/data/model/AccountabilityContact;", "(Lcom/focusflow/data/model/AccountabilityContact;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveContactCount", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllActiveContacts", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageResponseRate", "", "getAverageTrustLevel", "getContactById", "getContactsByRelationship", "relationship", "", "getEmergencyContacts", "getSpendingAlertContacts", "insertContact", "recordInteraction", "contactDate", "Lkotlinx/datetime/LocalDateTime;", "(JLkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "recordSuccessfulIntervention", "updateContact", "updateResponseRate", "responseRate", "(JDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateTrustLevel", "trustLevel", "(JILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao
public abstract interface AccountabilityContactDao {
    
    @androidx.room.Query(value = "SELECT * FROM accountability_contacts WHERE isActive = 1 ORDER BY trustLevel DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AccountabilityContact>> getAllActiveContacts();
    
    @androidx.room.Query(value = "SELECT * FROM accountability_contacts WHERE relationship = :relationship AND isActive = 1 ORDER BY trustLevel DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AccountabilityContact>> getContactsByRelationship(@org.jetbrains.annotations.NotNull
    java.lang.String relationship);
    
    @androidx.room.Query(value = "SELECT * FROM accountability_contacts WHERE emergencyContact = 1 AND isActive = 1 ORDER BY trustLevel DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AccountabilityContact>> getEmergencyContacts();
    
    @androidx.room.Query(value = "SELECT * FROM accountability_contacts WHERE canReceiveSpendingAlerts = 1 AND isActive = 1")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.AccountabilityContact>> getSpendingAlertContacts();
    
    @androidx.room.Query(value = "SELECT * FROM accountability_contacts WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getContactById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.AccountabilityContact> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM accountability_contacts WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getActiveContactCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(responseRate) FROM accountability_contacts WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageResponseRate(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(trustLevel) FROM accountability_contacts WHERE isActive = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageTrustLevel(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertContact(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AccountabilityContact contact, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateContact(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AccountabilityContact contact, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteContact(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.AccountabilityContact contact, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE accountability_contacts SET lastContactDate = :contactDate, totalInteractions = totalInteractions + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordInteraction(long id, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime contactDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE accountability_contacts SET successfulInterventions = successfulInterventions + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordSuccessfulIntervention(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE accountability_contacts SET trustLevel = :trustLevel WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateTrustLevel(long id, int trustLevel, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE accountability_contacts SET responseRate = :responseRate WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateResponseRate(long id, double responseRate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE accountability_contacts SET isActive = 0 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deactivateContact(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}