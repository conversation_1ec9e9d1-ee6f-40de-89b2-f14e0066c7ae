package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\f\n\u0002\u0010\b\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\bd\b\u0087\b\u0018\u00002\u00020\u0001B\u00f9\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\u0005\u0012\u0006\u0010\t\u001a\u00020\u0005\u0012\u0006\u0010\n\u001a\u00020\u0007\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\u0006\u0010\r\u001a\u00020\u0007\u0012\u0006\u0010\u000e\u001a\u00020\u0007\u0012\u0006\u0010\u000f\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0014\u0012\u0006\u0010\u0016\u001a\u00020\u0005\u0012\u0006\u0010\u0017\u001a\u00020\u0007\u0012\u0006\u0010\u0018\u001a\u00020\u0019\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u0019\u0012\b\b\u0002\u0010\u001b\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u001c\u001a\u00020\u0014\u0012\b\b\u0002\u0010\u001d\u001a\u00020\u0014\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u001f\u001a\u00020 \u0012\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u0014\u0012\b\b\u0002\u0010\'\u001a\u00020\u0005\u0012\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010)\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010,J\t\u0010Z\u001a\u00020\u0003H\u00c6\u0003J\t\u0010[\u001a\u00020\u0007H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010]\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010^\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010_\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010`\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003\u00a2\u0006\u0002\u0010GJ\u0010\u0010a\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003\u00a2\u0006\u0002\u0010GJ\t\u0010b\u001a\u00020\u0005H\u00c6\u0003J\t\u0010c\u001a\u00020\u0007H\u00c6\u0003J\t\u0010d\u001a\u00020\u0019H\u00c6\u0003J\t\u0010e\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010f\u001a\u0004\u0018\u00010\u0019H\u00c6\u0003J\t\u0010g\u001a\u00020\u0014H\u00c6\u0003J\t\u0010h\u001a\u00020\u0014H\u00c6\u0003J\t\u0010i\u001a\u00020\u0014H\u00c6\u0003J\u000b\u0010j\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010k\u001a\u00020 H\u00c6\u0003J\u000b\u0010l\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010m\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010n\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010o\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010p\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010q\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010r\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003\u00a2\u0006\u0002\u0010GJ\t\u0010s\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010t\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010u\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010v\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010NJ\u000b\u0010w\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010x\u001a\u00020\u0005H\u00c6\u0003J\t\u0010y\u001a\u00020\u0005H\u00c6\u0003J\t\u0010z\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010{\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010|\u001a\u00020\u0005H\u00c6\u0003J\t\u0010}\u001a\u00020\u0007H\u00c6\u0003J\u009a\u0003\u0010~\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u00072\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\f\u001a\u00020\u00052\b\b\u0002\u0010\r\u001a\u00020\u00072\b\b\u0002\u0010\u000e\u001a\u00020\u00072\b\b\u0002\u0010\u000f\u001a\u00020\u00052\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00142\b\b\u0002\u0010\u0016\u001a\u00020\u00052\b\b\u0002\u0010\u0017\u001a\u00020\u00072\b\b\u0002\u0010\u0018\u001a\u00020\u00192\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\u00192\b\b\u0002\u0010\u001b\u001a\u00020\u00142\b\b\u0002\u0010\u001c\u001a\u00020\u00142\b\b\u0002\u0010\u001d\u001a\u00020\u00142\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u001f\u001a\u00020 2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010$\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010%\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u00142\b\b\u0002\u0010\'\u001a\u00020\u00052\n\b\u0002\u0010(\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010)\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010+\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010\u007fJ\u0015\u0010\u0080\u0001\u001a\u00020 2\t\u0010\u0081\u0001\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\n\u0010\u0082\u0001\u001a\u00020\u0014H\u00d6\u0001J\n\u0010\u0083\u0001\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010.R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010.R\u0011\u0010\n\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u0010.R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b3\u0010.R\u0011\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00101R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u0010.R\u0011\u0010\u0018\u001a\u00020\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00107R\u0011\u0010\u000f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010.R\u0011\u0010\'\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010.R\u0013\u0010%\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010.R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u0010<R\u0013\u0010\"\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010.R\u0011\u0010\u001f\u001a\u00020 \u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010>R\u0013\u0010\u001a\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u00107R\u0013\u0010$\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010.R\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010.R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u00101R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u0010.R\u0013\u0010#\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u0010.R\u0013\u0010\u0010\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010.R\u0015\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\n\n\u0002\u0010H\u001a\u0004\bF\u0010GR\u0013\u0010+\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u0010.R\u0013\u0010)\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u0010.R\u0011\u0010\r\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u00101R\u0011\u0010\u000e\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u00101R\u0015\u0010*\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010O\u001a\u0004\bM\u0010NR\u0011\u0010\u0016\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u0010.R\u0015\u0010&\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\n\n\u0002\u0010H\u001a\u0004\bQ\u0010GR\u0013\u0010!\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bR\u0010.R\u0013\u0010(\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bS\u0010.R\u0011\u0010\u001c\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\bT\u0010UR\u0011\u0010\u001d\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\bV\u0010UR\u0011\u0010\u001b\u001a\u00020\u0014\u00a2\u0006\b\n\u0000\u001a\u0004\bW\u0010UR\u0013\u0010\u001e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bX\u0010.R\u0015\u0010\u0015\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\n\n\u0002\u0010H\u001a\u0004\bY\u0010G\u00a8\u0006\u0084\u0001"}, d2 = {"Lcom/focusflow/data/model/AlternativeProduct;", "", "id", "", "originalProductName", "", "originalPrice", "", "originalCategory", "alternativeName", "alternativePrice", "alternativeCategory", "alternativeType", "savingsAmount", "savingsPercentage", "description", "pros", "cons", "availabilityInfo", "qualityRating", "", "userRating", "suggestionSource", "confidenceScore", "createdDate", "Lkotlinx/datetime/LocalDateTime;", "lastSuggested", "timesShown", "timesAccepted", "timesRejected", "userFeedback", "isActive", "", "tags", "imageUrl", "productUrl", "merchant", "estimatedDeliveryTime", "sustainabilityScore", "difficultyLevel", "timeInvestment", "requiredSkills", "successRate", "relatedAlternatives", "(JLjava/lang/String;DLjava/lang/String;Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;DDLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;DLkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;IIILjava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/String;)V", "getAlternativeCategory", "()Ljava/lang/String;", "getAlternativeName", "getAlternativePrice", "()D", "getAlternativeType", "getAvailabilityInfo", "getConfidenceScore", "getCons", "getCreatedDate", "()Lkotlinx/datetime/LocalDateTime;", "getDescription", "getDifficultyLevel", "getEstimatedDeliveryTime", "getId", "()J", "getImageUrl", "()Z", "getLastSuggested", "getMerchant", "getOriginalCategory", "getOriginalPrice", "getOriginalProductName", "getProductUrl", "getPros", "getQualityRating", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getRelatedAlternatives", "getRequiredSkills", "getSavingsAmount", "getSavingsPercentage", "getSuccessRate", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getSuggestionSource", "getSustainabilityScore", "getTags", "getTimeInvestment", "getTimesAccepted", "()I", "getTimesRejected", "getTimesShown", "getUserFeedback", "getUserRating", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;DLjava/lang/String;Ljava/lang/String;DLjava/lang/String;Ljava/lang/String;DDLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;DLkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;IIILjava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/String;)Lcom/focusflow/data/model/AlternativeProduct;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "alternative_products")
public final class AlternativeProduct {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String originalProductName = null;
    private final double originalPrice = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String originalCategory = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String alternativeName = null;
    private final double alternativePrice = 0.0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String alternativeCategory = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String alternativeType = null;
    private final double savingsAmount = 0.0;
    private final double savingsPercentage = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String description = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String pros = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String cons = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String availabilityInfo = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer qualityRating = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer userRating = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String suggestionSource = null;
    private final double confidenceScore = 0.0;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime createdDate = null;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime lastSuggested = null;
    private final int timesShown = 0;
    private final int timesAccepted = 0;
    private final int timesRejected = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String userFeedback = null;
    private final boolean isActive = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String tags = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String imageUrl = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String productUrl = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String merchant = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String estimatedDeliveryTime = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer sustainabilityScore = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String difficultyLevel = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String timeInvestment = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String requiredSkills = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double successRate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String relatedAlternatives = null;
    
    public AlternativeProduct(long id, @org.jetbrains.annotations.NotNull
    java.lang.String originalProductName, double originalPrice, @org.jetbrains.annotations.NotNull
    java.lang.String originalCategory, @org.jetbrains.annotations.NotNull
    java.lang.String alternativeName, double alternativePrice, @org.jetbrains.annotations.Nullable
    java.lang.String alternativeCategory, @org.jetbrains.annotations.NotNull
    java.lang.String alternativeType, double savingsAmount, double savingsPercentage, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String pros, @org.jetbrains.annotations.Nullable
    java.lang.String cons, @org.jetbrains.annotations.Nullable
    java.lang.String availabilityInfo, @org.jetbrains.annotations.Nullable
    java.lang.Integer qualityRating, @org.jetbrains.annotations.Nullable
    java.lang.Integer userRating, @org.jetbrains.annotations.NotNull
    java.lang.String suggestionSource, double confidenceScore, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime createdDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastSuggested, int timesShown, int timesAccepted, int timesRejected, @org.jetbrains.annotations.Nullable
    java.lang.String userFeedback, boolean isActive, @org.jetbrains.annotations.Nullable
    java.lang.String tags, @org.jetbrains.annotations.Nullable
    java.lang.String imageUrl, @org.jetbrains.annotations.Nullable
    java.lang.String productUrl, @org.jetbrains.annotations.Nullable
    java.lang.String merchant, @org.jetbrains.annotations.Nullable
    java.lang.String estimatedDeliveryTime, @org.jetbrains.annotations.Nullable
    java.lang.Integer sustainabilityScore, @org.jetbrains.annotations.NotNull
    java.lang.String difficultyLevel, @org.jetbrains.annotations.Nullable
    java.lang.String timeInvestment, @org.jetbrains.annotations.Nullable
    java.lang.String requiredSkills, @org.jetbrains.annotations.Nullable
    java.lang.Double successRate, @org.jetbrains.annotations.Nullable
    java.lang.String relatedAlternatives) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getOriginalProductName() {
        return null;
    }
    
    public final double getOriginalPrice() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getOriginalCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getAlternativeName() {
        return null;
    }
    
    public final double getAlternativePrice() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAlternativeCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getAlternativeType() {
        return null;
    }
    
    public final double getSavingsAmount() {
        return 0.0;
    }
    
    public final double getSavingsPercentage() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getPros() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCons() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAvailabilityInfo() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getQualityRating() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getUserRating() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSuggestionSource() {
        return null;
    }
    
    public final double getConfidenceScore() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getCreatedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getLastSuggested() {
        return null;
    }
    
    public final int getTimesShown() {
        return 0;
    }
    
    public final int getTimesAccepted() {
        return 0;
    }
    
    public final int getTimesRejected() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getUserFeedback() {
        return null;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getTags() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getImageUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getProductUrl() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getMerchant() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getEstimatedDeliveryTime() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getSustainabilityScore() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDifficultyLevel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getTimeInvestment() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getRequiredSkills() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getSuccessRate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getRelatedAlternatives() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final double component10() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component16() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component17() {
        return null;
    }
    
    public final double component18() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component20() {
        return null;
    }
    
    public final int component21() {
        return 0;
    }
    
    public final int component22() {
        return 0;
    }
    
    public final int component23() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component24() {
        return null;
    }
    
    public final boolean component25() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component26() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component27() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component28() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component29() {
        return null;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component30() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component31() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component32() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component33() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component34() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component35() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component36() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    public final double component6() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component8() {
        return null;
    }
    
    public final double component9() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.AlternativeProduct copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String originalProductName, double originalPrice, @org.jetbrains.annotations.NotNull
    java.lang.String originalCategory, @org.jetbrains.annotations.NotNull
    java.lang.String alternativeName, double alternativePrice, @org.jetbrains.annotations.Nullable
    java.lang.String alternativeCategory, @org.jetbrains.annotations.NotNull
    java.lang.String alternativeType, double savingsAmount, double savingsPercentage, @org.jetbrains.annotations.NotNull
    java.lang.String description, @org.jetbrains.annotations.Nullable
    java.lang.String pros, @org.jetbrains.annotations.Nullable
    java.lang.String cons, @org.jetbrains.annotations.Nullable
    java.lang.String availabilityInfo, @org.jetbrains.annotations.Nullable
    java.lang.Integer qualityRating, @org.jetbrains.annotations.Nullable
    java.lang.Integer userRating, @org.jetbrains.annotations.NotNull
    java.lang.String suggestionSource, double confidenceScore, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime createdDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastSuggested, int timesShown, int timesAccepted, int timesRejected, @org.jetbrains.annotations.Nullable
    java.lang.String userFeedback, boolean isActive, @org.jetbrains.annotations.Nullable
    java.lang.String tags, @org.jetbrains.annotations.Nullable
    java.lang.String imageUrl, @org.jetbrains.annotations.Nullable
    java.lang.String productUrl, @org.jetbrains.annotations.Nullable
    java.lang.String merchant, @org.jetbrains.annotations.Nullable
    java.lang.String estimatedDeliveryTime, @org.jetbrains.annotations.Nullable
    java.lang.Integer sustainabilityScore, @org.jetbrains.annotations.NotNull
    java.lang.String difficultyLevel, @org.jetbrains.annotations.Nullable
    java.lang.String timeInvestment, @org.jetbrains.annotations.Nullable
    java.lang.String requiredSkills, @org.jetbrains.annotations.Nullable
    java.lang.Double successRate, @org.jetbrains.annotations.Nullable
    java.lang.String relatedAlternatives) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}