package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate

@Entity(tableName = "income")
data class Income(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val source: String, // e.g., "Primary Job", "Side Hustle", "Benefits"
    val amount: Double,
    val frequency: String, // "weekly", "bi-weekly", "monthly", "yearly"
    val nextPayDate: LocalDate? = null,
    val isActive: Boolean = true,
    val description: String? = null
)
