package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\t\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0011\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\"\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJf\u0010\f\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\n2\u0006\u0010\u0012\u001a\u00020\n2\u0006\u0010\u0013\u001a\u00020\u000f2\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u000f2\b\b\u0002\u0010\u0017\u001a\u00020\u000f2\b\b\u0002\u0010\u0018\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0019J\u001e\u0010\u001a\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\n2\u0006\u0010\u001b\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u0010\u001cJ\u0016\u0010\u001d\u001a\u00020\u00062\u0006\u0010\u001e\u001a\u00020\u001fH\u0086@\u00a2\u0006\u0002\u0010 J\u0016\u0010!\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020#H\u0086@\u00a2\u0006\u0002\u0010$J\u0012\u0010%\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\'0&J\u0012\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\'0&J\u0010\u0010)\u001a\u0004\u0018\u00010\u000fH\u0086@\u00a2\u0006\u0002\u0010*J\u001c\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\'0&2\b\b\u0002\u0010,\u001a\u00020\u000fJ\u0018\u0010-\u001a\u0004\u0018\u00010#2\u0006\u0010\r\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010.J\u000e\u0010/\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010*J\u0012\u00100\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\'0&J\u0018\u00101\u001a\u0004\u0018\u00010#2\u0006\u0010\u0007\u001a\u00020\bH\u0086@\u00a2\u0006\u0002\u00102J\u001a\u00103\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\'0&2\u0006\u0010\u0011\u001a\u00020\nJ\u0012\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020#0\'0&J\u0016\u00105\u001a\u00020\b2\u0006\u0010\"\u001a\u00020#H\u0086@\u00a2\u0006\u0002\u0010$J\"\u00106\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\nH\u0086@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u00107\u001a\u00020\u00062\u0006\u0010\"\u001a\u00020#H\u0086@\u00a2\u0006\u0002\u0010$R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00068"}, d2 = {"Lcom/focusflow/data/repository/BudgetRecommendationRepository;", "", "budgetRecommendationDao", "Lcom/focusflow/data/dao/BudgetRecommendationDao;", "(Lcom/focusflow/data/dao/BudgetRecommendationDao;)V", "acceptRecommendation", "", "id", "", "feedback", "", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createRecommendation", "categoryName", "recommendedAmount", "", "currentAmount", "reasonCode", "reasonDescription", "confidenceScore", "basedOnDays", "", "seasonalFactor", "trendFactor", "varianceFactor", "(Ljava/lang/String;DDLjava/lang/String;Ljava/lang/String;DIDDDLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deactivateOldRecommendationsForCategory", "excludeId", "(Ljava/lang/String;JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldProcessedRecommendations", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteRecommendation", "recommendation", "Lcom/focusflow/data/model/BudgetRecommendation;", "(Lcom/focusflow/data/model/BudgetRecommendation;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAcceptedRecommendations", "Lkotlinx/coroutines/flow/Flow;", "", "getAllActiveRecommendations", "getAverageAcceptedConfidenceScore", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHighConfidenceRecommendations", "minConfidence", "getLatestRecommendationForCategory", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPendingRecommendationCount", "getPendingRecommendations", "getRecommendationById", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getRecommendationsByReason", "getRejectedRecommendations", "insertRecommendation", "rejectRecommendation", "updateRecommendation", "app_debug"})
public final class BudgetRecommendationRepository {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.BudgetRecommendationDao budgetRecommendationDao = null;
    
    @javax.inject.Inject
    public BudgetRecommendationRepository(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.BudgetRecommendationDao budgetRecommendationDao) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getAllActiveRecommendations() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getLatestRecommendationForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.BudgetRecommendation> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getPendingRecommendations() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getAcceptedRecommendations() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getRejectedRecommendations() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getRecommendationsByReason(@org.jetbrains.annotations.NotNull
    java.lang.String reasonCode) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetRecommendation>> getHighConfidenceRecommendations(double minConfidence) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getRecommendationById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.BudgetRecommendation> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getPendingRecommendationCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getAverageAcceptedConfidenceScore(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object insertRecommendation(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetRecommendation recommendation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateRecommendation(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetRecommendation recommendation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteRecommendation(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetRecommendation recommendation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object acceptRecommendation(long id, @org.jetbrains.annotations.Nullable
    java.lang.String feedback, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object rejectRecommendation(long id, @org.jetbrains.annotations.Nullable
    java.lang.String feedback, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deactivateOldRecommendationsForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, long excludeId, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteOldProcessedRecommendations(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object createRecommendation(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, double recommendedAmount, double currentAmount, @org.jetbrains.annotations.NotNull
    java.lang.String reasonCode, @org.jetbrains.annotations.NotNull
    java.lang.String reasonDescription, double confidenceScore, int basedOnDays, double seasonalFactor, double trendFactor, double varianceFactor, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
}