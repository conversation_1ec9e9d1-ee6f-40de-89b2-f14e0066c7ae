package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "ai_interactions")
data class AIInteraction(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val userMessage: String,
    val aiResponse: String,
    val interactionType: String, // "spending_analysis", "task_breakdown", "motivation", etc.
    val timestamp: LocalDateTime,
    val contextData: String? = null // JSON string of relevant context
)

