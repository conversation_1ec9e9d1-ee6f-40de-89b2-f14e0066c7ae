package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\bU\b\u0087\b\u0018\u00002\u00020\u0001B\u009f\u0002\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u000b\u0012\b\b\u0002\u0010\r\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u000e\u0012\b\b\u0002\u0010\u0010\u001a\u00020\b\u0012\b\b\u0002\u0010\u0011\u001a\u00020\b\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u000e\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\b\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u001d\u001a\u00020\b\u0012\b\b\u0002\u0010\u001e\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010 \u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0002\u0010!J\t\u0010C\u001a\u00020\u0003H\u00c6\u0003J\t\u0010D\u001a\u00020\bH\u00c6\u0003J\t\u0010E\u001a\u00020\bH\u00c6\u0003J\u0010\u0010F\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\u0010\u0010G\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\u000b\u0010H\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010I\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010J\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010K\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010L\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u00100J\u0010\u0010M\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\t\u0010N\u001a\u00020\u0005H\u00c6\u0003J\u0010\u0010O\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\u000b\u0010P\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010Q\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010R\u001a\u00020\bH\u00c6\u0003J\t\u0010S\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010T\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010U\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003\u00a2\u0006\u0002\u00100J\t\u0010V\u001a\u00020\u0005H\u00c6\u0003J\t\u0010W\u001a\u00020\bH\u00c6\u0003J\u0010\u0010X\u001a\u0004\u0018\u00010\bH\u00c6\u0003\u00a2\u0006\u0002\u0010#J\t\u0010Y\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003J\t\u0010[\u001a\u00020\u000eH\u00c6\u0003J\t\u0010\\\u001a\u00020\u000eH\u00c6\u0003J\u00b0\u0002\u0010]\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\b2\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u000f\u001a\u00020\u000e2\b\b\u0002\u0010\u0010\u001a\u00020\b2\b\b\u0002\u0010\u0011\u001a\u00020\b2\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u000e2\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u001a\u001a\u0004\u0018\u00010\b2\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u001d\u001a\u00020\b2\b\b\u0002\u0010\u001e\u001a\u00020\u00052\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010 \u001a\u0004\u0018\u00010\u000eH\u00c6\u0001\u00a2\u0006\u0002\u0010^J\u0013\u0010_\u001a\u00020\u000e2\b\u0010`\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010a\u001a\u00020\bH\u00d6\u0001J\t\u0010b\u001a\u00020\u0005H\u00d6\u0001R\u0015\u0010\t\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b\"\u0010#R\u0013\u0010\u001f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0011\u0010\u0011\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010(R\u0013\u0010\u0016\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010&R\u0013\u0010\f\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010+R\u0015\u0010\u001a\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b,\u0010#R\u0015\u0010\u0019\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b-\u0010#R\u0015\u0010\u0012\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b.\u0010#R\u0015\u0010\u0018\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\n\n\u0002\u00101\u001a\u0004\b/\u00100R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u00103R\u0011\u0010\u0010\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010(R\u0011\u0010\r\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u00105R\u0015\u0010 \u001a\u0004\u0018\u00010\u000e\u00a2\u0006\n\n\u0002\u00101\u001a\u0004\b \u00100R\u0013\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u0010&R\u0013\u0010\u001b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u0010&R\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u0010&R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b9\u0010(R\u0011\u0010\u001d\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u0010(R\u0015\u0010\u0013\u001a\u0004\u0018\u00010\b\u00a2\u0006\n\n\u0002\u0010$\u001a\u0004\b;\u0010#R\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010&R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b=\u0010&R\u0011\u0010\u001e\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u0010&R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u0010+R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u0010&R\u0013\u0010\u0015\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u0010&R\u0011\u0010\u000f\u001a\u00020\u000e\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u00105\u00a8\u0006c"}, d2 = {"Lcom/focusflow/data/model/FocusSession;", "", "id", "", "taskType", "", "sessionName", "plannedDurationMinutes", "", "actualDurationMinutes", "startTime", "Lkotlinx/datetime/LocalDateTime;", "endTime", "isCompleted", "", "wasInterrupted", "interruptionCount", "breaksTaken", "focusQuality", "productivityScore", "notes", "tasksCompleted", "distractions", "sessionGoal", "goalAchieved", "energyLevelBefore", "energyLevelAfter", "moodBefore", "moodAfter", "pomodoroCount", "sessionType", "backgroundSound", "isSuccessful", "(JLjava/lang/String;Ljava/lang/String;ILjava/lang/Integer;Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;ZZIILjava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)V", "getActualDurationMinutes", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getBackgroundSound", "()Ljava/lang/String;", "getBreaksTaken", "()I", "getDistractions", "getEndTime", "()Lkotlinx/datetime/LocalDateTime;", "getEnergyLevelAfter", "getEnergyLevelBefore", "getFocusQuality", "getGoalAchieved", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getId", "()J", "getInterruptionCount", "()Z", "getMoodAfter", "getMoodBefore", "getNotes", "getPlannedDurationMinutes", "getPomodoroCount", "getProductivityScore", "getSessionGoal", "getSessionName", "getSessionType", "getStartTime", "getTaskType", "getTasksCompleted", "getWasInterrupted", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;Ljava/lang/String;ILjava/lang/Integer;Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;ZZIILjava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;)Lcom/focusflow/data/model/FocusSession;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "focus_sessions")
public final class FocusSession {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String taskType = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String sessionName = null;
    private final int plannedDurationMinutes = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer actualDurationMinutes = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime startTime = null;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime endTime = null;
    private final boolean isCompleted = false;
    private final boolean wasInterrupted = false;
    private final int interruptionCount = 0;
    private final int breaksTaken = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer focusQuality = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer productivityScore = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String notes = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String tasksCompleted = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String distractions = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String sessionGoal = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean goalAchieved = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer energyLevelBefore = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer energyLevelAfter = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String moodBefore = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String moodAfter = null;
    private final int pomodoroCount = 0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String sessionType = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String backgroundSound = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean isSuccessful = null;
    
    public FocusSession(long id, @org.jetbrains.annotations.NotNull
    java.lang.String taskType, @org.jetbrains.annotations.NotNull
    java.lang.String sessionName, int plannedDurationMinutes, @org.jetbrains.annotations.Nullable
    java.lang.Integer actualDurationMinutes, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startTime, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime endTime, boolean isCompleted, boolean wasInterrupted, int interruptionCount, int breaksTaken, @org.jetbrains.annotations.Nullable
    java.lang.Integer focusQuality, @org.jetbrains.annotations.Nullable
    java.lang.Integer productivityScore, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.Nullable
    java.lang.String tasksCompleted, @org.jetbrains.annotations.Nullable
    java.lang.String distractions, @org.jetbrains.annotations.Nullable
    java.lang.String sessionGoal, @org.jetbrains.annotations.Nullable
    java.lang.Boolean goalAchieved, @org.jetbrains.annotations.Nullable
    java.lang.Integer energyLevelBefore, @org.jetbrains.annotations.Nullable
    java.lang.Integer energyLevelAfter, @org.jetbrains.annotations.Nullable
    java.lang.String moodBefore, @org.jetbrains.annotations.Nullable
    java.lang.String moodAfter, int pomodoroCount, @org.jetbrains.annotations.NotNull
    java.lang.String sessionType, @org.jetbrains.annotations.Nullable
    java.lang.String backgroundSound, @org.jetbrains.annotations.Nullable
    java.lang.Boolean isSuccessful) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTaskType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSessionName() {
        return null;
    }
    
    public final int getPlannedDurationMinutes() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getActualDurationMinutes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getStartTime() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getEndTime() {
        return null;
    }
    
    public final boolean isCompleted() {
        return false;
    }
    
    public final boolean getWasInterrupted() {
        return false;
    }
    
    public final int getInterruptionCount() {
        return 0;
    }
    
    public final int getBreaksTaken() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getFocusQuality() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getProductivityScore() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNotes() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getTasksCompleted() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getDistractions() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSessionGoal() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean getGoalAchieved() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getEnergyLevelBefore() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getEnergyLevelAfter() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getMoodBefore() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getMoodAfter() {
        return null;
    }
    
    public final int getPomodoroCount() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSessionType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getBackgroundSound() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean isSuccessful() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final int component10() {
        return 0;
    }
    
    public final int component11() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component20() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component22() {
        return null;
    }
    
    public final int component23() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component24() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component25() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component26() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component7() {
        return null;
    }
    
    public final boolean component8() {
        return false;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.FocusSession copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String taskType, @org.jetbrains.annotations.NotNull
    java.lang.String sessionName, int plannedDurationMinutes, @org.jetbrains.annotations.Nullable
    java.lang.Integer actualDurationMinutes, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startTime, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime endTime, boolean isCompleted, boolean wasInterrupted, int interruptionCount, int breaksTaken, @org.jetbrains.annotations.Nullable
    java.lang.Integer focusQuality, @org.jetbrains.annotations.Nullable
    java.lang.Integer productivityScore, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.Nullable
    java.lang.String tasksCompleted, @org.jetbrains.annotations.Nullable
    java.lang.String distractions, @org.jetbrains.annotations.Nullable
    java.lang.String sessionGoal, @org.jetbrains.annotations.Nullable
    java.lang.Boolean goalAchieved, @org.jetbrains.annotations.Nullable
    java.lang.Integer energyLevelBefore, @org.jetbrains.annotations.Nullable
    java.lang.Integer energyLevelAfter, @org.jetbrains.annotations.Nullable
    java.lang.String moodBefore, @org.jetbrains.annotations.Nullable
    java.lang.String moodAfter, int pomodoroCount, @org.jetbrains.annotations.NotNull
    java.lang.String sessionType, @org.jetbrains.annotations.Nullable
    java.lang.String backgroundSound, @org.jetbrains.annotations.Nullable
    java.lang.Boolean isSuccessful) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}