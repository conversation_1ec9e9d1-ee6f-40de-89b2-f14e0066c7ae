package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b;\b\u0087\b\u0018\u00002\u00020\u0001B\u00cb\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\t\u0012\u0006\u0010\u000b\u001a\u00020\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u0012\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u0014\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\t\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0011\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\t\u00a2\u0006\u0002\u0010\u001aJ\t\u00107\u001a\u00020\u0003H\u00c6\u0003J\u000b\u00108\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u0010\u00109\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003\u00a2\u0006\u0002\u0010,J\u0010\u0010:\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003\u00a2\u0006\u0002\u0010,J\u0010\u0010;\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003\u00a2\u0006\u0002\u0010\"J\u000b\u0010<\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u0010\u0010=\u001a\u0004\u0018\u00010\u0014H\u00c6\u0003\u00a2\u0006\u0002\u0010\"J\u000b\u0010>\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u0010\u0010?\u001a\u0004\u0018\u00010\u0011H\u00c6\u0003\u00a2\u0006\u0002\u0010,J\u000b\u0010@\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u0010\u0010A\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010&J\u0010\u0010B\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010&J\t\u0010C\u001a\u00020\u0007H\u00c6\u0003J\t\u0010D\u001a\u00020\tH\u00c6\u0003J\t\u0010E\u001a\u00020\tH\u00c6\u0003J\t\u0010F\u001a\u00020\fH\u00c6\u0003J\u000b\u0010G\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010H\u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u00dc\u0001\u0010I\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\t2\b\b\u0002\u0010\n\u001a\u00020\t2\b\b\u0002\u0010\u000b\u001a\u00020\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0015\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u0016\u001a\u0004\u0018\u00010\u00142\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00112\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\tH\u00c6\u0001\u00a2\u0006\u0002\u0010JJ\u0013\u0010K\u001a\u00020\u00142\b\u0010L\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010M\u001a\u00020\u0011H\u00d6\u0001J\t\u0010N\u001a\u00020\tH\u00d6\u0001R\u0013\u0010\u0015\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001eR\u0013\u0010\u0019\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001cR\u0011\u0010\b\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001cR\u0015\u0010\u0016\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b!\u0010\"R\u0013\u0010\r\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001cR\u0015\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\'\u001a\u0004\b%\u0010&R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010)R\u0011\u0010\n\u001a\u00020\t\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u001cR\u0015\u0010\u0018\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\n\n\u0002\u0010-\u001a\u0004\b+\u0010,R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\u001cR\u0013\u0010\u0017\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u001cR\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u00101R\u0015\u0010\u0012\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\n\n\u0002\u0010-\u001a\u0004\b2\u0010,R\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u0011\u00a2\u0006\n\n\u0002\u0010-\u001a\u0004\b3\u0010,R\u0013\u0010\u000e\u001a\u0004\u0018\u00010\t\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u0010\u001cR\u0015\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\n\n\u0002\u0010\'\u001a\u0004\b5\u0010&R\u0015\u0010\u0013\u001a\u0004\u0018\u00010\u0014\u00a2\u0006\n\n\u0002\u0010#\u001a\u0004\b6\u0010\"\u00a8\u0006O"}, d2 = {"Lcom/focusflow/data/model/SpendingReflection;", "", "id", "", "expenseId", "wishlistItemId", "amount", "", "category", "", "itemDescription", "reflectionDate", "Lkotlinx/datetime/LocalDateTime;", "emotionalState", "triggerReason", "needVsWant", "satisfactionLevel", "", "regretLevel", "wouldBuyAgain", "", "alternativeConsidered", "delayHelpful", "notes", "mindfulnessScore", "budgetImpact", "(JLjava/lang/Long;Ljava/lang/Long;DLjava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;)V", "getAlternativeConsidered", "()Ljava/lang/String;", "getAmount", "()D", "getBudgetImpact", "getCategory", "getDelayHelpful", "()Ljava/lang/Boolean;", "Ljava/lang/Boolean;", "getEmotionalState", "getExpenseId", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getId", "()J", "getItemDescription", "getMindfulnessScore", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getNeedVsWant", "getNotes", "getReflectionDate", "()Lkotlinx/datetime/LocalDateTime;", "getRegretLevel", "getSatisfactionLevel", "getTriggerReason", "getWishlistItemId", "getWouldBuyAgain", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/Long;Ljava/lang/Long;DLjava/lang/String;Ljava/lang/String;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;)Lcom/focusflow/data/model/SpendingReflection;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "spending_reflections")
public final class SpendingReflection {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long expenseId = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Long wishlistItemId = null;
    private final double amount = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String category = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String itemDescription = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime reflectionDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String emotionalState = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String triggerReason = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String needVsWant = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer satisfactionLevel = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer regretLevel = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean wouldBuyAgain = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String alternativeConsidered = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Boolean delayHelpful = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String notes = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer mindfulnessScore = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String budgetImpact = null;
    
    public SpendingReflection(long id, @org.jetbrains.annotations.Nullable
    java.lang.Long expenseId, @org.jetbrains.annotations.Nullable
    java.lang.Long wishlistItemId, double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.NotNull
    java.lang.String itemDescription, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime reflectionDate, @org.jetbrains.annotations.Nullable
    java.lang.String emotionalState, @org.jetbrains.annotations.Nullable
    java.lang.String triggerReason, @org.jetbrains.annotations.Nullable
    java.lang.String needVsWant, @org.jetbrains.annotations.Nullable
    java.lang.Integer satisfactionLevel, @org.jetbrains.annotations.Nullable
    java.lang.Integer regretLevel, @org.jetbrains.annotations.Nullable
    java.lang.Boolean wouldBuyAgain, @org.jetbrains.annotations.Nullable
    java.lang.String alternativeConsidered, @org.jetbrains.annotations.Nullable
    java.lang.Boolean delayHelpful, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.Nullable
    java.lang.Integer mindfulnessScore, @org.jetbrains.annotations.Nullable
    java.lang.String budgetImpact) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getExpenseId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long getWishlistItemId() {
        return null;
    }
    
    public final double getAmount() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCategory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getItemDescription() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getReflectionDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getEmotionalState() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getTriggerReason() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNeedVsWant() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getSatisfactionLevel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getRegretLevel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean getWouldBuyAgain() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAlternativeConsidered() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean getDelayHelpful() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getNotes() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getMindfulnessScore() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getBudgetImpact() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component14() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Boolean component15() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component16() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Long component3() {
        return null;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.SpendingReflection copy(long id, @org.jetbrains.annotations.Nullable
    java.lang.Long expenseId, @org.jetbrains.annotations.Nullable
    java.lang.Long wishlistItemId, double amount, @org.jetbrains.annotations.NotNull
    java.lang.String category, @org.jetbrains.annotations.NotNull
    java.lang.String itemDescription, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime reflectionDate, @org.jetbrains.annotations.Nullable
    java.lang.String emotionalState, @org.jetbrains.annotations.Nullable
    java.lang.String triggerReason, @org.jetbrains.annotations.Nullable
    java.lang.String needVsWant, @org.jetbrains.annotations.Nullable
    java.lang.Integer satisfactionLevel, @org.jetbrains.annotations.Nullable
    java.lang.Integer regretLevel, @org.jetbrains.annotations.Nullable
    java.lang.Boolean wouldBuyAgain, @org.jetbrains.annotations.Nullable
    java.lang.String alternativeConsidered, @org.jetbrains.annotations.Nullable
    java.lang.Boolean delayHelpful, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.Nullable
    java.lang.Integer mindfulnessScore, @org.jetbrains.annotations.Nullable
    java.lang.String budgetImpact) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}