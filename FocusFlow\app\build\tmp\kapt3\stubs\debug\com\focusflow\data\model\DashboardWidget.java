package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\bt\b\u0087\b\u0018\u00002\u00020\u0001B\u00a7\u0003\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\n\u0012\b\b\u0002\u0010\f\u001a\u00020\u0005\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u000e\u001a\u00020\b\u0012\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u0015\u001a\u00020\n\u0012\b\b\u0002\u0010\u0016\u001a\u00020\n\u0012\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\u001a\u001a\u00020\n\u0012\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010 \u001a\u00020\n\u0012\b\b\u0002\u0010!\u001a\u00020\n\u0012\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010#\u001a\u00020\b\u0012\b\b\u0002\u0010$\u001a\u00020\n\u0012\b\b\u0002\u0010%\u001a\u00020\n\u0012\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u0005\u0012\b\b\u0002\u0010\'\u001a\u00020\u0005\u0012\b\b\u0002\u0010(\u001a\u00020\u0005\u0012\b\b\u0002\u0010)\u001a\u00020\b\u0012\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u0005\u0012\u0006\u0010+\u001a\u00020\u0010\u0012\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u0010\u0012\n\b\u0002\u0010-\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010.J\t\u0010X\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010Y\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003J\u000b\u0010Z\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010[\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010\\\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010]\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010^\u001a\u00020\nH\u00c6\u0003J\t\u0010_\u001a\u00020\nH\u00c6\u0003J\u000b\u0010`\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010a\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010b\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010c\u001a\u00020\u0005H\u00c6\u0003J\t\u0010d\u001a\u00020\nH\u00c6\u0003J\u000b\u0010e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010g\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010h\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010i\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010j\u001a\u00020\nH\u00c6\u0003J\t\u0010k\u001a\u00020\nH\u00c6\u0003J\u000b\u0010l\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010m\u001a\u00020\bH\u00c6\u0003J\t\u0010n\u001a\u00020\u0005H\u00c6\u0003J\t\u0010o\u001a\u00020\nH\u00c6\u0003J\t\u0010p\u001a\u00020\nH\u00c6\u0003J\u000b\u0010q\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010r\u001a\u00020\u0005H\u00c6\u0003J\t\u0010s\u001a\u00020\u0005H\u00c6\u0003J\t\u0010t\u001a\u00020\bH\u00c6\u0003J\u000b\u0010u\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010v\u001a\u00020\u0010H\u00c6\u0003J\u000b\u0010w\u001a\u0004\u0018\u00010\u0010H\u00c6\u0003J\u000b\u0010x\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010y\u001a\u00020\bH\u00c6\u0003J\t\u0010z\u001a\u00020\nH\u00c6\u0003J\t\u0010{\u001a\u00020\nH\u00c6\u0003J\t\u0010|\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010}\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\t\u0010~\u001a\u00020\bH\u00c6\u0003J\u00b3\u0003\u0010\u007f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\u00052\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u000e\u001a\u00020\b2\n\b\u0002\u0010\u000f\u001a\u0004\u0018\u00010\u00102\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0012\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0013\u001a\u00020\u00052\n\b\u0002\u0010\u0014\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u0015\u001a\u00020\n2\b\b\u0002\u0010\u0016\u001a\u00020\n2\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0018\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0019\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\u001a\u001a\u00020\n2\n\b\u0002\u0010\u001b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001c\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001d\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001e\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u001f\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010 \u001a\u00020\n2\b\b\u0002\u0010!\u001a\u00020\n2\n\b\u0002\u0010\"\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010#\u001a\u00020\b2\b\b\u0002\u0010$\u001a\u00020\n2\b\b\u0002\u0010%\u001a\u00020\n2\n\b\u0002\u0010&\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010\'\u001a\u00020\u00052\b\b\u0002\u0010(\u001a\u00020\u00052\b\b\u0002\u0010)\u001a\u00020\b2\n\b\u0002\u0010*\u001a\u0004\u0018\u00010\u00052\b\b\u0002\u0010+\u001a\u00020\u00102\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u00102\n\b\u0002\u0010-\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0015\u0010\u0080\u0001\u001a\u00020\n2\t\u0010\u0081\u0001\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\n\u0010\u0082\u0001\u001a\u00020\bH\u00d6\u0001J\n\u0010\u0083\u0001\u001a\u00020\u0005H\u00d6\u0001R\u0013\u0010\u001f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u00100R\u0013\u0010\u001e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b1\u00100R\u0011\u0010\u001a\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b2\u00103R\u0011\u0010$\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b4\u00103R\u0013\u0010\u001b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b5\u00100R\u0011\u0010\u0013\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b6\u00100R\u0013\u0010\r\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b7\u00100R\u0011\u0010+\u001a\u00020\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\b8\u00109R\u0013\u0010\u0014\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b:\u00100R\u0013\u0010\u0012\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b;\u00100R\u0011\u0010#\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b<\u0010=R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b>\u00100R\u0013\u0010*\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b?\u00100R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b@\u00100R\u0013\u0010&\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bA\u00100R\u0013\u0010\u0018\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u00100R\u0013\u0010\u0017\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bC\u00100R\u0013\u0010\u0019\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bD\u00100R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\bE\u0010FR\u0011\u0010 \u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b \u00103R\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u00103R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u00103R\u0013\u0010\u000f\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\bG\u00109R\u0011\u0010\'\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u00100R\u0013\u0010\u001c\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u00100R\u0013\u0010,\u001a\u0004\u0018\u00010\u0010\u00a2\u0006\b\n\u0000\u001a\u0004\bJ\u00109R\u0011\u0010%\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bK\u00103R\u0013\u0010\"\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bL\u00100R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\bM\u0010=R\u0011\u0010)\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\bN\u0010=R\u0011\u0010\u000e\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\bO\u0010=R\u0011\u0010!\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bP\u00103R\u0011\u0010\u0016\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bQ\u00103R\u0011\u0010\u0015\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\bR\u00103R\u0011\u0010\f\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bS\u00100R\u0013\u0010\u001d\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bT\u00100R\u0011\u0010(\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bU\u00100R\u0013\u0010-\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bV\u00100R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\bW\u00100\u00a8\u0006\u0084\u0001"}, d2 = {"Lcom/focusflow/data/model/DashboardWidget;", "", "id", "", "widgetType", "", "displayName", "position", "", "isVisible", "", "isEnabled", "size", "configuration", "refreshInterval", "lastUpdated", "Lkotlinx/datetime/LocalDateTime;", "dataSource", "customTitle", "colorScheme", "customColors", "showHeader", "showFooter", "headerText", "footerText", "iconName", "animationEnabled", "clickAction", "longPressAction", "swipeActions", "accessibilityLabel", "accessibilityHint", "isCustomizable", "requiresPermission", "permissionType", "dataRetentionDays", "cacheEnabled", "offlineSupport", "errorFallback", "loadingIndicator", "updateAnimation", "priority", "dependencies", "createdDate", "modifiedDate", "userNotes", "(JLjava/lang/String;Ljava/lang/String;IZZLjava/lang/String;Ljava/lang/String;ILkotlinx/datetime/LocalDateTime;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZLjava/lang/String;IZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Lkotlinx/datetime/LocalDateTime;Lkotlinx/datetime/LocalDateTime;Ljava/lang/String;)V", "getAccessibilityHint", "()Ljava/lang/String;", "getAccessibilityLabel", "getAnimationEnabled", "()Z", "getCacheEnabled", "getClickAction", "getColorScheme", "getConfiguration", "getCreatedDate", "()Lkotlinx/datetime/LocalDateTime;", "getCustomColors", "getCustomTitle", "getDataRetentionDays", "()I", "getDataSource", "getDependencies", "getDisplayName", "getErrorFallback", "getFooterText", "getHeaderText", "getIconName", "getId", "()J", "getLastUpdated", "getLoadingIndicator", "getLongPressAction", "getModifiedDate", "getOfflineSupport", "getPermissionType", "getPosition", "getPriority", "getRefreshInterval", "getRequiresPermission", "getShowFooter", "getShowHeader", "getSize", "getSwipeActions", "getUpdateAnimation", "getUserNotes", "getWidgetType", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component37", "component38", "component39", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "dashboard_widgets")
public final class DashboardWidget {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String widgetType = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String displayName = null;
    private final int position = 0;
    private final boolean isVisible = false;
    private final boolean isEnabled = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String size = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String configuration = null;
    private final int refreshInterval = 0;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime lastUpdated = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String dataSource = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String customTitle = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String colorScheme = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String customColors = null;
    private final boolean showHeader = false;
    private final boolean showFooter = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String headerText = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String footerText = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String iconName = null;
    private final boolean animationEnabled = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String clickAction = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String longPressAction = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String swipeActions = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String accessibilityLabel = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String accessibilityHint = null;
    private final boolean isCustomizable = false;
    private final boolean requiresPermission = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String permissionType = null;
    private final int dataRetentionDays = 0;
    private final boolean cacheEnabled = false;
    private final boolean offlineSupport = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String errorFallback = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String loadingIndicator = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String updateAnimation = null;
    private final int priority = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String dependencies = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.datetime.LocalDateTime createdDate = null;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime modifiedDate = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String userNotes = null;
    
    public DashboardWidget(long id, @org.jetbrains.annotations.NotNull
    java.lang.String widgetType, @org.jetbrains.annotations.NotNull
    java.lang.String displayName, int position, boolean isVisible, boolean isEnabled, @org.jetbrains.annotations.NotNull
    java.lang.String size, @org.jetbrains.annotations.Nullable
    java.lang.String configuration, int refreshInterval, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastUpdated, @org.jetbrains.annotations.Nullable
    java.lang.String dataSource, @org.jetbrains.annotations.Nullable
    java.lang.String customTitle, @org.jetbrains.annotations.NotNull
    java.lang.String colorScheme, @org.jetbrains.annotations.Nullable
    java.lang.String customColors, boolean showHeader, boolean showFooter, @org.jetbrains.annotations.Nullable
    java.lang.String headerText, @org.jetbrains.annotations.Nullable
    java.lang.String footerText, @org.jetbrains.annotations.Nullable
    java.lang.String iconName, boolean animationEnabled, @org.jetbrains.annotations.Nullable
    java.lang.String clickAction, @org.jetbrains.annotations.Nullable
    java.lang.String longPressAction, @org.jetbrains.annotations.Nullable
    java.lang.String swipeActions, @org.jetbrains.annotations.Nullable
    java.lang.String accessibilityLabel, @org.jetbrains.annotations.Nullable
    java.lang.String accessibilityHint, boolean isCustomizable, boolean requiresPermission, @org.jetbrains.annotations.Nullable
    java.lang.String permissionType, int dataRetentionDays, boolean cacheEnabled, boolean offlineSupport, @org.jetbrains.annotations.Nullable
    java.lang.String errorFallback, @org.jetbrains.annotations.NotNull
    java.lang.String loadingIndicator, @org.jetbrains.annotations.NotNull
    java.lang.String updateAnimation, int priority, @org.jetbrains.annotations.Nullable
    java.lang.String dependencies, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime createdDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime modifiedDate, @org.jetbrains.annotations.Nullable
    java.lang.String userNotes) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getWidgetType() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getDisplayName() {
        return null;
    }
    
    public final int getPosition() {
        return 0;
    }
    
    public final boolean isVisible() {
        return false;
    }
    
    public final boolean isEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getSize() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getConfiguration() {
        return null;
    }
    
    public final int getRefreshInterval() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getLastUpdated() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getDataSource() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCustomTitle() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getColorScheme() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getCustomColors() {
        return null;
    }
    
    public final boolean getShowHeader() {
        return false;
    }
    
    public final boolean getShowFooter() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getHeaderText() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getFooterText() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getIconName() {
        return null;
    }
    
    public final boolean getAnimationEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getClickAction() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getLongPressAction() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getSwipeActions() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAccessibilityLabel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getAccessibilityHint() {
        return null;
    }
    
    public final boolean isCustomizable() {
        return false;
    }
    
    public final boolean getRequiresPermission() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getPermissionType() {
        return null;
    }
    
    public final int getDataRetentionDays() {
        return 0;
    }
    
    public final boolean getCacheEnabled() {
        return false;
    }
    
    public final boolean getOfflineSupport() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getErrorFallback() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getLoadingIndicator() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getUpdateAnimation() {
        return null;
    }
    
    public final int getPriority() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getDependencies() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime getCreatedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getModifiedDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getUserNotes() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component11() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component14() {
        return null;
    }
    
    public final boolean component15() {
        return false;
    }
    
    public final boolean component16() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component17() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component18() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component19() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component20() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component21() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component22() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component23() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component24() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component25() {
        return null;
    }
    
    public final boolean component26() {
        return false;
    }
    
    public final boolean component27() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component28() {
        return null;
    }
    
    public final int component29() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component30() {
        return false;
    }
    
    public final boolean component31() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component32() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component33() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component34() {
        return null;
    }
    
    public final int component35() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component36() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.datetime.LocalDateTime component37() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component38() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component39() {
        return null;
    }
    
    public final int component4() {
        return 0;
    }
    
    public final boolean component5() {
        return false;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component8() {
        return null;
    }
    
    public final int component9() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.DashboardWidget copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String widgetType, @org.jetbrains.annotations.NotNull
    java.lang.String displayName, int position, boolean isVisible, boolean isEnabled, @org.jetbrains.annotations.NotNull
    java.lang.String size, @org.jetbrains.annotations.Nullable
    java.lang.String configuration, int refreshInterval, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastUpdated, @org.jetbrains.annotations.Nullable
    java.lang.String dataSource, @org.jetbrains.annotations.Nullable
    java.lang.String customTitle, @org.jetbrains.annotations.NotNull
    java.lang.String colorScheme, @org.jetbrains.annotations.Nullable
    java.lang.String customColors, boolean showHeader, boolean showFooter, @org.jetbrains.annotations.Nullable
    java.lang.String headerText, @org.jetbrains.annotations.Nullable
    java.lang.String footerText, @org.jetbrains.annotations.Nullable
    java.lang.String iconName, boolean animationEnabled, @org.jetbrains.annotations.Nullable
    java.lang.String clickAction, @org.jetbrains.annotations.Nullable
    java.lang.String longPressAction, @org.jetbrains.annotations.Nullable
    java.lang.String swipeActions, @org.jetbrains.annotations.Nullable
    java.lang.String accessibilityLabel, @org.jetbrains.annotations.Nullable
    java.lang.String accessibilityHint, boolean isCustomizable, boolean requiresPermission, @org.jetbrains.annotations.Nullable
    java.lang.String permissionType, int dataRetentionDays, boolean cacheEnabled, boolean offlineSupport, @org.jetbrains.annotations.Nullable
    java.lang.String errorFallback, @org.jetbrains.annotations.NotNull
    java.lang.String loadingIndicator, @org.jetbrains.annotations.NotNull
    java.lang.String updateAnimation, int priority, @org.jetbrains.annotations.Nullable
    java.lang.String dependencies, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime createdDate, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime modifiedDate, @org.jetbrains.annotations.Nullable
    java.lang.String userNotes) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}