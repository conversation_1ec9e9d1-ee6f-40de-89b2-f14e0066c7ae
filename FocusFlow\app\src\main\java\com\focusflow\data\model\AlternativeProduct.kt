package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "alternative_products")
data class AlternativeProduct(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val originalProductName: String,
    val originalPrice: Double,
    val originalCategory: String,
    val alternativeName: String,
    val alternativePrice: Double,
    val alternativeCategory: String? = null,
    val alternativeType: String, // "cheaper", "free", "delayed", "substitute"
    val savingsAmount: Double, // originalPrice - alternativePrice
    val savingsPercentage: Double,
    val description: String,
    val pros: String? = null, // JSON array of advantages
    val cons: String? = null, // JSON array of disadvantages
    val availabilityInfo: String? = null,
    val qualityRating: Int? = null, // 1-5 scale
    val userRating: Int? = null, // 1-5 scale if user tried it
    val suggestionSource: String, // "ai", "user", "community", "database"
    val confidenceScore: Double, // 0.0 to 1.0
    val createdDate: LocalDateTime,
    val lastSuggested: LocalDateTime? = null,
    val timesShown: Int = 0,
    val timesAccepted: Int = 0,
    val timesRejected: Int = 0,
    val userFeedback: String? = null,
    val isActive: Boolean = true,
    val tags: String? = null, // JSON array of tags
    val imageUrl: String? = null,
    val productUrl: String? = null,
    val merchant: String? = null,
    val estimatedDeliveryTime: String? = null,
    val sustainabilityScore: Int? = null, // 1-5 scale for eco-friendliness
    val difficultyLevel: String = "easy", // "easy", "moderate", "difficult"
    val timeInvestment: String? = null, // Time needed to implement alternative
    val requiredSkills: String? = null, // JSON array of skills needed
    val successRate: Double? = null, // Historical success rate for this alternative
    val relatedAlternatives: String? = null // JSON array of related alternative IDs
)
