package com.focusflow.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/focusflow/ui/theme/ThemeMode;", "", "(Ljava/lang/String;I)V", "SYSTEM", "LIGHT", "DARK", "HIGH_CONTRAST_LIGHT", "HIGH_CONTRAST_DARK", "app_debug"})
public enum ThemeMode {
    /*public static final*/ SYSTEM /* = new SYSTEM() */,
    /*public static final*/ LIGHT /* = new LIGHT() */,
    /*public static final*/ DARK /* = new DARK() */,
    /*public static final*/ HIGH_CONTRAST_LIGHT /* = new HIGH_CONTRAST_LIGHT() */,
    /*public static final*/ HIGH_CONTRAST_DARK /* = new HIGH_CONTRAST_DARK() */;
    
    ThemeMode() {
    }
    
    @org.jetbrains.annotations.NotNull
    public static kotlin.enums.EnumEntries<com.focusflow.ui.theme.ThemeMode> getEntries() {
        return null;
    }
}