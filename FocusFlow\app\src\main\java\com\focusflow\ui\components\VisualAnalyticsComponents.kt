package com.focusflow.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import kotlin.math.max
import kotlin.math.min

@Composable
fun SpendingTrendChart(
    data: List<SpendingDataPoint>,
    title: String,
    modifier: Modifier = Modifier,
    showGrid: Boolean = true,
    animateEntry: Boolean = true
) {
    var animationProgress by remember { mutableStateOf(if (animateEntry) 0f else 1f) }
    
    LaunchedEffect(data) {
        if (animateEntry) {
            animationProgress = 1f
        }
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (data.isNotEmpty()) {
                Canvas(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                ) {
                    drawSpendingChart(
                        data = data,
                        animationProgress = animationProgress,
                        showGrid = showGrid
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Legend
                SpendingChartLegend(data = data)
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun CategorySpendingPieChart(
    categories: List<CategorySpending>,
    title: String,
    modifier: Modifier = Modifier
) {
    val total = categories.sumOf { it.amount }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (categories.isNotEmpty() && total > 0) {
                Row {
                    // Pie chart
                    Canvas(
                        modifier = Modifier
                            .size(120.dp)
                            .weight(1f)
                    ) {
                        drawPieChart(categories, total)
                    }
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    // Legend
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        categories.forEachIndexed { index, category ->
                            CategoryLegendItem(
                                category = category,
                                color = getCategoryColor(index),
                                percentage = (category.amount / total * 100).toInt()
                            )
                            if (index < categories.size - 1) {
                                Spacer(modifier = Modifier.height(8.dp))
                            }
                        }
                    }
                }
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(120.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No spending data",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun SpendingHeatmap(
    heatmapData: List<List<Double>>,
    dayLabels: List<String>,
    hourLabels: List<String>,
    title: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            if (heatmapData.isNotEmpty()) {
                Canvas(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                ) {
                    drawHeatmap(heatmapData, dayLabels, hourLabels)
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Intensity legend
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Less",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                    
                    Row {
                        repeat(5) { intensity ->
                            Box(
                                modifier = Modifier
                                    .size(12.dp)
                                    .background(
                                        color = getHeatmapColor(intensity / 4f),
                                        shape = RoundedCornerShape(2.dp)
                                    )
                            )
                            if (intensity < 4) Spacer(modifier = Modifier.width(2.dp))
                        }
                    }
                    
                    Text(
                        text = "More",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
                    )
                }
            }
        }
    }
}

@Composable
fun BudgetVarianceChart(
    budgetData: List<BudgetVarianceData>,
    title: String,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            budgetData.forEach { data ->
                BudgetVarianceBar(
                    categoryName = data.categoryName,
                    budgetAmount = data.budgetAmount,
                    actualAmount = data.actualAmount,
                    modifier = Modifier.fillMaxWidth()
                )
                Spacer(modifier = Modifier.height(12.dp))
            }
        }
    }
}

@Composable
private fun BudgetVarianceBar(
    categoryName: String,
    budgetAmount: Double,
    actualAmount: Double,
    modifier: Modifier = Modifier
) {
    val variance = actualAmount - budgetAmount
    val variancePercentage = if (budgetAmount > 0) (variance / budgetAmount * 100) else 0.0
    val isOverBudget = variance > 0
    
    Column(modifier = modifier) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = categoryName,
                style = MaterialTheme.typography.subtitle2,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = "${if (isOverBudget) "+" else ""}${String.format("%.1f", variancePercentage)}%",
                style = MaterialTheme.typography.caption,
                color = if (isOverBudget) Color(0xFFF44336) else Color(0xFF4CAF50),
                fontWeight = FontWeight.Medium
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .background(
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.1f),
                    shape = RoundedCornerShape(4.dp)
                )
        ) {
            val progress = min(actualAmount / max(budgetAmount, actualAmount), 1.0).toFloat()
            
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(progress)
                    .background(
                        color = if (isOverBudget) Color(0xFFF44336) else Color(0xFF4CAF50),
                        shape = RoundedCornerShape(4.dp)
                    )
            )
        }
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "$${String.format("%.0f", actualAmount)}",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
            
            Text(
                text = "Budget: $${String.format("%.0f", budgetAmount)}",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun CategoryLegendItem(
    category: CategorySpending,
    color: Color,
    percentage: Int
) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color, RoundedCornerShape(2.dp))
        )
        
        Spacer(modifier = Modifier.width(8.dp))
        
        Column {
            Text(
                text = category.name,
                style = MaterialTheme.typography.caption,
                fontWeight = FontWeight.Medium
            )
            Text(
                text = "$${String.format("%.0f", category.amount)} ($percentage%)",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
private fun SpendingChartLegend(data: List<SpendingDataPoint>) {
    // Simple legend showing date range
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = data.firstOrNull()?.label ?: "",
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
        )
        
        Text(
            text = data.lastOrNull()?.label ?: "",
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.6f)
        )
    }
}

// Drawing functions
private fun DrawScope.drawSpendingChart(
    data: List<SpendingDataPoint>,
    animationProgress: Float,
    showGrid: Boolean
) {
    if (data.isEmpty()) return
    
    val maxValue = data.maxOfOrNull { it.value } ?: 0.0
    val minValue = data.minOfOrNull { it.value } ?: 0.0
    val range = maxValue - minValue
    
    if (range <= 0) return
    
    val stepX = size.width / (data.size - 1).coerceAtLeast(1)
    val chartColor = Color(0xFF2196F3)
    
    // Draw grid if enabled
    if (showGrid) {
        val gridColor = Color.Gray.copy(alpha = 0.2f)
        repeat(5) { i ->
            val y = size.height * i / 4
            drawLine(
                color = gridColor,
                start = androidx.compose.ui.geometry.Offset(0f, y),
                end = androidx.compose.ui.geometry.Offset(size.width, y),
                strokeWidth = 1.dp.toPx()
            )
        }
    }
    
    // Draw line chart
    val path = Path()
    data.forEachIndexed { index, point ->
        val x = index * stepX
        val y = size.height - ((point.value - minValue) / range * size.height).toFloat()
        
        if (index == 0) {
            path.moveTo(x, y)
        } else {
            path.lineTo(x, y)
        }
    }
    
    drawPath(
        path = path,
        color = chartColor,
        style = Stroke(width = 3.dp.toPx())
    )
}

private fun DrawScope.drawPieChart(categories: List<CategorySpending>, total: Double) {
    var startAngle = -90f
    
    categories.forEachIndexed { index, category ->
        val sweepAngle = (category.amount / total * 360).toFloat()
        val color = getCategoryColor(index)
        
        drawArc(
            color = color,
            startAngle = startAngle,
            sweepAngle = sweepAngle,
            useCenter = true,
            topLeft = androidx.compose.ui.geometry.Offset(
                size.width * 0.1f,
                size.height * 0.1f
            ),
            size = androidx.compose.ui.geometry.Size(
                size.width * 0.8f,
                size.height * 0.8f
            )
        )
        
        startAngle += sweepAngle
    }
}

private fun DrawScope.drawHeatmap(
    data: List<List<Double>>,
    dayLabels: List<String>,
    hourLabels: List<String>
) {
    if (data.isEmpty()) return
    
    val maxValue = data.flatten().maxOrNull() ?: 1.0
    val cellWidth = size.width / data[0].size
    val cellHeight = size.height / data.size
    
    data.forEachIndexed { dayIndex, dayData ->
        dayData.forEachIndexed { hourIndex, value ->
            val intensity = (value / maxValue).toFloat()
            val color = getHeatmapColor(intensity)
            
            drawRect(
                color = color,
                topLeft = androidx.compose.ui.geometry.Offset(
                    hourIndex * cellWidth,
                    dayIndex * cellHeight
                ),
                size = androidx.compose.ui.geometry.Size(cellWidth, cellHeight)
            )
        }
    }
}

private fun getCategoryColor(index: Int): Color {
    val colors = listOf(
        Color(0xFF2196F3), Color(0xFF4CAF50), Color(0xFFFF9800),
        Color(0xFFF44336), Color(0xFF9C27B0), Color(0xFF00BCD4),
        Color(0xFF8BC34A), Color(0xFFFFEB3B), Color(0xFF795548)
    )
    return colors[index % colors.size]
}

private fun getHeatmapColor(intensity: Float): Color {
    return Color(0xFF2196F3).copy(alpha = 0.2f + intensity * 0.8f)
}

// Data classes
data class SpendingDataPoint(
    val label: String,
    val value: Double
)

data class CategorySpending(
    val name: String,
    val amount: Double
)

data class BudgetVarianceData(
    val categoryName: String,
    val budgetAmount: Double,
    val actualAmount: Double
)
