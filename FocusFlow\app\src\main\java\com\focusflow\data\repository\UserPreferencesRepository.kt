package com.focusflow.data.repository

import com.focusflow.data.dao.UserPreferencesDao
import com.focusflow.data.model.UserPreferences
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class UserPreferencesRepository @Inject constructor(
    private val userPreferencesDao: UserPreferencesDao
) {
    fun getUserPreferences(): Flow<UserPreferences?> = userPreferencesDao.getUserPreferences()

    suspend fun getUserPreferencesSync(): UserPreferences? = userPreferencesDao.getUserPreferencesSync()

    suspend fun insertUserPreferences(preferences: UserPreferences) = 
        userPreferencesDao.insertUserPreferences(preferences)

    suspend fun updateUserPreferences(preferences: UserPreferences) = 
        userPreferencesDao.updateUserPreferences(preferences)

    suspend fun updateBudgetPeriod(period: String) = 
        userPreferencesDao.updateBudgetPeriod(period)

    suspend fun updateNotificationsEnabled(enabled: Boolean) = 
        userPreferencesDao.updateNotificationsEnabled(enabled)

    suspend fun updateDarkModeEnabled(enabled: Boolean) = 
        userPreferencesDao.updateDarkModeEnabled(enabled)

    suspend fun updateFontSize(fontSize: String) =
        userPreferencesDao.updateFontSize(fontSize)

    suspend fun updateThemePreference(themePreference: String) =
        userPreferencesDao.updateThemePreference(themePreference)

    suspend fun updateFontScale(fontScale: Float) =
        userPreferencesDao.updateFontScale(fontScale)

    suspend fun updateHighContrastMode(enabled: Boolean) =
        userPreferencesDao.updateHighContrastMode(enabled)

    suspend fun updateVoiceInputEnabled(enabled: Boolean) =
        userPreferencesDao.updateVoiceInputEnabled(enabled)

    suspend fun updateAnimationsEnabled(enabled: Boolean) =
        userPreferencesDao.updateAnimationsEnabled(enabled)
}

