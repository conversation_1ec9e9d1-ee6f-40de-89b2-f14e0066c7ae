package com.focusflow.service

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.focusflow.data.dao.VoiceCommandDao
import com.focusflow.data.model.VoiceCommand
import com.focusflow.utils.ErrorHandler
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class VoiceInputService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val voiceCommandDao: VoiceCommandDao
) {
    private var speechRecognizer: SpeechRecognizer? = null
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    
    private val _isListening = MutableLiveData(false)
    val isListening: LiveData<Boolean> = _isListening
    
    private val _recognizedText = MutableLiveData<String>()
    val recognizedText: LiveData<String> = _recognizedText
    
    private val _voiceCommandResult = MutableLiveData<VoiceCommandResult>()
    val voiceCommandResult: LiveData<VoiceCommandResult> = _voiceCommandResult
    
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error
    
    data class VoiceCommandResult(
        val command: String,
        val intent: String,
        val parameters: Map<String, Any>,
        val confidence: Double,
        val isSuccessful: Boolean,
        val actionTaken: String? = null,
        val followUpRequired: Boolean = false,
        val followUpPrompt: String? = null
    )
    
    fun startListening() {
        if (!SpeechRecognizer.isRecognitionAvailable(context)) {
            _error.value = "Speech recognition not available on this device"
            return
        }
        
        try {
            speechRecognizer?.destroy()
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
            speechRecognizer?.setRecognitionListener(recognitionListener)
            
            val intent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                putExtra(RecognizerIntent.EXTRA_PROMPT, "Say a command...")
                putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
                putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
            }
            
            speechRecognizer?.startListening(intent)
            _isListening.value = true
            _error.value = null
            
        } catch (e: Exception) {
            ErrorHandler.logError("Failed to start voice recognition", e)
            _error.value = "Failed to start voice recognition: ${e.message}"
        }
    }
    
    fun stopListening() {
        speechRecognizer?.stopListening()
        _isListening.value = false
    }
    
    private val recognitionListener = object : RecognitionListener {
        override fun onReadyForSpeech(params: Bundle?) {
            _isListening.value = true
        }
        
        override fun onBeginningOfSpeech() {
            // Speech input detected
        }
        
        override fun onRmsChanged(rmsdB: Float) {
            // Audio level changed - could be used for visual feedback
        }
        
        override fun onBufferReceived(buffer: ByteArray?) {
            // Audio buffer received
        }
        
        override fun onEndOfSpeech() {
            _isListening.value = false
        }
        
        override fun onError(error: Int) {
            _isListening.value = false
            val errorMessage = when (error) {
                SpeechRecognizer.ERROR_AUDIO -> "Audio recording error"
                SpeechRecognizer.ERROR_CLIENT -> "Client side error"
                SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "Insufficient permissions"
                SpeechRecognizer.ERROR_NETWORK -> "Network error"
                SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "Network timeout"
                SpeechRecognizer.ERROR_NO_MATCH -> "No speech input matched"
                SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "Recognition service busy"
                SpeechRecognizer.ERROR_SERVER -> "Server error"
                SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "No speech input"
                else -> "Unknown error"
            }
            _error.value = errorMessage
        }
        
        override fun onResults(results: Bundle?) {
            _isListening.value = false
            val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
            val confidences = results?.getFloatArray(SpeechRecognizer.CONFIDENCE_SCORES)
            
            if (!matches.isNullOrEmpty()) {
                val recognizedText = matches[0]
                val confidence = confidences?.get(0)?.toDouble() ?: 0.0
                
                _recognizedText.value = recognizedText
                processVoiceCommand(recognizedText, confidence)
            }
        }
        
        override fun onPartialResults(partialResults: Bundle?) {
            val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
            if (!matches.isNullOrEmpty()) {
                _recognizedText.value = matches[0]
            }
        }
        
        override fun onEvent(eventType: Int, params: Bundle?) {
            // Handle recognition events
        }
    }
    
    private fun processVoiceCommand(text: String, confidence: Double) {
        coroutineScope.launch {
            try {
                val result = parseVoiceCommand(text, confidence)
                _voiceCommandResult.postValue(result)
                
                // Save command to database
                val voiceCommand = VoiceCommand(
                    commandText = text,
                    recognizedText = text,
                    commandType = result.intent,
                    intent = result.intent,
                    parameters = result.parameters.toString(),
                    isSuccessful = result.isSuccessful,
                    confidence = confidence,
                    processingTime = System.currentTimeMillis(),
                    timestamp = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()),
                    actionTaken = result.actionTaken,
                    followUpRequired = result.followUpRequired,
                    followUpPrompt = result.followUpPrompt
                )
                
                voiceCommandDao.insertVoiceCommand(voiceCommand)
                
            } catch (e: Exception) {
                ErrorHandler.logError("Failed to process voice command", e)
                _error.postValue("Failed to process voice command: ${e.message}")
            }
        }
    }
    
    private fun parseVoiceCommand(text: String, confidence: Double): VoiceCommandResult {
        val lowerText = text.lowercase().trim()
        
        return when {
            // Expense logging commands
            lowerText.contains("add expense") || lowerText.contains("spent") || lowerText.contains("bought") -> {
                parseExpenseCommand(lowerText, confidence)
            }
            
            // Budget checking commands
            lowerText.contains("budget") && (lowerText.contains("check") || lowerText.contains("how much")) -> {
                parseBudgetCheckCommand(lowerText, confidence)
            }
            
            // Navigation commands
            lowerText.contains("go to") || lowerText.contains("open") || lowerText.contains("show") -> {
                parseNavigationCommand(lowerText, confidence)
            }
            
            // Focus timer commands
            lowerText.contains("start timer") || lowerText.contains("focus") && lowerText.contains("timer") -> {
                parseFocusTimerCommand(lowerText, confidence)
            }
            
            // Task management commands
            lowerText.contains("add task") || lowerText.contains("remind me") -> {
                parseTaskCommand(lowerText, confidence)
            }
            
            else -> {
                VoiceCommandResult(
                    command = text,
                    intent = "unknown",
                    parameters = emptyMap(),
                    confidence = confidence,
                    isSuccessful = false,
                    followUpRequired = true,
                    followUpPrompt = "I didn't understand that command. Try saying 'add expense', 'check budget', or 'start timer'."
                )
            }
        }
    }
    
    private fun parseExpenseCommand(text: String, confidence: Double): VoiceCommandResult {
        // Extract amount and category from expense command
        val amountRegex = Regex("""\$?(\d+(?:\.\d{2})?)""")
        val amount = amountRegex.find(text)?.groupValues?.get(1)?.toDoubleOrNull()
        
        val category = when {
            text.contains("food") || text.contains("lunch") || text.contains("dinner") -> "Food & Dining"
            text.contains("gas") || text.contains("fuel") -> "Transportation"
            text.contains("coffee") || text.contains("drink") -> "Food & Dining"
            text.contains("shopping") || text.contains("store") -> "Shopping"
            else -> "Other"
        }
        
        return VoiceCommandResult(
            command = text,
            intent = "expense_entry",
            parameters = mapOf(
                "amount" to (amount ?: 0.0),
                "category" to category,
                "description" to text
            ),
            confidence = confidence,
            isSuccessful = amount != null,
            actionTaken = if (amount != null) "Expense logged" else null,
            followUpRequired = amount == null,
            followUpPrompt = if (amount == null) "How much did you spend?" else null
        )
    }
    
    private fun parseBudgetCheckCommand(text: String, confidence: Double): VoiceCommandResult {
        val category = when {
            text.contains("food") -> "Food & Dining"
            text.contains("transport") -> "Transportation"
            text.contains("shopping") -> "Shopping"
            else -> "total"
        }
        
        return VoiceCommandResult(
            command = text,
            intent = "budget_check",
            parameters = mapOf("category" to category),
            confidence = confidence,
            isSuccessful = true,
            actionTaken = "Budget information retrieved"
        )
    }
    
    private fun parseNavigationCommand(text: String, confidence: Double): VoiceCommandResult {
        val destination = when {
            text.contains("dashboard") || text.contains("home") -> "dashboard"
            text.contains("expense") -> "expenses"
            text.contains("budget") -> "budget"
            text.contains("debt") -> "debt"
            text.contains("task") -> "tasks"
            text.contains("habit") -> "habits"
            text.contains("coach") || text.contains("ai") -> "ai_coach"
            text.contains("setting") -> "settings"
            else -> "unknown"
        }
        
        return VoiceCommandResult(
            command = text,
            intent = "navigation",
            parameters = mapOf("destination" to destination),
            confidence = confidence,
            isSuccessful = destination != "unknown",
            actionTaken = if (destination != "unknown") "Navigated to $destination" else null,
            followUpRequired = destination == "unknown",
            followUpPrompt = if (destination == "unknown") "Which screen would you like to go to?" else null
        )
    }
    
    private fun parseFocusTimerCommand(text: String, confidence: Double): VoiceCommandResult {
        val minutesRegex = Regex("""(\d+)\s*(?:minute|min)""")
        val minutes = minutesRegex.find(text)?.groupValues?.get(1)?.toIntOrNull() ?: 25
        
        return VoiceCommandResult(
            command = text,
            intent = "focus_timer",
            parameters = mapOf("minutes" to minutes),
            confidence = confidence,
            isSuccessful = true,
            actionTaken = "Focus timer started for $minutes minutes"
        )
    }
    
    private fun parseTaskCommand(text: String, confidence: Double): VoiceCommandResult {
        val taskDescription = text.replace(Regex("add task|remind me to?"), "").trim()
        
        return VoiceCommandResult(
            command = text,
            intent = "task_add",
            parameters = mapOf("description" to taskDescription),
            confidence = confidence,
            isSuccessful = taskDescription.isNotEmpty(),
            actionTaken = if (taskDescription.isNotEmpty()) "Task added" else null,
            followUpRequired = taskDescription.isEmpty(),
            followUpPrompt = if (taskDescription.isEmpty()) "What task would you like to add?" else null
        )
    }
    
    fun destroy() {
        speechRecognizer?.destroy()
        speechRecognizer = null
        _isListening.value = false
    }
}
