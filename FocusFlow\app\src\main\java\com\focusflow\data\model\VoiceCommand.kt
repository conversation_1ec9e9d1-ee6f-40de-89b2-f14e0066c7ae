package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "voice_commands")
data class VoiceCommand(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val commandText: String, // The spoken command
    val recognizedText: String, // What the speech recognition detected
    val commandType: String, // "expense_entry", "budget_check", "task_add", "navigation"
    val intent: String, // Parsed intent from the command
    val parameters: String? = null, // JSON object with extracted parameters
    val isSuccessful: Boolean = false,
    val confidence: Double, // Speech recognition confidence 0.0-1.0
    val processingTime: Long, // Milliseconds to process
    val timestamp: LocalDateTime,
    val userId: String? = null,
    val sessionId: String? = null, // For grouping related commands
    val context: String? = null, // App context when command was issued
    val followUpRequired: Boolean = false,
    val followUpPrompt: String? = null,
    val errorMessage: String? = null,
    val correctedCommand: String? = null, // If user corrected the recognition
    val actionTaken: String? = null, // What action was performed
    val resultData: String? = null, // JSON with results of the action
    val userFeedback: String? = null, // "helpful", "not_helpful", "incorrect"
    val language: String = "en", // Language code
    val accent: String? = null, // Detected accent if available
    val noiseLevel: String? = null, // "low", "medium", "high"
    val deviceType: String? = null, // "phone", "tablet", "watch"
    val isOffline: Boolean = false, // Was processed offline
    val retryCount: Int = 0, // Number of times user retried
    val alternativeCommands: String? = null, // JSON array of alternative interpretations
    val learningData: String? = null, // Data for improving recognition
    val privacyLevel: String = "standard", // "minimal", "standard", "detailed"
    val isTrainingData: Boolean = false, // Can be used for model training
    val customVocabulary: String? = null, // JSON array of user-specific terms
    val shortcuts: String? = null // JSON array of user-defined shortcuts
)
