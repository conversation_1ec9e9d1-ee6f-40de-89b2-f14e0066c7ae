package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0014\n\u0002\u0018\u0002\n\u0002\b\r\bg\u0018\u00002\u00020\u0001J&\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0010\u0010\u0012\u001a\u0004\u0018\u00010\rH\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00160\u0015H\'J\u0018\u0010\u0017\u001a\u0004\u0018\u00010\u00182\u0006\u0010\u0019\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010\u001e\u001a\u0004\u0018\u00010\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u0018H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0014\u0010 \u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00160\u0015H\'J\u000e\u0010!\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0018\u0010\"\u001a\u0004\u0018\u00010\r2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010#J$\u0010$\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00160\u00152\u0006\u0010%\u001a\u00020\u00072\u0006\u0010&\u001a\u00020\u0007H\'J\u001c\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00160\u00152\u0006\u0010\u0019\u001a\u00020\u001aH\'J\u001c\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\r0\u00160\u00152\u0006\u0010)\u001a\u00020\u001aH\'J\u000e\u0010*\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u001c\u0010+\u001a\b\u0012\u0004\u0012\u00020\r0\u00162\u0006\u0010,\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010-J\u0014\u0010.\u001a\b\u0012\u0004\u0012\u00020/0\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0016\u00100\u001a\u00020\t2\u0006\u0010%\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u000e\u00101\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0010\u00102\u001a\u0004\u0018\u00010\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0016\u00103\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010#J\u0016\u00104\u001a\u00020\u00052\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u00105\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010#J\u0016\u00106\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010#J\u0016\u00107\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ*\u00108\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\b\u00109\u001a\u0004\u0018\u00010\t2\b\u0010:\u001a\u0004\u0018\u00010\tH\u00a7@\u00a2\u0006\u0002\u0010;\u00a8\u0006<"}, d2 = {"Lcom/focusflow/data/dao/FocusSessionDao;", "", "completeFocusSession", "", "id", "", "endTime", "Lkotlinx/datetime/LocalDateTime;", "duration", "", "(JLkotlinx/datetime/LocalDateTime;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFocusSession", "focusSession", "Lcom/focusflow/data/model/FocusSession;", "(Lcom/focusflow/data/model/FocusSession;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteFocusSessionsOlderThan", "cutoffDate", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveFocusSession", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllFocusSessions", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageDurationByTaskType", "", "taskType", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAverageFocusQuality", "getAverageInterruptionCount", "getAverageProductivityScore", "getAverageSessionDuration", "getCompletedFocusSessions", "getCompletedSessionCount", "getFocusSessionById", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getFocusSessionsByDateRange", "startDate", "endDate", "getFocusSessionsByTaskType", "getFocusSessionsByType", "sessionType", "getInterruptedSessionCount", "getRecentCompletedSessions", "limit", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSessionCountByTaskType", "Lcom/focusflow/data/dao/TaskTypeCount;", "getSessionCountSince", "getSuccessfulSessionCount", "getTotalFocusTime", "incrementPomodoroCount", "insertFocusSession", "recordBreak", "recordInterruption", "updateFocusSession", "updateSessionRatings", "quality", "productivity", "(JLjava/lang/Integer;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao
public abstract interface FocusSessionDao {
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions ORDER BY startTime DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.FocusSession>> getAllFocusSessions();
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions WHERE taskType = :taskType ORDER BY startTime DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.FocusSession>> getFocusSessionsByTaskType(@org.jetbrains.annotations.NotNull
    java.lang.String taskType);
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions WHERE isCompleted = 1 ORDER BY startTime DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.FocusSession>> getCompletedFocusSessions();
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions WHERE isCompleted = 0 AND endTime IS NULL ORDER BY startTime DESC LIMIT 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getActiveFocusSession(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.FocusSession> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions WHERE startTime >= :startDate AND startTime <= :endDate ORDER BY startTime DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.FocusSession>> getFocusSessionsByDateRange(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime endDate);
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getFocusSessionById(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.FocusSession> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM focus_sessions WHERE isCompleted = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCompletedSessionCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(actualDurationMinutes) FROM focus_sessions WHERE isCompleted = 1 AND actualDurationMinutes IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageSessionDuration(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(focusQuality) FROM focus_sessions WHERE focusQuality IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageFocusQuality(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(productivityScore) FROM focus_sessions WHERE productivityScore IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageProductivityScore(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT SUM(actualDurationMinutes) FROM focus_sessions WHERE isCompleted = 1 AND actualDurationMinutes IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getTotalFocusTime(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM focus_sessions WHERE isCompleted = 1 AND startTime >= :startDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getSessionCountSince(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime startDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions WHERE isCompleted = 1 ORDER BY startTime DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getRecentCompletedSessions(int limit, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.FocusSession>> $completion);
    
    @androidx.room.Query(value = "SELECT taskType, COUNT(*) as count FROM focus_sessions WHERE isCompleted = 1 GROUP BY taskType ORDER BY count DESC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getSessionCountByTaskType(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.dao.TaskTypeCount>> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(actualDurationMinutes) FROM focus_sessions WHERE taskType = :taskType AND isCompleted = 1 AND actualDurationMinutes IS NOT NULL")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageDurationByTaskType(@org.jetbrains.annotations.NotNull
    java.lang.String taskType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM focus_sessions WHERE wasInterrupted = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getInterruptedSessionCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(interruptionCount) FROM focus_sessions WHERE isCompleted = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageInterruptionCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM focus_sessions WHERE sessionType = :sessionType ORDER BY startTime DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.FocusSession>> getFocusSessionsByType(@org.jetbrains.annotations.NotNull
    java.lang.String sessionType);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM focus_sessions WHERE isSuccessful = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getSuccessfulSessionCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertFocusSession(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.FocusSession focusSession, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateFocusSession(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.FocusSession focusSession, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteFocusSession(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.FocusSession focusSession, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE focus_sessions SET endTime = :endTime, actualDurationMinutes = :duration, isCompleted = 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object completeFocusSession(long id, @org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime endTime, int duration, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE focus_sessions SET wasInterrupted = 1, interruptionCount = interruptionCount + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordInterruption(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE focus_sessions SET breaksTaken = breaksTaken + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object recordBreak(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE focus_sessions SET focusQuality = :quality, productivityScore = :productivity WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateSessionRatings(long id, @org.jetbrains.annotations.Nullable
    java.lang.Integer quality, @org.jetbrains.annotations.Nullable
    java.lang.Integer productivity, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE focus_sessions SET pomodoroCount = pomodoroCount + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object incrementPomodoroCount(long id, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM focus_sessions WHERE startTime < :cutoffDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteFocusSessionsOlderThan(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}