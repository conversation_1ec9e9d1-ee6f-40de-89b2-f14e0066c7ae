package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.HabitLog
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate

@Dao
interface HabitLogDao {
    @Query("SELECT * FROM habit_logs WHERE habitType = :habitType ORDER BY date DESC")
    fun getHabitLogsByType(habitType: String): Flow<List<HabitLog>>

    @Query("SELECT * FROM habit_logs WHERE date = :date ORDER BY habitType ASC")
    fun getHabitLogsForDate(date: LocalDate): Flow<List<HabitLog>>

    @Query("SELECT * FROM habit_logs WHERE date >= :startDate AND date <= :endDate ORDER BY date DESC, habitType ASC")
    fun getHabitLogsByDateRange(startDate: LocalDate, endDate: LocalDate): Flow<List<HabitLog>>

    @Query("SELECT * FROM habit_logs WHERE habitType = :habitType AND date >= :startDate AND date <= :endDate ORDER BY date DESC")
    fun getHabitLogsByTypeAndDateRange(habitType: String, startDate: LocalDate, endDate: LocalDate): Flow<List<HabitLog>>

    @Query("SELECT COUNT(*) FROM habit_logs WHERE habitType = :habitType AND date >= :startDate AND date <= :endDate")
    suspend fun getHabitStreakCount(habitType: String, startDate: LocalDate, endDate: LocalDate): Int

    @Query("SELECT DISTINCT habitType FROM habit_logs ORDER BY habitType")
    fun getAllHabitTypes(): Flow<List<String>>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertHabitLog(habitLog: HabitLog): Long

    @Update
    suspend fun updateHabitLog(habitLog: HabitLog)

    @Delete
    suspend fun deleteHabitLog(habitLog: HabitLog)

    @Query("DELETE FROM habit_logs WHERE habitType = :habitType AND date = :date")
    suspend fun deleteHabitLogByTypeAndDate(habitType: String, date: LocalDate)
}

