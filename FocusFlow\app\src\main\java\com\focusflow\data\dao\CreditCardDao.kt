package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.CreditCard
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate

@Dao
interface CreditCardDao {
    @Query("SELECT * FROM credit_cards WHERE isActive = 1 ORDER BY dueDate ASC")
    fun getAllActiveCreditCards(): Flow<List<CreditCard>>

    @Query("SELECT * FROM credit_cards WHERE id = :cardId")
    suspend fun getCreditCardById(cardId: Long): CreditCard?

    @Query("SELECT SUM(currentBalance) FROM credit_cards WHERE isActive = 1")
    suspend fun getTotalDebt(): Double?

    @Query("SELECT SUM(minimumPayment) FROM credit_cards WHERE isActive = 1 AND dueDate <= :date")
    suspend fun getTotalMinimumPaymentsDue(date: LocalDate): Double?

    @Query("SELECT * FROM credit_cards WHERE isActive = 1 AND dueDate <= :date ORDER BY dueDate ASC")
    fun getCardsWithPaymentsDue(date: LocalDate): Flow<List<CreditCard>>

    @Insert
    suspend fun insertCreditCard(creditCard: CreditCard): Long

    @Update
    suspend fun updateCreditCard(creditCard: CreditCard)

    @Delete
    suspend fun deleteCreditCard(creditCard: CreditCard)

    @Query("UPDATE credit_cards SET isActive = 0 WHERE id = :cardId")
    suspend fun deactivateCreditCard(cardId: Long)
}

