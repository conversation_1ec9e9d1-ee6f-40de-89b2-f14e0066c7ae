package com.focusflow.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000&\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\u001a%\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\u0011\u0010\t\u001a\r\u0012\u0004\u0012\u00020\u00060\n\u00a2\u0006\u0002\b\u000bH\u0007\u001a%\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\f\u001a\u00020\r2\u0011\u0010\t\u001a\r\u0012\u0004\u0012\u00020\u00060\n\u00a2\u0006\u0002\b\u000bH\u0007\"\u000e\u0010\u0000\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0002\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0003\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\"\u000e\u0010\u0004\u001a\u00020\u0001X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"DarkColorPalette", "Landroidx/compose/material/Colors;", "HighContrastDarkPalette", "HighContrastLightPalette", "LightColorPalette", "FocusFlowTheme", "", "themeMode", "Lcom/focusflow/ui/theme/ThemeMode;", "content", "Lkotlin/Function0;", "Landroidx/compose/runtime/Composable;", "darkTheme", "", "app_debug"})
public final class ThemeKt {
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.material.Colors DarkColorPalette = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.material.Colors LightColorPalette = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.material.Colors HighContrastLightPalette = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.material.Colors HighContrastDarkPalette = null;
    
    @androidx.compose.runtime.Composable
    public static final void FocusFlowTheme(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.theme.ThemeMode themeMode, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void FocusFlowTheme(boolean darkTheme, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
}