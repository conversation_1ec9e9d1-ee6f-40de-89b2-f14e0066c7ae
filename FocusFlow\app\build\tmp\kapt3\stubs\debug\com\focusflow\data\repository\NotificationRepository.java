package com.focusflow.data.repository;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\t\n\u0002\b\f\n\u0002\u0010\u0006\n\u0002\b\u0010\b\u0007\u0018\u00002\u00020\u0001B!\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\t\u001a\u00020\nJ\u001a\u0010\u000b\u001a\u000e\u0012\u0004\u0012\u00020\r\u0012\u0004\u0012\u00020\r0\f2\u0006\u0010\u000e\u001a\u00020\u000fJ\u0006\u0010\u0010\u001a\u00020\u0011J\u000e\u0010\u0012\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0013J\u0006\u0010\u0014\u001a\u00020\u0011J0\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\u000f2\u0006\u0010\u0017\u001a\u00020\u000f2\u0006\u0010\u0018\u001a\u00020\u00192\b\b\u0002\u0010\u001a\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u001bJ\u0018\u0010\u001c\u001a\u00020\n2\u0006\u0010\u001d\u001a\u00020\r2\u0006\u0010\u001e\u001a\u00020\rH\u0002J\u000e\u0010\u001f\u001a\u00020\nH\u0086@\u00a2\u0006\u0002\u0010\u0013J\u0016\u0010 \u001a\u00020\n2\u0006\u0010!\u001a\u00020\u000f2\u0006\u0010\"\u001a\u00020\u000fJ\u0016\u0010#\u001a\u00020\n2\u0006\u0010$\u001a\u00020\u000f2\u0006\u0010%\u001a\u00020&J\u001e\u0010\'\u001a\u00020\n2\u0006\u0010(\u001a\u00020\u000f2\u0006\u0010)\u001a\u00020&2\u0006\u0010*\u001a\u00020\rJ\u0016\u0010+\u001a\u00020\n2\u0006\u0010,\u001a\u00020\u000f2\u0006\u0010-\u001a\u00020\rJ\u0016\u0010.\u001a\u00020\n2\u0006\u0010)\u001a\u00020&2\u0006\u0010/\u001a\u00020\u000fJ\u0016\u00100\u001a\u00020\n2\u0006\u0010\u001a\u001a\u00020\u000f2\u0006\u00101\u001a\u00020\u000fJ\u001e\u00102\u001a\u00020\n2\u0006\u00103\u001a\u00020\u00112\u0006\u00104\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u00105R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u00066"}, d2 = {"Lcom/focusflow/data/repository/NotificationRepository;", "", "context", "Landroid/content/Context;", "userPreferencesRepository", "Lcom/focusflow/data/repository/UserPreferencesRepository;", "notificationManager", "Lcom/focusflow/service/FocusFlowNotificationManager;", "(Landroid/content/Context;Lcom/focusflow/data/repository/UserPreferencesRepository;Lcom/focusflow/service/FocusFlowNotificationManager;)V", "cancelAllNotifications", "", "getOptimalNotificationTime", "Lkotlin/Pair;", "", "userPreferredTime", "", "hasNotificationPermission", "", "initializeNotifications", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isGoodTimeForNotification", "scheduleCustomNotification", "title", "message", "triggerTimeMillis", "", "notificationType", "(Ljava/lang/String;Ljava/lang/String;JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "scheduleDefaultHabitReminders", "hour", "minute", "setupAllNotifications", "showAchievementNotification", "achievementTitle", "description", "showBudgetWarning", "categoryName", "percentageUsed", "", "showDebtPaymentReminder", "cardName", "amount", "daysUntilDue", "showHabitStreakCelebration", "habitName", "streakDays", "showSpendingMilestone", "period", "trackNotificationInteraction", "action", "updateNotificationSettings", "enabled", "time", "(ZLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class NotificationRepository {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.FocusFlowNotificationManager notificationManager = null;
    
    @javax.inject.Inject
    public NotificationRepository(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    com.focusflow.data.repository.UserPreferencesRepository userPreferencesRepository, @org.jetbrains.annotations.NotNull
    com.focusflow.service.FocusFlowNotificationManager notificationManager) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object initializeNotifications(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object setupAllNotifications(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void scheduleDefaultHabitReminders(int hour, int minute) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateNotificationSettings(boolean enabled, @org.jetbrains.annotations.NotNull
    java.lang.String time, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    public final boolean hasNotificationPermission() {
        return false;
    }
    
    public final void showAchievementNotification(@org.jetbrains.annotations.NotNull
    java.lang.String achievementTitle, @org.jetbrains.annotations.NotNull
    java.lang.String description) {
    }
    
    public final void showBudgetWarning(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, double percentageUsed) {
    }
    
    public final void showSpendingMilestone(double amount, @org.jetbrains.annotations.NotNull
    java.lang.String period) {
    }
    
    public final void showDebtPaymentReminder(@org.jetbrains.annotations.NotNull
    java.lang.String cardName, double amount, int daysUntilDue) {
    }
    
    public final void showHabitStreakCelebration(@org.jetbrains.annotations.NotNull
    java.lang.String habitName, int streakDays) {
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object scheduleCustomNotification(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String message, long triggerTimeMillis, @org.jetbrains.annotations.NotNull
    java.lang.String notificationType, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    public final void cancelAllNotifications() {
    }
    
    public final void trackNotificationInteraction(@org.jetbrains.annotations.NotNull
    java.lang.String notificationType, @org.jetbrains.annotations.NotNull
    java.lang.String action) {
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlin.Pair<java.lang.Integer, java.lang.Integer> getOptimalNotificationTime(@org.jetbrains.annotations.NotNull
    java.lang.String userPreferredTime) {
        return null;
    }
    
    public final boolean isGoodTimeForNotification() {
        return false;
    }
}