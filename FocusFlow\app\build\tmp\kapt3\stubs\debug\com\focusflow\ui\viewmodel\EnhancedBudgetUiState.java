package com.focusflow.ui.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u001b\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001Bs\u0012\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u0012\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u0012\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u0012\b\b\u0002\u0010\t\u001a\u00020\n\u0012\b\b\u0002\u0010\u000b\u001a\u00020\n\u0012\b\b\u0002\u0010\f\u001a\u00020\r\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u000f\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0002\u0010\u0012J\u000f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u000f\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003H\u00c6\u0003J\u000f\u0010 \u001a\b\u0012\u0004\u0012\u00020\b0\u0003H\u00c6\u0003J\t\u0010!\u001a\u00020\nH\u00c6\u0003J\t\u0010\"\u001a\u00020\nH\u00c6\u0003J\t\u0010#\u001a\u00020\rH\u00c6\u0003J\t\u0010$\u001a\u00020\u000fH\u00c6\u0003J\t\u0010%\u001a\u00020\u000fH\u00c6\u0003J\u000b\u0010&\u001a\u0004\u0018\u00010\rH\u00c6\u0003Jw\u0010\'\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u000e\b\u0002\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u00032\u000e\b\u0002\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u00032\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\n2\b\b\u0002\u0010\f\u001a\u00020\r2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\b\b\u0002\u0010\u0010\u001a\u00020\u000f2\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\rH\u00c6\u0001J\u0013\u0010(\u001a\u00020\u000f2\b\u0010)\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010*\u001a\u00020+H\u00d6\u0001J\t\u0010,\u001a\u00020\rH\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0016R\u0017\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\b0\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0014R\u0011\u0010\u0010\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0019R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0019R\u0017\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00060\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0014R\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0011\u0010\u000b\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u001c\u00a8\u0006-"}, d2 = {"Lcom/focusflow/ui/viewmodel/EnhancedBudgetUiState;", "", "budgetCategories", "", "Lcom/focusflow/data/model/BudgetCategory;", "pendingRecommendations", "Lcom/focusflow/data/model/BudgetRecommendation;", "insights", "Lcom/focusflow/ui/components/BudgetInsight;", "totalBudget", "", "totalSpent", "budgetPeriod", "", "isLoading", "", "isGeneratingRecommendations", "error", "(Ljava/util/List;Ljava/util/List;Ljava/util/List;DDLjava/lang/String;ZZLjava/lang/String;)V", "getBudgetCategories", "()Ljava/util/List;", "getBudgetPeriod", "()Ljava/lang/String;", "getError", "getInsights", "()Z", "getPendingRecommendations", "getTotalBudget", "()D", "getTotalSpent", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class EnhancedBudgetUiState {
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.data.model.BudgetRecommendation> pendingRecommendations = null;
    @org.jetbrains.annotations.NotNull
    private final java.util.List<com.focusflow.ui.components.BudgetInsight> insights = null;
    private final double totalBudget = 0.0;
    private final double totalSpent = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String budgetPeriod = null;
    private final boolean isLoading = false;
    private final boolean isGeneratingRecommendations = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String error = null;
    
    public EnhancedBudgetUiState(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetRecommendation> pendingRecommendations, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.components.BudgetInsight> insights, double totalBudget, double totalSpent, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, boolean isLoading, boolean isGeneratingRecommendations, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetCategory> getBudgetCategories() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetRecommendation> getPendingRecommendations() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.ui.components.BudgetInsight> getInsights() {
        return null;
    }
    
    public final double getTotalBudget() {
        return 0.0;
    }
    
    public final double getTotalSpent() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getBudgetPeriod() {
        return null;
    }
    
    public final boolean isLoading() {
        return false;
    }
    
    public final boolean isGeneratingRecommendations() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getError() {
        return null;
    }
    
    public EnhancedBudgetUiState() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetCategory> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.data.model.BudgetRecommendation> component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.ui.components.BudgetInsight> component3() {
        return null;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    public final double component5() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component6() {
        return null;
    }
    
    public final boolean component7() {
        return false;
    }
    
    public final boolean component8() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.ui.viewmodel.EnhancedBudgetUiState copy(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetCategory> budgetCategories, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.BudgetRecommendation> pendingRecommendations, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.components.BudgetInsight> insights, double totalBudget, double totalSpent, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, boolean isLoading, boolean isGeneratingRecommendations, @org.jetbrains.annotations.Nullable
    java.lang.String error) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}