package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "achievements")
data class Achievement(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val type: String, // "expense_logging", "budget_adherence", "debt_payment", "streak"
    val title: String,
    val description: String,
    val iconEmoji: String,
    val pointsAwarded: Int,
    val isUnlocked: Boolean = false,
    val unlockedAt: LocalDateTime? = null,
    val targetValue: Int? = null, // For progress-based achievements
    val currentProgress: Int = 0
)

@Entity(tableName = "user_stats")
data class UserStats(
    @PrimaryKey
    val id: Long = 1,
    val totalPoints: Int = 0,
    val currentLevel: Int = 1,
    val expenseLoggingStreak: Int = 0,
    val budgetAdherenceStreak: Int = 0,
    val totalExpensesLogged: Int = 0,
    val totalDebtPaid: Double = 0.0,
    val achievementsUnlocked: Int = 0,
    val lastActivityDate: LocalDateTime? = null
)

@Entity(tableName = "virtual_pet")
@TypeConverters(StringListConverter::class)
data class VirtualPet(
    @PrimaryKey
    val id: Long = 1,
    val name: String = "Buddy",
    val type: String = "cat", // "cat", "dog", "bird", etc.
    val level: Int = 1,
    val happiness: Int = 100, // 0-100
    val health: Int = 100, // 0-100
    val experience: Int = 0,
    val lastFed: LocalDateTime? = null,
    val lastPlayed: LocalDateTime? = null,
    val accessories: List<String> = emptyList() // Unlocked accessories
)

class StringListConverter {
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return value.joinToString(",")
    }

    @TypeConverter
    fun toStringList(value: String): List<String> {
        return if (value.isEmpty()) emptyList() else value.split(",")
    }
}
