package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0002\b\u0006\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bH\'J\u0010\u0010\n\u001a\u0004\u0018\u00010\tH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0011\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0013\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0017\u001a\u00020\u00032\u0006\u0010\u0014\u001a\u00020\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u0016J\u0016\u0010\u0018\u001a\u00020\u00032\u0006\u0010\u0019\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u001a\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u000e\u00a8\u0006\u001b"}, d2 = {"Lcom/focusflow/data/dao/VirtualPetDao;", "", "addExperience", "", "exp", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVirtualPet", "Lkotlinx/coroutines/flow/Flow;", "Lcom/focusflow/data/model/VirtualPet;", "getVirtualPetSync", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertVirtualPet", "virtualPet", "(Lcom/focusflow/data/model/VirtualPet;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateHappiness", "happiness", "updateHealth", "health", "updateLastFed", "timestamp", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateLastPlayed", "updateLevel", "level", "updateVirtualPet", "app_debug"})
@androidx.room.Dao
public abstract interface VirtualPetDao {
    
    @androidx.room.Query(value = "SELECT * FROM virtual_pet WHERE id = 1")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<com.focusflow.data.model.VirtualPet> getVirtualPet();
    
    @androidx.room.Query(value = "SELECT * FROM virtual_pet WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getVirtualPetSync(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.VirtualPet> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertVirtualPet(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.VirtualPet virtualPet, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateVirtualPet(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.VirtualPet virtualPet, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE virtual_pet SET happiness = :happiness WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateHappiness(int happiness, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE virtual_pet SET health = :health WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateHealth(int health, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE virtual_pet SET experience = experience + :exp WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object addExperience(int exp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE virtual_pet SET level = :level WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateLevel(int level, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE virtual_pet SET lastFed = :timestamp WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateLastFed(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE virtual_pet SET lastPlayed = :timestamp WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateLastPlayed(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime timestamp, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}