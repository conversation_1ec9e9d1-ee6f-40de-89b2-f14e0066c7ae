package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.BudgetAnalytics
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface BudgetAnalyticsDao {
    
    @Query("SELECT * FROM budget_analytics ORDER BY calculatedDate DESC")
    fun getAllAnalytics(): Flow<List<BudgetAnalytics>>
    
    @Query("SELECT * FROM budget_analytics WHERE categoryName = :categoryName ORDER BY calculatedDate DESC")
    fun getAnalyticsByCategory(categoryName: String): Flow<List<BudgetAnalytics>>
    
    @Query("SELECT * FROM budget_analytics WHERE budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month ORDER BY categoryName")
    fun getAnalyticsForPeriod(period: String, year: Int, month: Int?): Flow<List<BudgetAnalytics>>
    
    @Query("SELECT * FROM budget_analytics WHERE categoryName = :categoryName AND budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month AND budgetWeek = :week")
    suspend fun getAnalyticsForCategoryAndPeriod(
        categoryName: String, 
        period: String, 
        year: Int, 
        month: Int?, 
        week: Int?
    ): BudgetAnalytics?
    
    @Query("SELECT * FROM budget_analytics WHERE variancePercentage > :threshold ORDER BY variancePercentage DESC")
    fun getHighVarianceAnalytics(threshold: Double): Flow<List<BudgetAnalytics>>
    
    @Query("SELECT * FROM budget_analytics WHERE trendDirection = :direction ORDER BY calculatedDate DESC")
    fun getAnalyticsByTrend(direction: String): Flow<List<BudgetAnalytics>>
    
    @Query("SELECT * FROM budget_analytics WHERE isOutlierPeriod = 1 ORDER BY calculatedDate DESC")
    fun getOutlierPeriods(): Flow<List<BudgetAnalytics>>
    
    @Query("SELECT categoryName, AVG(variancePercentage) as avgVariance FROM budget_analytics GROUP BY categoryName ORDER BY avgVariance DESC")
    suspend fun getCategoryVarianceAverages(): List<CategoryVarianceAverage>
    
    @Query("SELECT AVG(variancePercentage) FROM budget_analytics WHERE categoryName = :categoryName")
    suspend fun getAverageVarianceForCategory(categoryName: String): Double?
    
    @Query("SELECT * FROM budget_analytics WHERE categoryName = :categoryName ORDER BY calculatedDate DESC LIMIT :limit")
    suspend fun getRecentAnalyticsForCategory(categoryName: String, limit: Int): List<BudgetAnalytics>
    
    @Query("SELECT COUNT(*) FROM budget_analytics WHERE variancePercentage > 20")
    suspend fun getHighVarianceCount(): Int
    
    @Query("SELECT COUNT(*) FROM budget_analytics WHERE trendDirection = 'increasing'")
    suspend fun getIncreasingTrendCount(): Int
    
    @Insert
    suspend fun insertAnalytics(analytics: BudgetAnalytics): Long
    
    @Update
    suspend fun updateAnalytics(analytics: BudgetAnalytics)
    
    @Delete
    suspend fun deleteAnalytics(analytics: BudgetAnalytics)
    
    @Query("DELETE FROM budget_analytics WHERE calculatedDate < :cutoffDate")
    suspend fun deleteAnalyticsOlderThan(cutoffDate: LocalDateTime)
    
    @Query("DELETE FROM budget_analytics WHERE categoryName = :categoryName AND budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month AND budgetWeek = :week")
    suspend fun deleteAnalyticsForPeriod(categoryName: String, period: String, year: Int, month: Int?, week: Int?)
}

data class CategoryVarianceAverage(
    val categoryName: String,
    val avgVariance: Double
)
