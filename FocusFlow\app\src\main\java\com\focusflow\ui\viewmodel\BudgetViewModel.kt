package com.focusflow.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.focusflow.data.model.BudgetCategory
import com.focusflow.data.repository.BudgetCategoryRepository
import com.focusflow.data.repository.ExpenseRepository
import com.focusflow.data.repository.UserPreferencesRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.datetime.*
import javax.inject.Inject

@HiltViewModel
class BudgetViewModel @Inject constructor(
    private val budgetCategoryRepository: BudgetCategoryRepository,
    private val expenseRepository: ExpenseRepository,
    private val userPreferencesRepository: UserPreferencesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(BudgetUiState())
    val uiState: StateFlow<BudgetUiState> = _uiState.asStateFlow()

    init {
        loadBudgetData()
    }

    private fun loadBudgetData() {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val period = preferences?.budgetPeriod ?: "weekly"
                
                val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                val (year, month, week) = getCurrentPeriodValues(now, period)
                
                budgetCategoryRepository.getBudgetCategoriesByPeriod(period)
                    .collect { categories ->
                        val totalBudget = categories.sumOf { it.allocatedAmount }
                        val totalSpent = categories.sumOf { it.spentAmount }
                        
                        _uiState.value = _uiState.value.copy(
                            budgetCategories = categories,
                            totalBudget = totalBudget,
                            totalSpent = totalSpent,
                            budgetPeriod = period,
                            isLoading = false
                        )
                    }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load budget data: ${e.message}"
                )
            }
        }
    }

    fun addBudgetCategory(name: String, allocatedAmount: Double) {
        viewModelScope.launch {
            try {
                val preferences = userPreferencesRepository.getUserPreferencesSync()
                val period = preferences?.budgetPeriod ?: "weekly"
                
                val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
                val (year, month, week) = getCurrentPeriodValues(now, period)
                
                val budgetCategory = BudgetCategory(
                    name = name,
                    allocatedAmount = allocatedAmount,
                    spentAmount = 0.0,
                    budgetPeriod = period,
                    budgetYear = year,
                    budgetMonth = month,
                    budgetWeek = week
                )
                
                budgetCategoryRepository.insertBudgetCategory(budgetCategory)
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to add budget category: ${e.message}"
                )
            }
        }
    }

    fun updateBudgetCategory(budgetCategory: BudgetCategory) {
        viewModelScope.launch {
            try {
                budgetCategoryRepository.updateBudgetCategory(budgetCategory)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update budget category: ${e.message}"
                )
            }
        }
    }

    fun deleteBudgetCategory(budgetCategory: BudgetCategory) {
        viewModelScope.launch {
            try {
                budgetCategoryRepository.deleteBudgetCategory(budgetCategory)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete budget category: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    private fun getCurrentPeriodValues(now: LocalDateTime, period: String): Triple<Int, Int?, Int?> {
        val date = now.date
        return when (period) {
            "weekly" -> {
                val weekOfYear = date.dayOfYear / 7 + 1
                Triple(date.year, null, weekOfYear)
            }
            "monthly" -> {
                Triple(date.year, date.monthNumber, null)
            }
            else -> Triple(date.year, null, null)
        }
    }
}

data class BudgetUiState(
    val budgetCategories: List<BudgetCategory> = emptyList(),
    val totalBudget: Double = 0.0,
    val totalSpent: Double = 0.0,
    val budgetPeriod: String = "weekly",
    val isLoading: Boolean = true,
    val error: String? = null
)

object DefaultBudgetCategories {
    val categories = listOf(
        "Food & Dining" to 150.0,
        "Transportation" to 100.0,
        "Entertainment" to 75.0,
        "Shopping" to 100.0,
        "Bills & Utilities" to 200.0,
        "Healthcare" to 50.0,
        "Personal Care" to 30.0,
        "Other" to 45.0
    )
}

