You are an expert product designer, UX/UI specialist, and Android developer. Please design a detailed, ADHD-friendly Android app that helps users:

Track weekly/monthly expenses
Monitor credit card balances and minimum payments
Set and achieve debt payoff and savings goals
Budget effectively (with weekly and monthly options)
Track health habits (mood, sleep, exercise, medication)
Manage to-dos and daily routines
Overcome procrastination and impulsive spending
Receive AI-powered, personalized coaching and reminders
Your output should include:

App Overview:
A concise summary of the app’s purpose, target audience (ADHD adults), and unique value.
Core Features:
List and describe all major features, including:
Expense tracking (with safe-to-spend widget)
Credit card/debt management (with payoff planner)
Budgeting (weekly/monthly, zero-based, envelope-style)
Health & habit tracking (mood, sleep, exercise, medication)
To-do list and planner (with focus mode and recurring tasks)
AI assistant (for coaching, reminders, and insights)
Gamification (streaks, rewards, virtual pet, etc.)
Wireframe/Screen-by-Screen Flow:
For each main screen, describe:
Layout and navigation
Key UI elements (widgets, buttons, charts, etc.)
ADHD-friendly design choices (minimalism, color, accessibility, etc.)
Example user flows (e.g., logging an expense, paying a card, completing a task)
AI Features (Deepseek/Claude 4.0):
List and describe all AI-powered features, such as:
Personalized budgeting and spending analysis
Debt payoff optimization and reminders
Task breakdown and prioritization
Health/mood correlation insights
Smart, adaptive reminders (timing, tone, frequency)
Natural language input (voice/text for logging and commands)
Weekly progress summaries and motivational nudges
For each, provide example prompts and expected outputs.
Technical Stack & Integration:
Recommend Android tech stack (Kotlin, Jetpack Compose, Room, Firebase, etc.)
Describe how to integrate Claude 4.0/Deepseek API for AI features.
Suggest data privacy and security best practices.
Prompt Engineering Guidance:
For each AI feature, provide explicit, Claude 4.0-optimized prompt templates.
Use clear, direct instructions, context, and examples.
Specify desired output format (e.g., prose, JSON, UI-ready text).
Encourage extended thinking and reflection where appropriate.
For parallel tasks, instruct Claude to use parallel tool calls.
Accessibility & ADHD Design Principles:
List ADHD-friendly design strategies (short timeframes, visual cues, minimal steps, positive feedback, etc.)
Suggest accessibility features (color contrast, font size, voice input, etc.)
MVP vs. Future Features:
Prioritize features for a minimum viable product.
List advanced features for future releases.
Formatting:

Use clear section headers.
Use bullet points and numbered lists for clarity.
For each example, use explicit, Claude 4.0-friendly language.
Avoid markdown in the output; use plain text and clear structure.
Context:
This app is for adults with ADHD who struggle with procrastination, impulsive spending, and executive dysfunction. The goal is to make financial and personal health management simple, motivating, and sustainable, with AI as a supportive coach.

Example AI prompt for budgeting:
“You are my financial coach. Analyze my last week’s expenses and suggest three simple ways I can save money next week, using friendly, non-judgmental language.”

Example AI prompt for task breakdown:
“I have to pay off my credit card debt. Break this big goal into small, weekly steps, and suggest how to stay motivated.”

Example AI prompt for health insights:
“Review my mood and spending logs for the past month. Are there any patterns or triggers I should be aware of?”

Please generate a complete, detailed product specification and prompt set for this app, following all instructions above.