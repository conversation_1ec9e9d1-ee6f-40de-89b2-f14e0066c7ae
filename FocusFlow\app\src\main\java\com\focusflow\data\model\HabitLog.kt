package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDate

@Entity(tableName = "habit_logs")
data class HabitLog(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val habitType: String, // "mood", "sleep", "exercise", "medication"
    val date: LocalDate,
    val value: String, // For mood: "1-5", sleep: "7.5", exercise: "true/false", medication: "taken/missed"
    val notes: String? = null
)

