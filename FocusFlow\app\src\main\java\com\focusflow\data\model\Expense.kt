package com.focusflow.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.datetime.LocalDateTime

@Entity(tableName = "expenses")
data class Expense(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val amount: Double,
    val category: String,
    val description: String,
    val merchant: String? = null,
    val date: LocalDateTime,
    val receiptPath: String? = null,
    val isRecurring: Boolean = false,
    val recurringFrequency: String? = null // "daily", "weekly", "monthly"
)

