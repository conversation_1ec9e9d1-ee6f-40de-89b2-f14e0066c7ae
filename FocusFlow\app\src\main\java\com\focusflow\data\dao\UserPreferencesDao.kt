package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.UserPreferences
import kotlinx.coroutines.flow.Flow

@Dao
interface UserPreferencesDao {
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    fun getUserPreferences(): Flow<UserPreferences?>

    @Query("SELECT * FROM user_preferences WHERE id = 1")
    suspend fun getUserPreferencesSync(): UserPreferences?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserPreferences(preferences: UserPreferences)

    @Update
    suspend fun updateUserPreferences(preferences: UserPreferences)

    @Query("UPDATE user_preferences SET budgetPeriod = :period WHERE id = 1")
    suspend fun updateBudgetPeriod(period: String)

    @Query("UPDATE user_preferences SET notificationsEnabled = :enabled WHERE id = 1")
    suspend fun updateNotificationsEnabled(enabled: Boolean)

    @Query("UPDATE user_preferences SET darkModeEnabled = :enabled WHERE id = 1")
    suspend fun updateDarkModeEnabled(enabled: Boolean)

    @Query("UPDATE user_preferences SET fontSize = :fontSize WHERE id = 1")
    suspend fun updateFontSize(fontSize: String)

    @Query("UPDATE user_preferences SET themePreference = :themePreference WHERE id = 1")
    suspend fun updateThemePreference(themePreference: String)

    @Query("UPDATE user_preferences SET fontScale = :fontScale WHERE id = 1")
    suspend fun updateFontScale(fontScale: Float)

    @Query("UPDATE user_preferences SET highContrastMode = :enabled WHERE id = 1")
    suspend fun updateHighContrastMode(enabled: Boolean)

    @Query("UPDATE user_preferences SET voiceInputEnabled = :enabled WHERE id = 1")
    suspend fun updateVoiceInputEnabled(enabled: Boolean)

    @Query("UPDATE user_preferences SET animationsEnabled = :enabled WHERE id = 1")
    suspend fun updateAnimationsEnabled(enabled: Boolean)
}

