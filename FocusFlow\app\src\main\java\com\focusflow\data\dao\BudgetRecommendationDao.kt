package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.BudgetRecommendation
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface BudgetRecommendationDao {
    
    @Query("SELECT * FROM budget_recommendations WHERE isActive = 1 ORDER BY generatedDate DESC")
    fun getAllActiveRecommendations(): Flow<List<BudgetRecommendation>>
    
    @Query("SELECT * FROM budget_recommendations WHERE categoryName = :categoryName AND isActive = 1 ORDER BY generatedDate DESC LIMIT 1")
    suspend fun getLatestRecommendationForCategory(categoryName: String): BudgetRecommendation?
    
    @Query("SELECT * FROM budget_recommendations WHERE isAccepted IS NULL AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getPendingRecommendations(): Flow<List<BudgetRecommendation>>
    
    @Query("SELECT * FROM budget_recommendations WHERE isAccepted = 1 ORDER BY generatedDate DESC")
    fun getAcceptedRecommendations(): Flow<List<BudgetRecommendation>>
    
    @Query("SELECT * FROM budget_recommendations WHERE isAccepted = 0 ORDER BY generatedDate DESC")
    fun getRejectedRecommendations(): Flow<List<BudgetRecommendation>>
    
    @Query("SELECT * FROM budget_recommendations WHERE reasonCode = :reasonCode AND isActive = 1")
    fun getRecommendationsByReason(reasonCode: String): Flow<List<BudgetRecommendation>>
    
    @Query("SELECT * FROM budget_recommendations WHERE confidenceScore >= :minConfidence AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getHighConfidenceRecommendations(minConfidence: Double): Flow<List<BudgetRecommendation>>
    
    @Query("SELECT * FROM budget_recommendations WHERE id = :id")
    suspend fun getRecommendationById(id: Long): BudgetRecommendation?
    
    @Query("SELECT COUNT(*) FROM budget_recommendations WHERE isAccepted IS NULL AND isActive = 1")
    suspend fun getPendingRecommendationCount(): Int
    
    @Query("SELECT AVG(confidenceScore) FROM budget_recommendations WHERE isAccepted = 1")
    suspend fun getAverageAcceptedConfidenceScore(): Double?
    
    @Insert
    suspend fun insertRecommendation(recommendation: BudgetRecommendation): Long
    
    @Update
    suspend fun updateRecommendation(recommendation: BudgetRecommendation)
    
    @Delete
    suspend fun deleteRecommendation(recommendation: BudgetRecommendation)
    
    @Query("UPDATE budget_recommendations SET isAccepted = :isAccepted, userFeedback = :feedback WHERE id = :id")
    suspend fun updateRecommendationResponse(id: Long, isAccepted: Boolean, feedback: String?)
    
    @Query("UPDATE budget_recommendations SET isActive = 0 WHERE categoryName = :categoryName AND id != :excludeId")
    suspend fun deactivateOldRecommendationsForCategory(categoryName: String, excludeId: Long)
    
    @Query("DELETE FROM budget_recommendations WHERE generatedDate < :cutoffDate AND isAccepted IS NOT NULL")
    suspend fun deleteOldProcessedRecommendations(cutoffDate: LocalDateTime)
}
