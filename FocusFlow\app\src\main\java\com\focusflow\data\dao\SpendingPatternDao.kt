package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.SpendingPattern
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface SpendingPatternDao {
    
    @Query("SELECT * FROM spending_patterns WHERE isActive = 1 ORDER BY confidenceScore DESC")
    fun getAllActiveSpendingPatterns(): Flow<List<SpendingPattern>>
    
    @Query("SELECT * FROM spending_patterns WHERE patternType = :patternType AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getSpendingPatternsByType(patternType: String): Flow<List<SpendingPattern>>
    
    @Query("SELECT * FROM spending_patterns WHERE category = :category AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getSpendingPatternsByCategory(category: String): Flow<List<SpendingPattern>>
    
    @Query("SELECT * FROM spending_patterns WHERE userConfirmed IS NULL AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getPendingSpendingPatterns(): Flow<List<SpendingPattern>>
    
    @Query("SELECT * FROM spending_patterns WHERE userConfirmed = 1 AND isActive = 1 ORDER BY lastOccurrence DESC")
    fun getConfirmedSpendingPatterns(): Flow<List<SpendingPattern>>
    
    @Query("SELECT * FROM spending_patterns WHERE severity = :severity AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getSpendingPatternsBySeverity(severity: String): Flow<List<SpendingPattern>>
    
    @Query("SELECT * FROM spending_patterns WHERE id = :id")
    suspend fun getSpendingPatternById(id: Long): SpendingPattern?
    
    @Query("SELECT * FROM spending_patterns WHERE confidenceScore >= :minConfidence AND isActive = 1 ORDER BY confidenceScore DESC")
    fun getHighConfidencePatterns(minConfidence: Double): Flow<List<SpendingPattern>>
    
    @Query("SELECT * FROM spending_patterns WHERE lastOccurrence >= :recentDate AND isActive = 1 ORDER BY lastOccurrence DESC")
    fun getRecentSpendingPatterns(recentDate: LocalDateTime): Flow<List<SpendingPattern>>
    
    @Query("SELECT COUNT(*) FROM spending_patterns WHERE userConfirmed IS NULL AND isActive = 1")
    suspend fun getPendingPatternCount(): Int
    
    @Query("SELECT COUNT(*) FROM spending_patterns WHERE userConfirmed = 1 AND isActive = 1")
    suspend fun getConfirmedPatternCount(): Int
    
    @Query("SELECT AVG(confidenceScore) FROM spending_patterns WHERE userConfirmed = 1")
    suspend fun getAverageConfidenceScore(): Double?
    
    @Query("SELECT patternType, COUNT(*) as count FROM spending_patterns WHERE isActive = 1 GROUP BY patternType ORDER BY count DESC")
    suspend fun getPatternCountByType(): List<PatternTypeCount>
    
    @Query("SELECT category, COUNT(*) as count FROM spending_patterns WHERE isActive = 1 GROUP BY category ORDER BY count DESC")
    suspend fun getPatternCountByCategory(): List<PatternCategoryCount>
    
    @Query("SELECT * FROM spending_patterns WHERE interventionEffectiveness IS NOT NULL ORDER BY interventionEffectiveness DESC LIMIT :limit")
    suspend fun getMostEffectiveInterventions(limit: Int): List<SpendingPattern>
    
    @Query("SELECT COUNT(*) FROM spending_patterns WHERE preventionSuccess > 0")
    suspend fun getPatternsWithSuccessfulPrevention(): Int
    
    @Query("SELECT SUM(preventionSuccess) FROM spending_patterns")
    suspend fun getTotalPreventionSuccesses(): Int
    
    @Query("SELECT SUM(preventionAttempts) FROM spending_patterns")
    suspend fun getTotalPreventionAttempts(): Int
    
    @Insert
    suspend fun insertSpendingPattern(spendingPattern: SpendingPattern): Long
    
    @Update
    suspend fun updateSpendingPattern(spendingPattern: SpendingPattern)
    
    @Delete
    suspend fun deleteSpendingPattern(spendingPattern: SpendingPattern)
    
    @Query("UPDATE spending_patterns SET userConfirmed = :confirmed WHERE id = :id")
    suspend fun updatePatternConfirmation(id: Long, confirmed: Boolean)
    
    @Query("UPDATE spending_patterns SET lastOccurrence = :occurrence, occurrenceCount = occurrenceCount + 1 WHERE id = :id")
    suspend fun recordPatternOccurrence(id: Long, occurrence: LocalDateTime)
    
    @Query("UPDATE spending_patterns SET preventionSuccess = preventionSuccess + 1, preventionAttempts = preventionAttempts + 1 WHERE id = :id")
    suspend fun recordSuccessfulPrevention(id: Long)
    
    @Query("UPDATE spending_patterns SET preventionAttempts = preventionAttempts + 1 WHERE id = :id")
    suspend fun recordPreventionAttempt(id: Long)
    
    @Query("UPDATE spending_patterns SET interventionEffectiveness = :effectiveness WHERE id = :id")
    suspend fun updateInterventionEffectiveness(id: Long, effectiveness: Double)
    
    @Query("UPDATE spending_patterns SET isActive = 0 WHERE id = :id")
    suspend fun deactivateSpendingPattern(id: Long)
    
    @Query("DELETE FROM spending_patterns WHERE detectedDate < :cutoffDate AND userConfirmed != 1")
    suspend fun deleteOldUnconfirmedPatterns(cutoffDate: LocalDateTime)
}

data class PatternTypeCount(
    val patternType: String,
    val count: Int
)

data class PatternCategoryCount(
    val category: String,
    val count: Int
)
