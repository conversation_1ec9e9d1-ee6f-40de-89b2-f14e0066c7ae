package com.focusflow.data.repository

import com.focusflow.data.dao.CreditCardDao
import com.focusflow.data.model.CreditCard
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CreditCardRepository @Inject constructor(
    private val creditCardDao: CreditCardDao
) {
    fun getAllActiveCreditCards(): Flow<List<CreditCard>> = creditCardDao.getAllActiveCreditCards()

    suspend fun getCreditCardById(cardId: Long): CreditCard? = creditCardDao.getCreditCardById(cardId)

    suspend fun getTotalDebt(): Double = creditCardDao.getTotalDebt() ?: 0.0

    suspend fun getTotalMinimumPaymentsDue(date: LocalDate): Double = 
        creditCardDao.getTotalMinimumPaymentsDue(date) ?: 0.0

    fun getCardsWithPaymentsDue(date: LocalDate): Flow<List<CreditCard>> = 
        creditCardDao.getCardsWithPaymentsDue(date)

    suspend fun insertCreditCard(creditCard: CreditCard): Long = 
        creditCardDao.insertCreditCard(creditCard)

    suspend fun updateCreditCard(creditCard: CreditCard) = 
        creditCardDao.updateCreditCard(creditCard)

    suspend fun deleteCreditCard(creditCard: CreditCard) = 
        creditCardDao.deleteCreditCard(creditCard)

    suspend fun deactivateCreditCard(cardId: Long) = 
        creditCardDao.deactivateCreditCard(cardId)
}

