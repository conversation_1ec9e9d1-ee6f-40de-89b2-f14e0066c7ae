package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.AIInteraction
import kotlinx.coroutines.flow.Flow

@Dao
interface AIInteractionDao {
    @Query("SELECT * FROM ai_interactions ORDER BY timestamp DESC")
    fun getAllInteractions(): Flow<List<AIInteraction>>

    @Query("SELECT * FROM ai_interactions WHERE interactionType = :type ORDER BY timestamp DESC")
    fun getInteractionsByType(type: String): Flow<List<AIInteraction>>

    @Query("SELECT * FROM ai_interactions ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentInteractions(limit: Int): Flow<List<AIInteraction>>

    @Insert
    suspend fun insertInteraction(interaction: AIInteraction): Long

    @Delete
    suspend fun deleteInteraction(interaction: AIInteraction)

    @Query("DELETE FROM ai_interactions WHERE timestamp < :cutoffDate")
    suspend fun deleteOldInteractions(cutoffDate: kotlinx.datetime.LocalDateTime)
}

