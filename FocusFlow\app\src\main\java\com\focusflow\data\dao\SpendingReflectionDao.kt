package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.SpendingReflection
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface SpendingReflectionDao {
    
    @Query("SELECT * FROM spending_reflections ORDER BY reflectionDate DESC")
    fun getAllReflections(): Flow<List<SpendingReflection>>
    
    @Query("SELECT * FROM spending_reflections WHERE category = :category ORDER BY reflectionDate DESC")
    fun getReflectionsByCategory(category: String): Flow<List<SpendingReflection>>
    
    @Query("SELECT * FROM spending_reflections WHERE expenseId = :expenseId")
    suspend fun getReflectionByExpenseId(expenseId: Long): SpendingReflection?
    
    @Query("SELECT * FROM spending_reflections WHERE wishlistItemId = :wishlistItemId")
    suspend fun getReflectionByWishlistItemId(wishlistItemId: Long): SpendingReflection?
    
    @Query("SELECT * FROM spending_reflections WHERE reflectionDate BETWEEN :startDate AND :endDate ORDER BY reflectionDate DESC")
    fun getReflectionsByDateRange(startDate: LocalDateTime, endDate: LocalDateTime): Flow<List<SpendingReflection>>
    
    @Query("SELECT * FROM spending_reflections WHERE emotionalState = :emotionalState ORDER BY reflectionDate DESC")
    fun getReflectionsByEmotionalState(emotionalState: String): Flow<List<SpendingReflection>>
    
    @Query("SELECT * FROM spending_reflections WHERE regretLevel >= :minRegretLevel ORDER BY regretLevel DESC, reflectionDate DESC")
    fun getHighRegretReflections(minRegretLevel: Int): Flow<List<SpendingReflection>>
    
    @Query("SELECT * FROM spending_reflections WHERE satisfactionLevel >= :minSatisfactionLevel ORDER BY satisfactionLevel DESC, reflectionDate DESC")
    fun getHighSatisfactionReflections(minSatisfactionLevel: Int): Flow<List<SpendingReflection>>
    
    @Query("SELECT * FROM spending_reflections WHERE delayHelpful = 1 ORDER BY reflectionDate DESC")
    fun getReflectionsWhereDelayHelped(): Flow<List<SpendingReflection>>
    
    @Query("SELECT AVG(satisfactionLevel) FROM spending_reflections WHERE satisfactionLevel IS NOT NULL")
    suspend fun getAverageSatisfactionLevel(): Double?
    
    @Query("SELECT AVG(regretLevel) FROM spending_reflections WHERE regretLevel IS NOT NULL")
    suspend fun getAverageRegretLevel(): Double?
    
    @Query("SELECT AVG(mindfulnessScore) FROM spending_reflections WHERE mindfulnessScore IS NOT NULL")
    suspend fun getAverageMindfulnessScore(): Double?
    
    @Query("SELECT COUNT(*) FROM spending_reflections WHERE delayHelpful = 1")
    suspend fun getDelayHelpfulCount(): Int
    
    @Query("SELECT COUNT(*) FROM spending_reflections WHERE wouldBuyAgain = 0")
    suspend fun getWouldNotBuyAgainCount(): Int
    
    @Insert
    suspend fun insertReflection(reflection: SpendingReflection): Long
    
    @Update
    suspend fun updateReflection(reflection: SpendingReflection)
    
    @Delete
    suspend fun deleteReflection(reflection: SpendingReflection)
    
    @Query("DELETE FROM spending_reflections WHERE reflectionDate < :cutoffDate")
    suspend fun deleteReflectionsOlderThan(cutoffDate: LocalDateTime)
}
