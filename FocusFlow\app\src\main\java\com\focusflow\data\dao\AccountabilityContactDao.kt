package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.AccountabilityContact
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface AccountabilityContactDao {
    
    @Query("SELECT * FROM accountability_contacts WHERE isActive = 1 ORDER BY trustLevel DESC")
    fun getAllActiveContacts(): Flow<List<AccountabilityContact>>
    
    @Query("SELECT * FROM accountability_contacts WHERE relationship = :relationship AND isActive = 1 ORDER BY trustLevel DESC")
    fun getContactsByRelationship(relationship: String): Flow<List<AccountabilityContact>>
    
    @Query("SELECT * FROM accountability_contacts WHERE emergencyContact = 1 AND isActive = 1 ORDER BY trustLevel DESC")
    fun getEmergencyContacts(): Flow<List<AccountabilityContact>>
    
    @Query("SELECT * FROM accountability_contacts WHERE canReceiveSpendingAlerts = 1 AND isActive = 1")
    fun getSpendingAlertContacts(): Flow<List<AccountabilityContact>>
    
    @Query("SELECT * FROM accountability_contacts WHERE id = :id")
    suspend fun getContactById(id: Long): AccountabilityContact?
    
    @Query("SELECT COUNT(*) FROM accountability_contacts WHERE isActive = 1")
    suspend fun getActiveContactCount(): Int
    
    @Query("SELECT AVG(responseRate) FROM accountability_contacts WHERE isActive = 1")
    suspend fun getAverageResponseRate(): Double?
    
    @Query("SELECT AVG(trustLevel) FROM accountability_contacts WHERE isActive = 1")
    suspend fun getAverageTrustLevel(): Double?
    
    @Insert
    suspend fun insertContact(contact: AccountabilityContact): Long
    
    @Update
    suspend fun updateContact(contact: AccountabilityContact)
    
    @Delete
    suspend fun deleteContact(contact: AccountabilityContact)
    
    @Query("UPDATE accountability_contacts SET lastContactDate = :contactDate, totalInteractions = totalInteractions + 1 WHERE id = :id")
    suspend fun recordInteraction(id: Long, contactDate: LocalDateTime)
    
    @Query("UPDATE accountability_contacts SET successfulInterventions = successfulInterventions + 1 WHERE id = :id")
    suspend fun recordSuccessfulIntervention(id: Long)
    
    @Query("UPDATE accountability_contacts SET trustLevel = :trustLevel WHERE id = :id")
    suspend fun updateTrustLevel(id: Long, trustLevel: Int)
    
    @Query("UPDATE accountability_contacts SET responseRate = :responseRate WHERE id = :id")
    suspend fun updateResponseRate(id: Long, responseRate: Double)
    
    @Query("UPDATE accountability_contacts SET isActive = 0 WHERE id = :id")
    suspend fun deactivateContact(id: Long)
}
