1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.focusflow.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions for AI features and data sync -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Notification permissions -->
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:10:5-77
16-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
17-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:11:5-79
17-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:11:22-76
18
19    <!-- Camera permission for receipt scanning (optional) -->
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:14:5-65
20-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:14:22-62
21
22    <uses-feature
22-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:5-85
23        android:name="android.hardware.camera"
23-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:19-57
24        android:required="false" />
24-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:58-82
25
26    <!-- Storage permissions for receipt images -->
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:18:5-76
27-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:18:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:19:5-75
28-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:19:22-72
29    <uses-permission
29-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:20:5-21:38
30        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
30-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:20:22-78
31        android:maxSdkVersion="28" />
31-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:21:9-35
32
33    <!-- Biometric authentication -->
34    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
34-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:24:5-72
34-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:24:22-69
35
36    <!-- Voice input permissions -->
37    <uses-permission android:name="android.permission.RECORD_AUDIO" />
37-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:27:5-71
37-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:27:22-68
38
39    <uses-feature
39-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:5-89
40        android:name="android.hardware.microphone"
40-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:19-61
41        android:required="false" />
41-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:62-86
42
43    <!-- Accessibility services -->
44    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
44-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:31:5-32:47
44-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:31:22-82
45
46    <!-- suppress DeprecatedClassUsageInspection -->
47    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
48    <uses-permission android:name="android.permission.WAKE_LOCK" />
48-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
48-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
49    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
50-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
50-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
51
52    <permission
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
53        android:name="com.focusflow.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.focusflow.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
57
58    <application
58-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:34:5-93:19
59        android:name="com.focusflow.FocusFlowApplication"
59-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:35:9-45
60        android:allowBackup="false"
60-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:36:9-36
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
62        android:dataExtractionRules="@xml/data_extraction_rules"
62-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:37:9-65
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:fullBackupContent="@xml/backup_rules"
65-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:38:9-54
66        android:icon="@mipmap/ic_launcher"
66-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:39:9-43
67        android:label="@string/app_name"
67-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:40:9-41
68        android:roundIcon="@mipmap/ic_launcher_round"
68-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:41:9-54
69        android:supportsRtl="true"
69-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:42:9-35
70        android:theme="@style/Theme.FocusFlow"
70-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:43:9-47
71        android:usesCleartextTraffic="false" >
71-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:44:9-45
72        <activity
72-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:47:9-58:20
73            android:name="com.focusflow.MainActivity"
73-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:48:13-41
74            android:exported="true"
74-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:49:13-36
75            android:label="@string/app_name"
75-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:50:13-45
76            android:screenOrientation="unspecified"
76-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:52:13-52
77            android:theme="@style/Theme.FocusFlow"
77-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:51:13-51
78            android:windowSoftInputMode="adjustResize" >
78-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:53:13-55
79            <intent-filter>
79-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:54:13-57:29
80                <action android:name="android.intent.action.MAIN" />
80-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:55:17-69
80-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:55:25-66
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:56:17-77
82-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:56:27-74
83            </intent-filter>
84        </activity>
85
86        <!-- Onboarding Activity -->
87        <activity
87-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:61:9-65:55
88            android:name="com.focusflow.ui.onboarding.OnboardingActivity"
88-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:62:13-61
89            android:exported="false"
89-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:63:13-37
90            android:screenOrientation="unspecified"
90-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:65:13-52
91            android:theme="@style/Theme.FocusFlow.NoActionBar" />
91-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:64:13-63
92
93        <!-- Notification Service -->
94        <service
94-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:68:9-70:40
95            android:name="com.focusflow.service.NotificationService"
95-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:69:13-56
96            android:exported="false" />
96-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:70:13-37
97
98        <!-- Alarm Receiver for scheduled notifications -->
99        <receiver
99-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:73:9-75:40
100            android:name="com.focusflow.receiver.AlarmReceiver"
100-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:74:13-51
101            android:exported="false" />
101-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:75:13-37
102
103        <!-- File Provider for sharing receipts -->
104        <provider
105            android:name="androidx.core.content.FileProvider"
105-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:79:13-62
106            android:authorities="com.focusflow.debug.fileProvider"
106-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:80:13-64
107            android:exported="false"
107-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:81:13-37
108            android:grantUriPermissions="true" >
108-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:82:13-47
109            <meta-data
109-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:83:13-85:54
110                android:name="android.support.FILE_PROVIDER_PATHS"
110-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:84:17-67
111                android:resource="@xml/file_paths" />
111-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:85:17-51
112        </provider>
113
114        <!-- Network Security Config -->
115        <meta-data
115-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:89:9-91:63
116            android:name="android.security.net.config"
116-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:90:13-55
117            android:resource="@xml/network_security_config" />
117-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:91:13-60
118
119        <provider
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
120            android:name="androidx.startup.InitializationProvider"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
121            android:authorities="com.focusflow.debug.androidx-startup"
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
122            android:exported="false" >
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
123            <meta-data
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
124                android:name="androidx.work.WorkManagerInitializer"
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
125                android:value="androidx.startup" />
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
126            <meta-data
126-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
127                android:name="androidx.emoji2.text.EmojiCompatInitializer"
127-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
128                android:value="androidx.startup" />
128-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
131                android:value="androidx.startup" />
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
134                android:value="androidx.startup" />
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
135        </provider>
136
137        <service
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
138            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
139            android:directBootAware="false"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
140            android:enabled="@bool/enable_system_alarm_service_default"
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
141            android:exported="false" />
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
142        <service
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
143            android:name="androidx.work.impl.background.systemjob.SystemJobService"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
144            android:directBootAware="false"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
145            android:enabled="@bool/enable_system_job_service_default"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
146            android:exported="true"
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
147            android:permission="android.permission.BIND_JOB_SERVICE" />
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
148        <service
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
149            android:name="androidx.work.impl.foreground.SystemForegroundService"
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
150            android:directBootAware="false"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
151            android:enabled="@bool/enable_system_foreground_service_default"
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
152            android:exported="false" />
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
153
154        <receiver
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
155            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
157            android:enabled="true"
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
158            android:exported="false" />
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
159        <receiver
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
160            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
161            android:directBootAware="false"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
162            android:enabled="false"
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
163            android:exported="false" >
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
164            <intent-filter>
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
165                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
166                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
167            </intent-filter>
168        </receiver>
169        <receiver
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
170            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
172            android:enabled="false"
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
173            android:exported="false" >
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
174            <intent-filter>
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
175                <action android:name="android.intent.action.BATTERY_OKAY" />
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
176                <action android:name="android.intent.action.BATTERY_LOW" />
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
177            </intent-filter>
178        </receiver>
179        <receiver
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
180            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
181            android:directBootAware="false"
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
182            android:enabled="false"
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
183            android:exported="false" >
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
184            <intent-filter>
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
185                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
186                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
187            </intent-filter>
188        </receiver>
189        <receiver
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
190            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
191            android:directBootAware="false"
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
192            android:enabled="false"
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
193            android:exported="false" >
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
194            <intent-filter>
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
195                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
196            </intent-filter>
197        </receiver>
198        <receiver
198-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
199            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
200            android:directBootAware="false"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
201            android:enabled="false"
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
202            android:exported="false" >
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
203            <intent-filter>
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
204                <action android:name="android.intent.action.BOOT_COMPLETED" />
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
205                <action android:name="android.intent.action.TIME_SET" />
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
206                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
207            </intent-filter>
208        </receiver>
209        <receiver
209-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
210            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
211            android:directBootAware="false"
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
212            android:enabled="@bool/enable_system_alarm_service_default"
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
213            android:exported="false" >
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
214            <intent-filter>
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
215                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
216            </intent-filter>
217        </receiver>
218        <receiver
218-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
219            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
220            android:directBootAware="false"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
221            android:enabled="true"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
222            android:exported="true"
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
223            android:permission="android.permission.DUMP" >
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
224            <intent-filter>
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
225                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
226            </intent-filter>
227        </receiver>
228
229        <activity
229-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
230            android:name="androidx.compose.ui.tooling.PreviewActivity"
230-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
231            android:exported="true" />
231-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
232        <activity
232-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
233            android:name="androidx.activity.ComponentActivity"
233-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
234            android:exported="true" />
234-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
235
236        <service
236-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
237            android:name="androidx.room.MultiInstanceInvalidationService"
237-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
238            android:directBootAware="true"
238-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
239            android:exported="false" />
239-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
240
241        <receiver
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
242            android:name="androidx.profileinstaller.ProfileInstallReceiver"
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
243            android:directBootAware="false"
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
244            android:enabled="true"
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
245            android:exported="true"
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
246            android:permission="android.permission.DUMP" >
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
247            <intent-filter>
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
248                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
249            </intent-filter>
250            <intent-filter>
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
251                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
252            </intent-filter>
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
254                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
255            </intent-filter>
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
257                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
258            </intent-filter>
259        </receiver>
260    </application>
261
262</manifest>
