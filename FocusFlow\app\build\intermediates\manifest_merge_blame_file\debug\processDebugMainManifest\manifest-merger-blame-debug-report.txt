1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.focusflow.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Network permissions for AI features and data sync -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Notification permissions -->
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:10:5-77
16-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:10:22-74
17    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
17-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:11:5-79
17-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:11:22-76
18
19    <!-- Camera permission for receipt scanning (optional) -->
20    <uses-permission android:name="android.permission.CAMERA" />
20-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:14:5-65
20-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:14:22-62
21
22    <uses-feature
22-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:5-85
23        android:name="android.hardware.camera"
23-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:19-57
24        android:required="false" />
24-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:15:58-82
25
26    <!-- Storage permissions for receipt images -->
27    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
27-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:18:5-76
27-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:18:22-73
28    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
28-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:19:5-75
28-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:19:22-72
29    <uses-permission
29-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:20:5-21:38
30        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
30-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:20:22-78
31        android:maxSdkVersion="28" />
31-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:21:9-35
32
33    <!-- Biometric authentication -->
34    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
34-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:24:5-72
34-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:24:22-69
35
36    <!-- Voice input permissions -->
37    <uses-permission android:name="android.permission.RECORD_AUDIO" />
37-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:27:5-71
37-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:27:22-68
38
39    <uses-feature
39-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:5-89
40        android:name="android.hardware.microphone"
40-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:19-61
41        android:required="false" />
41-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:28:62-86
42
43    <!-- Accessibility services -->
44    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
44-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:31:5-32:47
44-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:31:22-82
45
46    <!-- suppress DeprecatedClassUsageInspection -->
47    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
47-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0e1dc919567705f737931e90f7aead7b\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
48    <uses-permission android:name="android.permission.WAKE_LOCK" />
48-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:5-68
48-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:23:22-65
49    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
49-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
50    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
50-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
50-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
51
52    <permission
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
53        android:name="com.focusflow.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.focusflow.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
57
58    <application
58-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:34:5-93:19
59        android:name="com.focusflow.FocusFlowApplication"
59-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:35:9-45
60        android:allowBackup="false"
60-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:36:9-36
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\904fd36595e0135e8fdca5c15906c24d\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
62        android:dataExtractionRules="@xml/data_extraction_rules"
62-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:37:9-65
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:fullBackupContent="@xml/backup_rules"
65-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:38:9-54
66        android:icon="@mipmap/ic_launcher"
66-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:39:9-43
67        android:label="@string/app_name"
67-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:40:9-41
68        android:roundIcon="@mipmap/ic_launcher_round"
68-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:41:9-54
69        android:supportsRtl="true"
69-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:42:9-35
70        android:testOnly="true"
71        android:theme="@style/Theme.FocusFlow"
71-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:43:9-47
72        android:usesCleartextTraffic="false" >
72-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:44:9-45
73        <activity
73-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:47:9-58:20
74            android:name="com.focusflow.MainActivity"
74-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:48:13-41
75            android:exported="true"
75-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:49:13-36
76            android:label="@string/app_name"
76-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:50:13-45
77            android:screenOrientation="unspecified"
77-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:52:13-52
78            android:theme="@style/Theme.FocusFlow"
78-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:51:13-51
79            android:windowSoftInputMode="adjustResize" >
79-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:53:13-55
80            <intent-filter>
80-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:54:13-57:29
81                <action android:name="android.intent.action.MAIN" />
81-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:55:17-69
81-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:55:25-66
82
83                <category android:name="android.intent.category.LAUNCHER" />
83-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:56:17-77
83-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:56:27-74
84            </intent-filter>
85        </activity>
86
87        <!-- Onboarding Activity -->
88        <activity
88-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:61:9-65:55
89            android:name="com.focusflow.ui.onboarding.OnboardingActivity"
89-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:62:13-61
90            android:exported="false"
90-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:63:13-37
91            android:screenOrientation="unspecified"
91-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:65:13-52
92            android:theme="@style/Theme.FocusFlow.NoActionBar" />
92-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:64:13-63
93
94        <!-- Notification Service -->
95        <service
95-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:68:9-70:40
96            android:name="com.focusflow.service.NotificationService"
96-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:69:13-56
97            android:exported="false" />
97-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:70:13-37
98
99        <!-- Alarm Receiver for scheduled notifications -->
100        <receiver
100-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:73:9-75:40
101            android:name="com.focusflow.receiver.AlarmReceiver"
101-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:74:13-51
102            android:exported="false" />
102-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:75:13-37
103
104        <!-- File Provider for sharing receipts -->
105        <provider
106            android:name="androidx.core.content.FileProvider"
106-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:79:13-62
107            android:authorities="com.focusflow.debug.fileProvider"
107-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:80:13-64
108            android:exported="false"
108-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:81:13-37
109            android:grantUriPermissions="true" >
109-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:82:13-47
110            <meta-data
110-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:83:13-85:54
111                android:name="android.support.FILE_PROVIDER_PATHS"
111-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:84:17-67
112                android:resource="@xml/file_paths" />
112-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:85:17-51
113        </provider>
114
115        <!-- Network Security Config -->
116        <meta-data
116-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:89:9-91:63
117            android:name="android.security.net.config"
117-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:90:13-55
118            android:resource="@xml/network_security_config" />
118-->C:\Users\<USER>\Downloads\Understanding Content in pasted_content (1)\FocusFlow\app\src\main\AndroidManifest.xml:91:13-60
119
120        <provider
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:29:9-37:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:30:13-67
122            android:authorities="com.focusflow.debug.androidx-startup"
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:31:13-68
123            android:exported="false" >
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:32:13-37
124            <meta-data
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:34:13-36:52
125                android:name="androidx.work.WorkManagerInitializer"
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:35:17-68
126                android:value="androidx.startup" />
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:36:17-49
127            <meta-data
127-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.emoji2.text.EmojiCompatInitializer"
128-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:30:17-75
129                android:value="androidx.startup" />
129-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1fecd53a331ce0b963897f9da8c854aa\transformed\jetified-emoji2-1.4.0\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
131-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
132                android:value="androidx.startup" />
132-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9fe1310f25ca312ed873f64d82bd0648\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
133            <meta-data
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
134-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
135                android:value="androidx.startup" />
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
136        </provider>
137
138        <service
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
139            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
140            android:directBootAware="false"
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
141            android:enabled="@bool/enable_system_alarm_service_default"
141-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
142            android:exported="false" />
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
143        <service
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
144            android:name="androidx.work.impl.background.systemjob.SystemJobService"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
146            android:enabled="@bool/enable_system_job_service_default"
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
147            android:exported="true"
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
148            android:permission="android.permission.BIND_JOB_SERVICE" />
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
149        <service
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
150            android:name="androidx.work.impl.foreground.SystemForegroundService"
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
151            android:directBootAware="false"
151-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
152            android:enabled="@bool/enable_system_foreground_service_default"
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
153            android:exported="false" />
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
154
155        <receiver
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
156            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
157            android:directBootAware="false"
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
158            android:enabled="true"
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
159            android:exported="false" />
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
160        <receiver
160-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
161            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
163            android:enabled="false"
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
164            android:exported="false" >
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
165            <intent-filter>
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
166                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
167                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
168            </intent-filter>
169        </receiver>
170        <receiver
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
171            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
171-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
172            android:directBootAware="false"
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
173            android:enabled="false"
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
174            android:exported="false" >
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
175            <intent-filter>
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
176                <action android:name="android.intent.action.BATTERY_OKAY" />
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
177                <action android:name="android.intent.action.BATTERY_LOW" />
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
178            </intent-filter>
179        </receiver>
180        <receiver
180-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
181            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
182            android:directBootAware="false"
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
183            android:enabled="false"
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
184            android:exported="false" >
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
185            <intent-filter>
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
186                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
187                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
188            </intent-filter>
189        </receiver>
190        <receiver
190-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
191            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
191-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
192            android:directBootAware="false"
192-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
193            android:enabled="false"
193-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
194            android:exported="false" >
194-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
195            <intent-filter>
195-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
196                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
196-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
196-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
197            </intent-filter>
198        </receiver>
199        <receiver
199-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
200            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
200-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
202            android:enabled="false"
202-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
203            android:exported="false" >
203-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
204            <intent-filter>
204-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
205                <action android:name="android.intent.action.BOOT_COMPLETED" />
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
205-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
206                <action android:name="android.intent.action.TIME_SET" />
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
206-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
207                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
207-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
208            </intent-filter>
209        </receiver>
210        <receiver
210-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
211            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
211-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
212            android:directBootAware="false"
212-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
213            android:enabled="@bool/enable_system_alarm_service_default"
213-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
214            android:exported="false" >
214-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
215            <intent-filter>
215-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
216                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
216-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
220            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
220-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
222            android:enabled="true"
222-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
223            android:exported="true"
223-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
224            android:permission="android.permission.DUMP" >
224-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
225            <intent-filter>
225-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
226                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
226-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc6d5b5f2df2f7220830487457b3fd87\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
227            </intent-filter>
228        </receiver>
229
230        <activity
230-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\AndroidManifest.xml:23:9-25:39
231            android:name="androidx.compose.ui.tooling.PreviewActivity"
231-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\AndroidManifest.xml:24:13-71
232            android:exported="true" />
232-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\a0a2678a7cfe444c1610586a94808387\transformed\jetified-ui-tooling-release\AndroidManifest.xml:25:13-36
233        <activity
233-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
234            android:name="androidx.activity.ComponentActivity"
234-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
235            android:exported="true" />
235-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\cbcef85fc27e89f72d399aad76d2f59a\transformed\jetified-ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
236
237        <service
237-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
238            android:name="androidx.room.MultiInstanceInvalidationService"
238-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
239            android:directBootAware="true"
239-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
240            android:exported="false" />
240-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\10a529dc4934703a517ccd294fe20872\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
241
242        <receiver
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
243            android:name="androidx.profileinstaller.ProfileInstallReceiver"
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
244            android:directBootAware="false"
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
245            android:enabled="true"
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
246            android:exported="true"
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
247            android:permission="android.permission.DUMP" >
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
248            <intent-filter>
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
249                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
250            </intent-filter>
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
252                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
253            </intent-filter>
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
255                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
258                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f40e16e10024a749bf4cb75653564f97\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
259            </intent-filter>
260        </receiver>
261    </application>
262
263</manifest>
