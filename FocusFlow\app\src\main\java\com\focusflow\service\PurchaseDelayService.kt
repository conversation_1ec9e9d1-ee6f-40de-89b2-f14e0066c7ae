package com.focusflow.service

import com.focusflow.data.repository.WishlistRepository
import com.focusflow.data.model.WishlistItem
import kotlinx.datetime.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PurchaseDelayService @Inject constructor(
    private val wishlistRepository: WishlistRepository,
    private val notificationService: NotificationService
) {
    
    companion object {
        const val DELAY_5_MINUTES = 5 * 60 * 1000L // 5 minutes in milliseconds
        const val DELAY_30_MINUTES = 30 * 60 * 1000L // 30 minutes
        const val DELAY_24_HOURS = 24 * 60 * 60 * 1000L // 24 hours
        const val DELAY_48_HOURS = 48 * 60 * 60 * 1000L // 48 hours
        const val DELAY_1_WEEK = 7 * 24 * 60 * 60 * 1000L // 1 week
    }
    
    suspend fun addItemToDelayList(
        itemName: String,
        estimatedPrice: Double,
        category: String,
        description: String? = null,
        merchant: String? = null,
        delayPeriodHours: Int = 24,
        priority: String = "medium"
    ): Long {
        
        val wishlistItemId = wishlistRepository.addItemToWishlist(
            itemName = itemName,
            estimatedPrice = estimatedPrice,
            category = category,
            description = description,
            merchant = merchant,
            delayPeriodHours = delayPeriodHours,
            priority = priority
        )
        
        // Schedule notification for when delay period ends
        scheduleDelayEndNotification(wishlistItemId, delayPeriodHours)
        
        return wishlistItemId
    }
    
    suspend fun getRecommendedDelayPeriod(amount: Double, category: String): DelayRecommendation {
        return when {
            amount < 25.0 -> DelayRecommendation(
                hours = 1,
                reason = "Small purchase - short reflection period",
                confidence = 0.8
            )
            amount < 100.0 -> DelayRecommendation(
                hours = 24,
                reason = "Medium purchase - 24-hour cooling off period recommended",
                confidence = 0.9
            )
            amount < 500.0 -> DelayRecommendation(
                hours = 48,
                reason = "Significant purchase - 48-hour delay recommended",
                confidence = 0.95
            )
            else -> DelayRecommendation(
                hours = 168, // 1 week
                reason = "Large purchase - week-long consideration period recommended",
                confidence = 0.98
            )
        }
    }
    
    suspend fun checkExpiredDelays(): List<WishlistItem> {
        val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
        return wishlistRepository.getItemsWithExpiredDelay(now)
    }
    
    suspend fun processExpiredDelays() {
        val expiredItems = checkExpiredDelays()
        
        expiredItems.forEach { item ->
            // Send notification that delay period has ended
            notificationService.sendDelayEndNotification(item)
            
            // Update item to mark delay as inactive
            wishlistRepository.updateWishlistItem(
                item.copy(isDelayActive = false)
            )
        }
    }
    
    suspend fun extendDelay(itemId: Long, additionalHours: Int): Boolean {
        return try {
            wishlistRepository.extendDelay(itemId, additionalHours)
            
            // Reschedule notification
            val item = wishlistRepository.getWishlistItemById(itemId)
            item?.let {
                scheduleDelayEndNotification(itemId, additionalHours, isExtension = true)
            }
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun removeFromDelayList(itemId: Long): Boolean {
        return try {
            wishlistRepository.removeDelay(itemId)
            
            // Cancel any scheduled notifications
            notificationService.cancelDelayNotification(itemId)
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun markAsPurchased(
        itemId: Long, 
        actualPrice: Double,
        reflectionNotes: String? = null
    ): Boolean {
        return try {
            val now = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault())
            wishlistRepository.markAsPurchased(itemId, now, actualPrice)
            
            // Update reflection if provided
            reflectionNotes?.let {
                wishlistRepository.updateReflection(itemId, true, it)
            }
            
            // Cancel any scheduled notifications
            notificationService.cancelDelayNotification(itemId)
            
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun addReflection(
        itemId: Long,
        stillWanted: Boolean,
        notes: String?
    ): Boolean {
        return try {
            wishlistRepository.updateReflection(itemId, stillWanted, notes)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    suspend fun getDelayStatistics(): DelayStatistics {
        val activeItems = wishlistRepository.getAllActiveWishlistItems()
        val purchasedItems = wishlistRepository.getPurchasedWishlistItems()
        
        // This would need to be implemented with proper Flow collection
        // For now, returning default values
        return DelayStatistics(
            totalItemsInDelay = 0,
            averageDelayPeriod = 24.0,
            itemsPurchasedAfterDelay = 0,
            itemsRemovedWithoutPurchase = 0,
            averagePriceOfDelayedItems = 0.0,
            mostCommonDelayCategory = "Shopping",
            delayEffectivenessRate = 0.0
        )
    }
    
    private suspend fun scheduleDelayEndNotification(
        itemId: Long, 
        delayHours: Int, 
        isExtension: Boolean = false
    ) {
        val item = wishlistRepository.getWishlistItemById(itemId) ?: return
        
        val notificationTime = if (isExtension) {
            Clock.System.now().plus(delayHours, DateTimeUnit.HOUR)
        } else {
            item.delayEndTime.toInstant(TimeZone.currentSystemDefault())
        }
        
        notificationService.scheduleDelayEndNotification(
            itemId = itemId,
            itemName = item.itemName,
            price = item.estimatedPrice,
            triggerTime = notificationTime.toEpochMilliseconds()
        )
    }
    
    fun getDelayPeriodOptions(): List<DelayPeriodOption> {
        return listOf(
            DelayPeriodOption(
                hours = 1,
                displayName = "1 Hour",
                description = "Quick reflection period",
                recommendedFor = "Small impulse purchases under $25"
            ),
            DelayPeriodOption(
                hours = 24,
                displayName = "24 Hours",
                description = "Standard cooling-off period",
                recommendedFor = "Most purchases between $25-$100"
            ),
            DelayPeriodOption(
                hours = 48,
                displayName = "48 Hours",
                description = "Extended consideration time",
                recommendedFor = "Significant purchases $100-$500"
            ),
            DelayPeriodOption(
                hours = 168,
                displayName = "1 Week",
                description = "Major purchase evaluation",
                recommendedFor = "Large purchases over $500"
            )
        )
    }
}

data class DelayRecommendation(
    val hours: Int,
    val reason: String,
    val confidence: Double
)

data class DelayStatistics(
    val totalItemsInDelay: Int,
    val averageDelayPeriod: Double,
    val itemsPurchasedAfterDelay: Int,
    val itemsRemovedWithoutPurchase: Int,
    val averagePriceOfDelayedItems: Double,
    val mostCommonDelayCategory: String,
    val delayEffectivenessRate: Double
)

data class DelayPeriodOption(
    val hours: Int,
    val displayName: String,
    val description: String,
    val recommendedFor: String
)
