package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00006\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\f\u001a\"\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a\u0018\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\tH\u0007\u001a\"\u0010\u000b\u001a\u00020\u00012\b\b\u0002\u0010\f\u001a\u00020\r2\u000e\b\u0002\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00010\u000fH\u0007\u001a\b\u0010\u0010\u001a\u00020\u0001H\u0007\u001a \u0010\u0011\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0014H\u0007\u001a\b\u0010\u0015\u001a\u00020\u0001H\u0007\u001a\b\u0010\u0016\u001a\u00020\u0001H\u0007\u001a \u0010\u0017\u001a\u00020\u00012\u0006\u0010\u0018\u001a\u00020\t2\u0006\u0010\u0019\u001a\u00020\u00032\u0006\u0010\u001a\u001a\u00020\u0006H\u0007\u001a\u0018\u0010\u001b\u001a\u00020\u00012\u0006\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u001d\u001a\u00020\u0006H\u0007\u001a\b\u0010\u001e\u001a\u00020\u0001H\u0007\u001a\b\u0010\u001f\u001a\u00020\u0001H\u0007\u00a8\u0006 "}, d2 = {"AchievementBadge", "", "emoji", "", "title", "isLocked", "", "CreditCardSummaryCard", "totalDebt", "", "nextPayment", "DashboardScreen", "viewModel", "Lcom/focusflow/ui/viewmodel/DashboardViewModel;", "onNavigateToSettings", "Lkotlin/Function0;", "HabitStreakCard", "HabitStreakItem", "habit", "streak", "", "MotivationalQuoteCard", "ProgressAchievementsCard", "SafeToSpendWidget", "safeToSpend", "period", "isLoading", "TaskItem", "task", "completed", "TodaysTasksCard", "VirtualPetWidget", "app_debug"})
public final class DashboardScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void DashboardScreen(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DashboardViewModel viewModel, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateToSettings) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void SafeToSpendWidget(double safeToSpend, @org.jetbrains.annotations.NotNull
    java.lang.String period, boolean isLoading) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void CreditCardSummaryCard(double totalDebt, double nextPayment) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TodaysTasksCard() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void TaskItem(@org.jetbrains.annotations.NotNull
    java.lang.String task, boolean completed) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void HabitStreakCard() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void HabitStreakItem(@org.jetbrains.annotations.NotNull
    java.lang.String emoji, @org.jetbrains.annotations.NotNull
    java.lang.String habit, int streak) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void MotivationalQuoteCard() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void VirtualPetWidget() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ProgressAchievementsCard() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void AchievementBadge(@org.jetbrains.annotations.NotNull
    java.lang.String emoji, @org.jetbrains.annotations.NotNull
    java.lang.String title, boolean isLocked) {
    }
}