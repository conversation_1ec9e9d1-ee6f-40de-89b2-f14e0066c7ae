package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b-\b\u0087\b\u0018\u00002\u00020\u0001B\u0087\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\t\u001a\u00020\u0005\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000b\u0012\b\b\u0002\u0010\u000e\u001a\u00020\u000f\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0015J\t\u0010,\u001a\u00020\u0003H\u00c6\u0003J\u0010\u0010-\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003\u00a2\u0006\u0002\u0010(J\u000b\u0010.\u001a\u0004\u0018\u00010\u0012H\u00c6\u0003J\t\u0010/\u001a\u00020\u0007H\u00c6\u0003J\t\u00100\u001a\u00020\u0005H\u00c6\u0003J\t\u00101\u001a\u00020\u0005H\u00c6\u0003J\t\u00102\u001a\u00020\u0007H\u00c6\u0003J\t\u00103\u001a\u00020\u0007H\u00c6\u0003J\t\u00104\u001a\u00020\u0005H\u00c6\u0003J\t\u00105\u001a\u00020\u000bH\u00c6\u0003J\u0010\u00106\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\u0010\u00107\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\t\u00108\u001a\u00020\u000fH\u00c6\u0003J\u0098\u0001\u00109\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00072\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000b2\b\b\u0002\u0010\u000e\u001a\u00020\u000f2\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u00072\b\b\u0002\u0010\u0014\u001a\u00020\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010:J\u0013\u0010;\u001a\u00020\u000f2\b\u0010<\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010=\u001a\u00020\u000bH\u00d6\u0001J\t\u0010>\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u0015\u0010\f\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001cR\u0015\u0010\r\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u001d\u0010\u0019R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001fR\u0011\u0010\u0014\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u001cR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b!\u0010\"R\u0011\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010#R\u0013\u0010\u0011\u001a\u0004\u0018\u00010\u0012\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010%R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b&\u0010\u001cR\u0015\u0010\u0010\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\n\n\u0002\u0010)\u001a\u0004\b\'\u0010(R\u0011\u0010\b\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u0017R\u0011\u0010\u0013\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u0017\u00a8\u0006?"}, d2 = {"Lcom/focusflow/data/model/BudgetCategory;", "", "id", "", "name", "", "allocatedAmount", "", "spentAmount", "budgetPeriod", "budgetYear", "", "budgetMonth", "budgetWeek", "isActive", "", "recommendedAmount", "lastRecommendationUpdate", "Lkotlinx/datetime/LocalDateTime;", "varianceThreshold", "categoryColor", "(JLjava/lang/String;DDLjava/lang/String;ILjava/lang/Integer;Ljava/lang/Integer;ZLjava/lang/Double;Lkotlinx/datetime/LocalDateTime;DLjava/lang/String;)V", "getAllocatedAmount", "()D", "getBudgetMonth", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getBudgetPeriod", "()Ljava/lang/String;", "getBudgetWeek", "getBudgetYear", "()I", "getCategoryColor", "getId", "()J", "()Z", "getLastRecommendationUpdate", "()Lkotlinx/datetime/LocalDateTime;", "getName", "getRecommendedAmount", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getSpentAmount", "getVarianceThreshold", "component1", "component10", "component11", "component12", "component13", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;DDLjava/lang/String;ILjava/lang/Integer;Ljava/lang/Integer;ZLjava/lang/Double;Lkotlinx/datetime/LocalDateTime;DLjava/lang/String;)Lcom/focusflow/data/model/BudgetCategory;", "equals", "other", "hashCode", "toString", "app_debug"})
@androidx.room.Entity(tableName = "budget_categories")
public final class BudgetCategory {
    @androidx.room.PrimaryKey(autoGenerate = true)
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String name = null;
    private final double allocatedAmount = 0.0;
    private final double spentAmount = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String budgetPeriod = null;
    private final int budgetYear = 0;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer budgetMonth = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Integer budgetWeek = null;
    private final boolean isActive = false;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double recommendedAmount = null;
    @org.jetbrains.annotations.Nullable
    private final kotlinx.datetime.LocalDateTime lastRecommendationUpdate = null;
    private final double varianceThreshold = 0.0;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String categoryColor = null;
    
    public BudgetCategory(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, double allocatedAmount, double spentAmount, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, int budgetYear, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetMonth, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetWeek, boolean isActive, @org.jetbrains.annotations.Nullable
    java.lang.Double recommendedAmount, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastRecommendationUpdate, double varianceThreshold, @org.jetbrains.annotations.NotNull
    java.lang.String categoryColor) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getName() {
        return null;
    }
    
    public final double getAllocatedAmount() {
        return 0.0;
    }
    
    public final double getSpentAmount() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getBudgetPeriod() {
        return null;
    }
    
    public final int getBudgetYear() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getBudgetMonth() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer getBudgetWeek() {
        return null;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getRecommendedAmount() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime getLastRecommendationUpdate() {
        return null;
    }
    
    public final double getVarianceThreshold() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getCategoryColor() {
        return null;
    }
    
    public final long component1() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component10() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final kotlinx.datetime.LocalDateTime component11() {
        return null;
    }
    
    public final double component12() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    public final double component3() {
        return 0.0;
    }
    
    public final double component4() {
        return 0.0;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component5() {
        return null;
    }
    
    public final int component6() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Integer component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.BudgetCategory copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String name, double allocatedAmount, double spentAmount, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, int budgetYear, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetMonth, @org.jetbrains.annotations.Nullable
    java.lang.Integer budgetWeek, boolean isActive, @org.jetbrains.annotations.Nullable
    java.lang.Double recommendedAmount, @org.jetbrains.annotations.Nullable
    kotlinx.datetime.LocalDateTime lastRecommendationUpdate, double varianceThreshold, @org.jetbrains.annotations.NotNull
    java.lang.String categoryColor) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}