package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.DashboardWidget
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface DashboardWidgetDao {
    
    @Query("SELECT * FROM dashboard_widgets WHERE isVisible = 1 AND isEnabled = 1 ORDER BY position ASC")
    fun getVisibleWidgets(): Flow<List<DashboardWidget>>
    
    @Query("SELECT * FROM dashboard_widgets ORDER BY position ASC")
    fun getAllWidgets(): Flow<List<DashboardWidget>>
    
    @Query("SELECT * FROM dashboard_widgets WHERE widgetType = :widgetType")
    suspend fun getWidgetByType(widgetType: String): DashboardWidget?
    
    @Query("SELECT * FROM dashboard_widgets WHERE id = :id")
    suspend fun getWidgetById(id: Long): DashboardWidget?
    
    @Query("SELECT COUNT(*) FROM dashboard_widgets WHERE isVisible = 1")
    suspend fun getVisibleWidgetCount(): Int
    
    @Insert
    suspend fun insertWidget(widget: DashboardWidget): Long
    
    @Update
    suspend fun updateWidget(widget: DashboardWidget)
    
    @Delete
    suspend fun deleteWidget(widget: DashboardWidget)
    
    @Query("UPDATE dashboard_widgets SET position = :position WHERE id = :id")
    suspend fun updateWidgetPosition(id: Long, position: Int)
    
    @Query("UPDATE dashboard_widgets SET isVisible = :isVisible WHERE id = :id")
    suspend fun updateWidgetVisibility(id: Long, isVisible: Boolean)
    
    @Query("UPDATE dashboard_widgets SET lastUpdated = :timestamp WHERE id = :id")
    suspend fun updateLastRefresh(id: Long, timestamp: LocalDateTime)
    
    @Query("UPDATE dashboard_widgets SET configuration = :config WHERE id = :id")
    suspend fun updateWidgetConfiguration(id: Long, config: String)
}
