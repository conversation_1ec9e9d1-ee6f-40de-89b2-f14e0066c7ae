package com.focusflow.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.focusflow.data.model.BudgetCategory
import com.focusflow.ui.viewmodel.BudgetViewModel
import com.focusflow.ui.viewmodel.DefaultBudgetCategories

@Composable
fun BudgetScreen(
    viewModel: BudgetViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    
    var showAddCategoryDialog by remember { mutableStateOf(false) }
    var showQuickSetupDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Budget",
                style = MaterialTheme.typography.h4,
                fontWeight = FontWeight.Bold
            )
            
            Row {
                if (uiState.budgetCategories.isEmpty()) {
                    TextButton(
                        onClick = { showQuickSetupDialog = true }
                    ) {
                        Icon(                            Icons.Default.Star,contentDescription = null)
                        Spacer(modifier = Modifier.width(4.dp))
                        Text("Quick Setup")
                    }
                }
                TextButton(
                    onClick = { showAddCategoryDialog = true }
                ) {
                    Icon(Icons.Default.Add, contentDescription = null)
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("Add")
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Budget overview
        BudgetOverviewCard(
            totalBudget = uiState.totalBudget,
            totalSpent = uiState.totalSpent,
            period = uiState.budgetPeriod
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Budget categories
        if (uiState.budgetCategories.isEmpty()) {
            EmptyBudgetState()
        } else {
            Text(
                text = "Budget Categories",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(uiState.budgetCategories) { category ->
                    BudgetCategoryItem(
                        budgetCategory = category,
                        onEdit = { /* TODO: Implement edit */ },
                        onDelete = { viewModel.deleteBudgetCategory(category) }
                    )
                }
            }
        }
    }

    // Add category dialog
    if (showAddCategoryDialog) {
        AddBudgetCategoryDialog(
            onDismiss = { showAddCategoryDialog = false },
            onAddCategory = { name, amount ->
                viewModel.addBudgetCategory(name, amount)
                showAddCategoryDialog = false
            }
        )
    }

    // Quick setup dialog
    if (showQuickSetupDialog) {
        QuickSetupDialog(
            onDismiss = { showQuickSetupDialog = false },
            onSetupComplete = { categories ->
                categories.forEach { (name, amount) ->
                    viewModel.addBudgetCategory(name, amount)
                }
                showQuickSetupDialog = false
            }
        )
    }

    // Error handling
    uiState.error?.let { error ->
        LaunchedEffect(error) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }
}

@Composable
fun BudgetOverviewCard(
    totalBudget: Double,
    totalSpent: Double,
    period: String
) {
    val remaining = totalBudget - totalSpent
    val spentPercentage = if (totalBudget > 0) (totalSpent / totalBudget).coerceIn(0.0, 1.0) else 0.0

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Budget Overview",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Progress bar
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Spent",
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Text(
                        text = "${String.format("%.1f", spentPercentage * 100)}%",
                        style = MaterialTheme.typography.caption,
                        color = when {
                            spentPercentage > 1.0 -> Color(0xFFF44336)
                            spentPercentage > 0.8 -> Color(0xFFFF9800)
                            else -> Color(0xFF4CAF50)
                        }
                    )
                }
                
                LinearProgressIndicator(
                    progress = spentPercentage.toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(12.dp),
                    color = when {
                        spentPercentage > 1.0 -> Color(0xFFF44336)
                        spentPercentage > 0.8 -> Color(0xFFFF9800)
                        else -> Color(0xFF4CAF50)
                    },
                    backgroundColor = MaterialTheme.colors.onSurface.copy(alpha = 0.1f)
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Budget amounts
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                BudgetAmountItem(
                    label = "Budget",
                    amount = totalBudget,
                    color = MaterialTheme.colors.primary
                )
                BudgetAmountItem(
                    label = "Spent",
                    amount = totalSpent,
                    color = Color(0xFFFF9800)
                )
                BudgetAmountItem(
                    label = "Remaining",
                    amount = remaining,
                    color = if (remaining >= 0) Color(0xFF4CAF50) else Color(0xFFF44336)
                )
            }
            
            Text(
                text = "This ${period.replaceFirstChar { it.lowercase() }}",
                style = MaterialTheme.typography.caption,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f),
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun BudgetAmountItem(
    label: String,
    amount: Double,
    color: Color
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.caption,
            color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
        )
        Text(
            text = "$${String.format("%.0f", amount)}",
            style = MaterialTheme.typography.h6,
            fontWeight = FontWeight.Bold,
            color = color
        )
    }
}

@Composable
fun BudgetCategoryItem(
    budgetCategory: BudgetCategory,
    onEdit: () -> Unit,
    onDelete: () -> Unit
) {
    val spentPercentage = if (budgetCategory.allocatedAmount > 0) {
        (budgetCategory.spentAmount / budgetCategory.allocatedAmount).coerceIn(0.0, 1.0)
    } else 0.0

    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = budgetCategory.name,
                    style = MaterialTheme.typography.h6,
                    fontWeight = FontWeight.Medium
                )
                
                Row {
                    IconButton(
                        onClick = onEdit,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            Icons.Default.Edit,
                            contentDescription = "Edit",
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    IconButton(
                        onClick = onDelete,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = MaterialTheme.colors.error,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Progress bar
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "$${String.format("%.2f", budgetCategory.spentAmount)} of $${String.format("%.2f", budgetCategory.allocatedAmount)}",
                        style = MaterialTheme.typography.body2
                    )
                    Text(
                        text = "${String.format("%.1f", spentPercentage * 100)}%",
                        style = MaterialTheme.typography.body2,
                        color = when {
                            spentPercentage > 1.0 -> Color(0xFFF44336)
                            spentPercentage > 0.8 -> Color(0xFFFF9800)
                            else -> Color(0xFF4CAF50)
                        }
                    )
                }
                
                LinearProgressIndicator(
                    progress = spentPercentage.toFloat(),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp),
                    color = when {
                        spentPercentage > 1.0 -> Color(0xFFF44336)
                        spentPercentage > 0.8 -> Color(0xFFFF9800)
                        else -> Color(0xFF4CAF50)
                    },
                    backgroundColor = MaterialTheme.colors.onSurface.copy(alpha = 0.1f)
                )
            }

            val remaining = budgetCategory.allocatedAmount - budgetCategory.spentAmount
            if (remaining < 0) {
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = "Over budget by $${String.format("%.2f", -remaining)}",
                    style = MaterialTheme.typography.caption,
                    color = Color(0xFFF44336)
                )
            }
        }
    }
}

@Composable
fun EmptyBudgetState() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(                            Icons.Default.ShoppingCart,
                contentDescription = null,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colors.onSurface.copy(alpha = 0.3f)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No Budget Set",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = "Create budget categories to track your spending and stay on target",
                style = MaterialTheme.typography.body2,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
            )
        }
    }
}

@Composable
fun AddBudgetCategoryDialog(
    onDismiss: () -> Unit,
    onAddCategory: (String, Double) -> Unit
) {
    var name by remember { mutableStateOf("") }
    var amount by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Add Budget Category",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                OutlinedTextField(
                    value = name,
                    onValueChange = { name = it },
                    label = { Text("Category Name") },
                    modifier = Modifier.fillMaxWidth()
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                OutlinedTextField(
                    value = amount,
                    onValueChange = { amount = it },
                    label = { Text("Budget Amount") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val amountDouble = amount.toDoubleOrNull()
                    if (name.isNotBlank() && amountDouble != null && amountDouble > 0) {
                        onAddCategory(name, amountDouble)
                    }
                },
                enabled = name.isNotBlank() && 
                         amount.toDoubleOrNull() != null && 
                         amount.toDoubleOrNull()!! > 0
            ) {
                Text("Add")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

@Composable
fun QuickSetupDialog(
    onDismiss: () -> Unit,
    onSetupComplete: (List<Pair<String, Double>>) -> Unit
) {
    var selectedCategories by remember { mutableStateOf(DefaultBudgetCategories.categories.toMap().toMutableMap()) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "Quick Budget Setup",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            LazyColumn {
                item {
                    Text(
                        text = "Adjust the suggested budget amounts:",
                        style = MaterialTheme.typography.body2,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
                
                items(selectedCategories.toList()) { (category, amount) ->
                    var categoryAmount by remember { mutableStateOf(amount.toString()) }
                    
                    OutlinedTextField(
                        value = categoryAmount,
                        onValueChange = { 
                            categoryAmount = it
                            it.toDoubleOrNull()?.let { newAmount ->
                                selectedCategories[category] = newAmount
                            }
                        },
                        label = { Text(category) },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onSetupComplete(selectedCategories.toList())
                }
            ) {
                Text("Create Budget")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}

// Duplicate functions removed - using comprehensive versions from OtherScreens.kt

