package com.focusflow.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000R\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\t\u001a&\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a`\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00030\f2\b\u0010\r\u001a\u0004\u0018\u00010\u00032\u0012\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u000f2\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u00122\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\nH\u0007\u001a?\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\n2\u0006\u0010\u0016\u001a\u00020\n2\u0011\u0010\u0017\u001a\r\u0012\u0004\u0012\u00020\u00010\u0007\u00a2\u0006\u0002\b\u00182\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0019\u001a\u00020\u0005H\u0007\u001a(\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u001c2\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0003\u001a>\u0010\u001d\u001a\u00020\u00012\f\u0010\u001e\u001a\b\u0012\u0004\u0012\u00020\u001c0\f2\u0012\u0010\u001f\u001a\u000e\u0012\u0004\u0012\u00020\u001c\u0012\u0004\u0012\u00020\u00010\u000f2\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010 \u001a\u00020!H\u0007\u001a\u0010\u0010\"\u001a\u00020\u00012\u0006\u0010#\u001a\u00020!H\u0003\u001a`\u0010$\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\n\b\u0002\u0010\u0013\u001a\u0004\u0018\u00010\n2\b\b\u0002\u0010%\u001a\u00020\n2\b\b\u0002\u0010&\u001a\u00020\n2\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\f\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00010\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010)\u001a\u00020\u0005H\u0007\u00a8\u0006*"}, d2 = {"DecisionOptionCard", "", "option", "Lcom/focusflow/ui/components/DecisionOption;", "isSelected", "", "onClick", "Lkotlin/Function0;", "MultipleChoiceDecision", "question", "", "options", "", "selectedOption", "onOptionSelected", "Lkotlin/Function1;", "onConfirm", "modifier", "Landroidx/compose/ui/Modifier;", "description", "ProgressiveDisclosureCard", "title", "summary", "detailedContent", "Landroidx/compose/runtime/Composable;", "initiallyExpanded", "QuickActionButton", "action", "Lcom/focusflow/ui/components/QuickAction;", "QuickActionGrid", "actions", "onActionClick", "columns", "", "RecommendationBadge", "level", "SimpleYesNoDecision", "yesText", "noText", "onYes", "onNo", "isLoading", "app_debug"})
public final class DecisionSupportComponentsKt {
    
    @androidx.compose.runtime.Composable
    public static final void SimpleYesNoDecision(@org.jetbrains.annotations.NotNull
    java.lang.String question, @org.jetbrains.annotations.Nullable
    java.lang.String description, @org.jetbrains.annotations.NotNull
    java.lang.String yesText, @org.jetbrains.annotations.NotNull
    java.lang.String noText, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onYes, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNo, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, boolean isLoading) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void MultipleChoiceDecision(@org.jetbrains.annotations.NotNull
    java.lang.String question, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.components.DecisionOption> options, @org.jetbrains.annotations.Nullable
    com.focusflow.ui.components.DecisionOption selectedOption, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.components.DecisionOption, kotlin.Unit> onOptionSelected, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onConfirm, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, @org.jetbrains.annotations.Nullable
    java.lang.String description) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void DecisionOptionCard(com.focusflow.ui.components.DecisionOption option, boolean isSelected, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void RecommendationBadge(int level) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void ProgressiveDisclosureCard(@org.jetbrains.annotations.NotNull
    java.lang.String title, @org.jetbrains.annotations.NotNull
    java.lang.String summary, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> detailedContent, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, boolean initiallyExpanded) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void QuickActionGrid(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.ui.components.QuickAction> actions, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.components.QuickAction, kotlin.Unit> onActionClick, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, int columns) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void QuickActionButton(com.focusflow.ui.components.QuickAction action, kotlin.jvm.functions.Function0<kotlin.Unit> onClick, androidx.compose.ui.Modifier modifier) {
    }
}