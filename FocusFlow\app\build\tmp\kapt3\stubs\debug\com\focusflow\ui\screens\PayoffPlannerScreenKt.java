package com.focusflow.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000T\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0007\u001a \u0010\u0002\u001a\u00020\u00012\f\u0010\u0003\u001a\b\u0012\u0004\u0012\u00020\u00010\u00042\b\b\u0002\u0010\u0005\u001a\u00020\u0006H\u0007\u001a.\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000b2\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u001a\u0010\u0010\u000f\u001a\u00020\u00012\u0006\u0010\u0010\u001a\u00020\u0011H\u0007\u001a\u0010\u0010\u0012\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\tH\u0007\u001a\u001e\u0010\u0013\u001a\u00020\u00012\f\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000e0\r2\u0006\u0010\u0005\u001a\u00020\u0006H\u0007\u001aN\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u00162\u0012\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0016\u0012\u0004\u0012\u00020\u00010\u00182\u0006\u0010\u0019\u001a\u00020\u001a2\u0012\u0010\u001b\u001a\u000e\u0012\u0004\u0012\u00020\u001a\u0012\u0004\u0012\u00020\u00010\u00182\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\u0004H\u0007\u001a*\u0010\u001d\u001a\u00020\u00012\u0006\u0010\u001e\u001a\u00020\u001a2\u0006\u0010\u001f\u001a\u00020\u001a2\u0006\u0010 \u001a\u00020!H\u0007\u00f8\u0001\u0000\u00a2\u0006\u0004\b\"\u0010#\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006$"}, d2 = {"EmptyStateMessage", "", "PayoffPlannerScreen", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/focusflow/ui/viewmodel/DebtViewModel;", "PayoffResultsSection", "payoffPlan", "Lcom/focusflow/ui/viewmodel/PayoffPlan;", "showComparison", "", "creditCards", "", "Lcom/focusflow/data/model/CreditCard;", "PayoffStepItem", "step", "Lcom/focusflow/ui/viewmodel/PayoffStep;", "PayoffSummaryCard", "StrategyComparisonCard", "StrategySelectionCard", "selectedStrategy", "Lcom/focusflow/ui/viewmodel/PayoffStrategy;", "onStrategySelected", "Lkotlin/Function1;", "extraPayment", "", "onExtraPaymentChanged", "onGeneratePlan", "SummaryItem", "label", "value", "color", "Landroidx/compose/ui/graphics/Color;", "SummaryItem-mxwnekA", "(Ljava/lang/String;Ljava/lang/String;J)V", "app_debug"})
public final class PayoffPlannerScreenKt {
    
    @androidx.compose.runtime.Composable
    public static final void PayoffPlannerScreen(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void EmptyStateMessage() {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategySelectionCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStrategy selectedStrategy, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.focusflow.ui.viewmodel.PayoffStrategy, kotlin.Unit> onStrategySelected, @org.jetbrains.annotations.NotNull
    java.lang.String extraPayment, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onExtraPaymentChanged, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function0<kotlin.Unit> onGeneratePlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffResultsSection(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan, boolean showComparison, @org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffSummaryCard(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffPlan payoffPlan) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void StrategyComparisonCard(@org.jetbrains.annotations.NotNull
    java.util.List<com.focusflow.data.model.CreditCard> creditCards, @org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.DebtViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable
    public static final void PayoffStepItem(@org.jetbrains.annotations.NotNull
    com.focusflow.ui.viewmodel.PayoffStep step) {
    }
}