package com.focusflow.ui.components

import androidx.compose.animation.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.focusflow.service.TimerState
import com.focusflow.service.TimerPreset
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun FocusTimerWidget(
    remainingTimeSeconds: Long,
    totalTimeSeconds: Long,
    timerState: TimerState,
    isBreakTime: Boolean,
    sessionName: String?,
    onStartPause: () -> Unit,
    onStop: () -> Unit,
    onSkipBreak: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val progress = if (totalTimeSeconds > 0) {
        (totalTimeSeconds - remainingTimeSeconds).toFloat() / totalTimeSeconds.toFloat()
    } else 0f
    
    val minutes = remainingTimeSeconds / 60
    val seconds = remainingTimeSeconds % 60
    
    val timerColor = when {
        isBreakTime -> Color(0xFF4CAF50) // Green for break
        timerState == TimerState.RUNNING -> Color(0xFF2196F3) // Blue for focus
        else -> Color(0xFF9E9E9E) // Gray for stopped/paused
    }
    
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 8.dp,
        shape = RoundedCornerShape(16.dp),
        backgroundColor = if (isBreakTime) Color(0xFFF1F8E9) else Color(0xFFE3F2FD)
    ) {
        Column(
            modifier = Modifier.padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Session name or break indicator
            Text(
                text = if (isBreakTime) "Break Time" else (sessionName ?: "Focus Session"),
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Bold,
                color = timerColor,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Circular timer
            Box(
                modifier = Modifier.size(200.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularTimer(
                    progress = progress,
                    color = timerColor,
                    modifier = Modifier.fillMaxSize()
                )
                
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = String.format("%02d:%02d", minutes, seconds),
                        style = MaterialTheme.typography.h4,
                        fontWeight = FontWeight.Bold,
                        color = timerColor
                    )
                    
                    Text(
                        text = when (timerState) {
                            TimerState.RUNNING -> if (isBreakTime) "Relax" else "Focus"
                            TimerState.PAUSED -> "Paused"
                            TimerState.STOPPED -> "Ready"
                        },
                        style = MaterialTheme.typography.caption,
                        color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // Control buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (isBreakTime) {
                    OutlinedButton(
                        onClick = onSkipBreak,
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = timerColor
                        )
                    ) {
                        Icon(Icons.Default.ArrowForward, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Skip Break")
                    }
                } else {
                    // Start/Pause button
                    Button(
                        onClick = onStartPause,
                        colors = ButtonDefaults.buttonColors(
                            backgroundColor = timerColor
                        ),
                        modifier = Modifier.size(64.dp),
                        shape = CircleShape
                    ) {
                        Icon(
                            imageVector = when (timerState) {
                                TimerState.RUNNING -> Icons.Default.Close // Using Close for pause
                                else -> Icons.Default.PlayArrow
                            },
                            contentDescription = when (timerState) {
                                TimerState.RUNNING -> "Pause"
                                else -> "Start"
                            },
                            modifier = Modifier.size(32.dp)
                        )
                    }
                    
                    // Stop button
                    if (timerState != TimerState.STOPPED) {
                        OutlinedButton(
                            onClick = onStop,
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFFF44336)
                            )
                        ) {
                            Icon(Icons.Default.Close, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Stop")
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun CircularTimer(
    progress: Float,
    color: Color,
    modifier: Modifier = Modifier
) {
    Canvas(modifier = modifier) {
        val strokeWidth = 12.dp.toPx()
        val radius = (size.minDimension - strokeWidth) / 2
        val center = androidx.compose.ui.geometry.Offset(size.width / 2, size.height / 2)
        
        // Background circle
        drawCircle(
            color = color.copy(alpha = 0.2f),
            radius = radius,
            center = center,
            style = Stroke(width = strokeWidth, cap = StrokeCap.Round)
        )
        
        // Progress arc
        if (progress > 0) {
            drawArc(
                color = color,
                startAngle = -90f,
                sweepAngle = 360f * progress,
                useCenter = false,
                style = Stroke(width = strokeWidth, cap = StrokeCap.Round),
                topLeft = androidx.compose.ui.geometry.Offset(
                    center.x - radius,
                    center.y - radius
                ),
                size = androidx.compose.ui.geometry.Size(radius * 2, radius * 2)
            )
        }
    }
}

@Composable
fun TimerPresetSelector(
    presets: List<TimerPreset>,
    selectedPreset: TimerPreset?,
    onPresetSelected: (TimerPreset) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Focus Session Presets",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(12.dp))
            
            presets.forEach { preset ->
                TimerPresetItem(
                    preset = preset,
                    isSelected = selectedPreset == preset,
                    onClick = { onPresetSelected(preset) }
                )
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

@Composable
private fun TimerPresetItem(
    preset: TimerPreset,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        elevation = if (isSelected) 4.dp else 1.dp,
        backgroundColor = if (isSelected) MaterialTheme.colors.primary.copy(alpha = 0.1f) else MaterialTheme.colors.surface,
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier.padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            RadioButton(
                selected = isSelected,
                onClick = onClick,
                colors = RadioButtonDefaults.colors(
                    selectedColor = MaterialTheme.colors.primary
                )
            )
            
            Spacer(modifier = Modifier.width(12.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = preset.name,
                    style = MaterialTheme.typography.subtitle1,
                    fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal
                )
                Text(
                    text = preset.description,
                    style = MaterialTheme.typography.caption,
                    color = MaterialTheme.colors.onSurface.copy(alpha = 0.7f)
                )
            }
            
            Text(
                text = "${preset.durationMinutes}min",
                style = MaterialTheme.typography.subtitle2,
                fontWeight = FontWeight.Medium,
                color = if (isSelected) MaterialTheme.colors.primary else MaterialTheme.colors.onSurface
            )
        }
    }
}

@Composable
fun FocusSessionSetup(
    taskTypes: List<String>,
    selectedTaskType: String,
    sessionName: String,
    sessionGoal: String,
    onTaskTypeSelected: (String) -> Unit,
    onSessionNameChanged: (String) -> Unit,
    onSessionGoalChanged: (String) -> Unit,
    onStartSession: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = 4.dp,
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Setup Focus Session",
                style = MaterialTheme.typography.h6,
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Task type selection
            Text(
                text = "Task Type",
                style = MaterialTheme.typography.subtitle2,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(8.dp))
            
            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(taskTypes) { taskType ->
                    // Using Button instead of FilterChip to avoid experimental API
                    Button(
                        onClick = { onTaskTypeSelected(taskType) },
                        colors = ButtonDefaults.buttonColors(
                            backgroundColor = if (selectedTaskType == taskType)
                                MaterialTheme.colors.primary else MaterialTheme.colors.surface
                        )
                    ) {
                        Text(taskType)
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Session name
            OutlinedTextField(
                value = sessionName,
                onValueChange = onSessionNameChanged,
                label = { Text("Session Name") },
                placeholder = { Text("e.g., Review monthly budget") },
                modifier = Modifier.fillMaxWidth()
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Session goal
            OutlinedTextField(
                value = sessionGoal,
                onValueChange = onSessionGoalChanged,
                label = { Text("Session Goal (Optional)") },
                placeholder = { Text("What do you want to accomplish?") },
                modifier = Modifier.fillMaxWidth(),
                maxLines = 2
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Button(
                onClick = onStartSession,
                modifier = Modifier.fillMaxWidth(),
                enabled = sessionName.isNotBlank()
            ) {
                Icon(Icons.Default.PlayArrow, contentDescription = null)
                Spacer(modifier = Modifier.width(8.dp))
                Text("Start Focus Session")
            }
        }
    }
}
