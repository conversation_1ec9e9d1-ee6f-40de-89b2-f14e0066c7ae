package com.focusflow.data.dao;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0007\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\n\n\u0002\u0010\t\n\u0002\b\u0002\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J:\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\f2\b\u0010\u000e\u001a\u0004\u0018\u00010\fH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0010\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0013J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00160\u0015H\'J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00160\u00152\u0006\u0010\b\u001a\u00020\tH\'J\u001c\u0010\u0018\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00160\u00152\u0006\u0010\u0019\u001a\u00020\tH\'J<\u0010\u001a\u001a\u0004\u0018\u00010\u00052\u0006\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\f2\b\u0010\u000e\u001a\u0004\u0018\u00010\fH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ3\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00160\u00152\u0006\u0010\n\u001a\u00020\t2\u0006\u0010\u000b\u001a\u00020\f2\b\u0010\r\u001a\u0004\u0018\u00010\fH\'\u00a2\u0006\u0002\u0010\u001cJ\u0018\u0010\u001d\u001a\u0004\u0018\u00010\u001e2\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u001fJ\u0014\u0010 \u001a\b\u0012\u0004\u0012\u00020!0\u0016H\u00a7@\u00a2\u0006\u0002\u0010\"J\u001c\u0010#\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00160\u00152\u0006\u0010$\u001a\u00020\u001eH\'J\u000e\u0010%\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\"J\u000e\u0010&\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\"J\u0014\u0010\'\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00050\u00160\u0015H\'J$\u0010(\u001a\b\u0012\u0004\u0012\u00020\u00050\u00162\u0006\u0010\b\u001a\u00020\t2\u0006\u0010)\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010*J\u0016\u0010+\u001a\u00020,2\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010-\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006\u00a8\u0006."}, d2 = {"Lcom/focusflow/data/dao/BudgetAnalyticsDao;", "", "deleteAnalytics", "", "analytics", "Lcom/focusflow/data/model/BudgetAnalytics;", "(Lcom/focusflow/data/model/BudgetAnalytics;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAnalyticsForPeriod", "categoryName", "", "period", "year", "", "month", "week", "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/Integer;Ljava/lang/Integer;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteAnalyticsOlderThan", "cutoffDate", "Lkotlinx/datetime/LocalDateTime;", "(Lkotlinx/datetime/LocalDateTime;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllAnalytics", "Lkotlinx/coroutines/flow/Flow;", "", "getAnalyticsByCategory", "getAnalyticsByTrend", "direction", "getAnalyticsForCategoryAndPeriod", "getAnalyticsForPeriod", "(Ljava/lang/String;ILjava/lang/Integer;)Lkotlinx/coroutines/flow/Flow;", "getAverageVarianceForCategory", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getCategoryVarianceAverages", "Lcom/focusflow/data/dao/CategoryVarianceAverage;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getHighVarianceAnalytics", "threshold", "getHighVarianceCount", "getIncreasingTrendCount", "getOutlierPeriods", "getRecentAnalyticsForCategory", "limit", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertAnalytics", "", "updateAnalytics", "app_debug"})
@androidx.room.Dao
public abstract interface BudgetAnalyticsDao {
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics ORDER BY calculatedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAllAnalytics();
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics WHERE categoryName = :categoryName ORDER BY calculatedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAnalyticsByCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName);
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics WHERE budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month ORDER BY categoryName")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAnalyticsForPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String period, int year, @org.jetbrains.annotations.Nullable
    java.lang.Integer month);
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics WHERE categoryName = :categoryName AND budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month AND budgetWeek = :week")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAnalyticsForCategoryAndPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    java.lang.String period, int year, @org.jetbrains.annotations.Nullable
    java.lang.Integer month, @org.jetbrains.annotations.Nullable
    java.lang.Integer week, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.data.model.BudgetAnalytics> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics WHERE variancePercentage > :threshold ORDER BY variancePercentage DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getHighVarianceAnalytics(double threshold);
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics WHERE trendDirection = :direction ORDER BY calculatedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getAnalyticsByTrend(@org.jetbrains.annotations.NotNull
    java.lang.String direction);
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics WHERE isOutlierPeriod = 1 ORDER BY calculatedDate DESC")
    @org.jetbrains.annotations.NotNull
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.focusflow.data.model.BudgetAnalytics>> getOutlierPeriods();
    
    @androidx.room.Query(value = "SELECT categoryName, AVG(variancePercentage) as avgVariance FROM budget_analytics GROUP BY categoryName ORDER BY avgVariance DESC")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getCategoryVarianceAverages(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.dao.CategoryVarianceAverage>> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(variancePercentage) FROM budget_analytics WHERE categoryName = :categoryName")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getAverageVarianceForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM budget_analytics WHERE categoryName = :categoryName ORDER BY calculatedDate DESC LIMIT :limit")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getRecentAnalyticsForCategory(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, int limit, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.util.List<com.focusflow.data.model.BudgetAnalytics>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM budget_analytics WHERE variancePercentage > 20")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getHighVarianceCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM budget_analytics WHERE trendDirection = \'increasing\'")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getIncreasingTrendCount(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertAnalytics(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetAnalytics analytics, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateAnalytics(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetAnalytics analytics, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteAnalytics(@org.jetbrains.annotations.NotNull
    com.focusflow.data.model.BudgetAnalytics analytics, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM budget_analytics WHERE calculatedDate < :cutoffDate")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteAnalyticsOlderThan(@org.jetbrains.annotations.NotNull
    kotlinx.datetime.LocalDateTime cutoffDate, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM budget_analytics WHERE categoryName = :categoryName AND budgetPeriod = :period AND budgetYear = :year AND budgetMonth = :month AND budgetWeek = :week")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object deleteAnalyticsForPeriod(@org.jetbrains.annotations.NotNull
    java.lang.String categoryName, @org.jetbrains.annotations.NotNull
    java.lang.String period, int year, @org.jetbrains.annotations.Nullable
    java.lang.Integer month, @org.jetbrains.annotations.Nullable
    java.lang.Integer week, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}