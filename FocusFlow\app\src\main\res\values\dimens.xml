<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- ADHD-friendly text sizes -->
    <dimen name="text_size_headline">24sp</dimen>
    <dimen name="text_size_title">20sp</dimen>
    <dimen name="text_size_body">16sp</dimen>
    <dimen name="text_size_caption">14sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    
    <!-- Large text sizes for accessibility -->
    <dimen name="text_size_headline_large">28sp</dimen>
    <dimen name="text_size_title_large">24sp</dimen>
    <dimen name="text_size_body_large">18sp</dimen>
    <dimen name="text_size_caption_large">16sp</dimen>
    
    <!-- Extra large text sizes for high accessibility needs -->
    <dimen name="text_size_headline_xl">32sp</dimen>
    <dimen name="text_size_title_xl">28sp</dimen>
    <dimen name="text_size_body_xl">22sp</dimen>
    <dimen name="text_size_caption_xl">20sp</dimen>
    
    <!-- Spacing for ADHD-friendly layouts -->
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="spacing_sm">8dp</dimen>
    <dimen name="spacing_md">16dp</dimen>
    <dimen name="spacing_lg">24dp</dimen>
    <dimen name="spacing_xl">32dp</dimen>
    
    <!-- Touch target sizes for accessibility -->
    <dimen name="touch_target_min">48dp</dimen>
    <dimen name="touch_target_comfortable">56dp</dimen>
    <dimen name="touch_target_large">64dp</dimen>
    
    <!-- Card and component dimensions -->
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="button_corner_radius">8dp</dimen>
    
    <!-- Focus indicators -->
    <dimen name="focus_border_width">3dp</dimen>
    <dimen name="focus_corner_radius">8dp</dimen>
</resources>
