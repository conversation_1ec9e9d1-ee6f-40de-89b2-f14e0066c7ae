package com.focusflow.data.dao

import androidx.room.*
import com.focusflow.data.model.VoiceCommand
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDateTime

@Dao
interface VoiceCommandDao {
    
    @Query("SELECT * FROM voice_commands ORDER BY timestamp DESC")
    fun getAllVoiceCommands(): Flow<List<VoiceCommand>>
    
    @Query("SELECT * FROM voice_commands WHERE commandType = :commandType ORDER BY timestamp DESC")
    fun getVoiceCommandsByType(commandType: String): Flow<List<VoiceCommand>>
    
    @Query("SELECT * FROM voice_commands WHERE isSuccessful = 1 ORDER BY timestamp DESC")
    fun getSuccessfulCommands(): Flow<List<VoiceCommand>>
    
    @Query("SELECT * FROM voice_commands WHERE timestamp >= :startDate ORDER BY timestamp DESC")
    fun getRecentCommands(startDate: LocalDateTime): Flow<List<VoiceCommand>>
    
    @Query("SELECT * FROM voice_commands WHERE id = :id")
    suspend fun getVoiceCommandById(id: Long): VoiceCommand?
    
    @Query("SELECT COUNT(*) FROM voice_commands WHERE isSuccessful = 1")
    suspend fun getSuccessfulCommandCount(): Int
    
    @Query("SELECT COUNT(*) FROM voice_commands")
    suspend fun getTotalCommandCount(): Int
    
    @Query("SELECT AVG(confidence) FROM voice_commands WHERE isSuccessful = 1")
    suspend fun getAverageSuccessConfidence(): Double?
    
    @Query("SELECT AVG(processingTime) FROM voice_commands")
    suspend fun getAverageProcessingTime(): Double?
    
    @Query("SELECT commandType, COUNT(*) as count FROM voice_commands WHERE isSuccessful = 1 GROUP BY commandType ORDER BY count DESC")
    suspend fun getCommandTypeUsage(): List<CommandTypeUsage>
    
    @Insert
    suspend fun insertVoiceCommand(voiceCommand: VoiceCommand): Long
    
    @Update
    suspend fun updateVoiceCommand(voiceCommand: VoiceCommand)
    
    @Delete
    suspend fun deleteVoiceCommand(voiceCommand: VoiceCommand)
    
    @Query("UPDATE voice_commands SET userFeedback = :feedback WHERE id = :id")
    suspend fun updateUserFeedback(id: Long, feedback: String)
    
    @Query("UPDATE voice_commands SET correctedCommand = :correctedCommand WHERE id = :id")
    suspend fun updateCorrectedCommand(id: Long, correctedCommand: String)
    
    @Query("DELETE FROM voice_commands WHERE timestamp < :cutoffDate")
    suspend fun deleteOldCommands(cutoffDate: LocalDateTime)
}

data class CommandTypeUsage(
    val commandType: String,
    val count: Int
)
