package com.focusflow.data.model;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00006\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u0006\n\u0002\b\u0007\n\u0002\u0010\u0007\n\u0002\b3\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u00bf\u0001\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u0012\b\b\u0002\u0010\t\u001a\u00020\u0007\u0012\b\b\u0002\u0010\n\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\r\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0010\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0011\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0013\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0015\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0017\u001a\u00020\u0007\u0012\b\b\u0002\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\u0019J\t\u00102\u001a\u00020\u0003H\u00c6\u0003J\t\u00103\u001a\u00020\u0007H\u00c6\u0003J\t\u00104\u001a\u00020\u0007H\u00c6\u0003J\t\u00105\u001a\u00020\u0005H\u00c6\u0003J\t\u00106\u001a\u00020\u0005H\u00c6\u0003J\t\u00107\u001a\u00020\u0005H\u00c6\u0003J\t\u00108\u001a\u00020\u0015H\u00c6\u0003J\t\u00109\u001a\u00020\u0007H\u00c6\u0003J\t\u0010:\u001a\u00020\u0007H\u00c6\u0003J\t\u0010;\u001a\u00020\u0007H\u00c6\u0003J\t\u0010<\u001a\u00020\u0005H\u00c6\u0003J\t\u0010=\u001a\u00020\u0007H\u00c6\u0003J\t\u0010>\u001a\u00020\u0005H\u00c6\u0003J\t\u0010?\u001a\u00020\u0007H\u00c6\u0003J\t\u0010@\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010A\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010B\u001a\u0004\u0018\u00010\rH\u00c6\u0003\u00a2\u0006\u0002\u0010(J\u0010\u0010C\u001a\u0004\u0018\u00010\rH\u00c6\u0003\u00a2\u0006\u0002\u0010(J\u00c8\u0001\u0010D\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\u00052\b\b\u0002\u0010\t\u001a\u00020\u00072\b\b\u0002\u0010\n\u001a\u00020\u00052\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\r2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\r2\b\b\u0002\u0010\u000f\u001a\u00020\u00072\b\b\u0002\u0010\u0010\u001a\u00020\u00072\b\b\u0002\u0010\u0011\u001a\u00020\u00052\b\b\u0002\u0010\u0012\u001a\u00020\u00052\b\b\u0002\u0010\u0013\u001a\u00020\u00052\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u00072\b\b\u0002\u0010\u0017\u001a\u00020\u00072\b\b\u0002\u0010\u0018\u001a\u00020\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010EJ\u0013\u0010F\u001a\u00020\u00072\b\u0010G\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010H\u001a\u00020IH\u00d6\u0001J\t\u0010J\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0018\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u001bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u001dR\u0011\u0010\t\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u001bR\u0011\u0010\u0010\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010\u001bR\u0011\u0010\u0014\u001a\u00020\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010!R\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u001dR\u0011\u0010\u000f\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b#\u0010\u001bR\u0011\u0010\u0016\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b$\u0010\u001bR\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010&R\u0015\u0010\u000e\u001a\u0004\u0018\u00010\r\u00a2\u0006\n\n\u0002\u0010)\u001a\u0004\b\'\u0010(R\u0011\u0010\u0011\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010\u001dR\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010\u001bR\u0013\u0010\u000b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b,\u0010\u001dR\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010\u001dR\u0011\u0010\u0012\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b.\u0010\u001dR\u0011\u0010\u0013\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b/\u0010\u001dR\u0011\u0010\u0017\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010\u001bR\u0015\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\n\n\u0002\u0010)\u001a\u0004\b1\u0010(\u00a8\u0006K"}, d2 = {"Lcom/focusflow/data/model/UserPreferences;", "", "id", "", "budgetPeriod", "", "notificationsEnabled", "", "reminderTime", "darkModeEnabled", "fontSize", "primaryGoal", "weeklyBudget", "", "monthlyBudget", "hasCompletedOnboarding", "enableNotifications", "notificationTime", "theme", "themePreference", "fontScale", "", "highContrastMode", "voiceInputEnabled", "animationsEnabled", "(JLjava/lang/String;ZLjava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;ZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;FZZZ)V", "getAnimationsEnabled", "()Z", "getBudgetPeriod", "()Ljava/lang/String;", "getDarkModeEnabled", "getEnableNotifications", "getFontScale", "()F", "getFontSize", "getHasCompletedOnboarding", "getHighContrastMode", "getId", "()J", "getMonthlyBudget", "()Ljava/lang/Double;", "Ljava/lang/Double;", "getNotificationTime", "getNotificationsEnabled", "getPrimaryGoal", "getReminderTime", "getTheme", "getThemePreference", "getVoiceInputEnabled", "getWeeklyBudget", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(JLjava/lang/String;ZLjava/lang/String;ZLjava/lang/String;Ljava/lang/String;Ljava/lang/Double;Ljava/lang/Double;ZZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;FZZZ)Lcom/focusflow/data/model/UserPreferences;", "equals", "other", "hashCode", "", "toString", "app_debug"})
@androidx.room.Entity(tableName = "user_preferences")
public final class UserPreferences {
    @androidx.room.PrimaryKey
    private final long id = 0L;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String budgetPeriod = null;
    private final boolean notificationsEnabled = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String reminderTime = null;
    private final boolean darkModeEnabled = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String fontSize = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.String primaryGoal = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double weeklyBudget = null;
    @org.jetbrains.annotations.Nullable
    private final java.lang.Double monthlyBudget = null;
    private final boolean hasCompletedOnboarding = false;
    private final boolean enableNotifications = false;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String notificationTime = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String theme = null;
    @org.jetbrains.annotations.NotNull
    private final java.lang.String themePreference = null;
    private final float fontScale = 0.0F;
    private final boolean highContrastMode = false;
    private final boolean voiceInputEnabled = false;
    private final boolean animationsEnabled = false;
    
    public UserPreferences(long id, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, boolean notificationsEnabled, @org.jetbrains.annotations.NotNull
    java.lang.String reminderTime, boolean darkModeEnabled, @org.jetbrains.annotations.NotNull
    java.lang.String fontSize, @org.jetbrains.annotations.Nullable
    java.lang.String primaryGoal, @org.jetbrains.annotations.Nullable
    java.lang.Double weeklyBudget, @org.jetbrains.annotations.Nullable
    java.lang.Double monthlyBudget, boolean hasCompletedOnboarding, boolean enableNotifications, @org.jetbrains.annotations.NotNull
    java.lang.String notificationTime, @org.jetbrains.annotations.NotNull
    java.lang.String theme, @org.jetbrains.annotations.NotNull
    java.lang.String themePreference, float fontScale, boolean highContrastMode, boolean voiceInputEnabled, boolean animationsEnabled) {
        super();
    }
    
    public final long getId() {
        return 0L;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getBudgetPeriod() {
        return null;
    }
    
    public final boolean getNotificationsEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getReminderTime() {
        return null;
    }
    
    public final boolean getDarkModeEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getFontSize() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String getPrimaryGoal() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getWeeklyBudget() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double getMonthlyBudget() {
        return null;
    }
    
    public final boolean getHasCompletedOnboarding() {
        return false;
    }
    
    public final boolean getEnableNotifications() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getNotificationTime() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getTheme() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String getThemePreference() {
        return null;
    }
    
    public final float getFontScale() {
        return 0.0F;
    }
    
    public final boolean getHighContrastMode() {
        return false;
    }
    
    public final boolean getVoiceInputEnabled() {
        return false;
    }
    
    public final boolean getAnimationsEnabled() {
        return false;
    }
    
    public UserPreferences() {
        super();
    }
    
    public final long component1() {
        return 0L;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final boolean component11() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component13() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component14() {
        return null;
    }
    
    public final float component15() {
        return 0.0F;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final boolean component17() {
        return false;
    }
    
    public final boolean component18() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component2() {
        return null;
    }
    
    public final boolean component3() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component4() {
        return null;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Double component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final com.focusflow.data.model.UserPreferences copy(long id, @org.jetbrains.annotations.NotNull
    java.lang.String budgetPeriod, boolean notificationsEnabled, @org.jetbrains.annotations.NotNull
    java.lang.String reminderTime, boolean darkModeEnabled, @org.jetbrains.annotations.NotNull
    java.lang.String fontSize, @org.jetbrains.annotations.Nullable
    java.lang.String primaryGoal, @org.jetbrains.annotations.Nullable
    java.lang.Double weeklyBudget, @org.jetbrains.annotations.Nullable
    java.lang.Double monthlyBudget, boolean hasCompletedOnboarding, boolean enableNotifications, @org.jetbrains.annotations.NotNull
    java.lang.String notificationTime, @org.jetbrains.annotations.NotNull
    java.lang.String theme, @org.jetbrains.annotations.NotNull
    java.lang.String themePreference, float fontScale, boolean highContrastMode, boolean voiceInputEnabled, boolean animationsEnabled) {
        return null;
    }
    
    @java.lang.Override
    public boolean equals(@org.jetbrains.annotations.Nullable
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override
    @org.jetbrains.annotations.NotNull
    public java.lang.String toString() {
        return null;
    }
}