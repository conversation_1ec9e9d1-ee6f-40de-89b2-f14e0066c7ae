package com.focusflow.service;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000x\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0015\b\u0007\u0018\u0000 ?2\u00020\u0001:\u0001?B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ2\u0010\u001f\u001a\u00020 2\n\b\u0002\u0010!\u001a\u0004\u0018\u00010\"2\n\b\u0002\u0010#\u001a\u0004\u0018\u00010\"2\n\b\u0002\u0010$\u001a\u0004\u0018\u00010%H\u0086@\u00a2\u0006\u0002\u0010&J\f\u0010\'\u001a\b\u0012\u0004\u0012\u00020)0(J\u000e\u0010*\u001a\u00020+H\u0086@\u00a2\u0006\u0002\u0010,J\u000e\u0010-\u001a\u00020 H\u0082@\u00a2\u0006\u0002\u0010,J\u000e\u0010.\u001a\u00020 H\u0086@\u00a2\u0006\u0002\u0010,J\u000e\u0010/\u001a\u00020 H\u0086@\u00a2\u0006\u0002\u0010,J\u000e\u00100\u001a\u00020 H\u0086@\u00a2\u0006\u0002\u0010,J\u0018\u00101\u001a\u00020 2\b\b\u0002\u00102\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u00103JJ\u00104\u001a\u00020\u000f2\u0006\u00105\u001a\u00020%2\u0006\u00106\u001a\u00020%2\b\b\u0002\u00107\u001a\u00020\"2\b\b\u0002\u00108\u001a\u00020%2\n\b\u0002\u00109\u001a\u0004\u0018\u00010%2\n\b\u0002\u0010:\u001a\u0004\u0018\u00010%H\u0086@\u00a2\u0006\u0002\u0010;J\u0010\u0010<\u001a\u00020 2\u0006\u0010=\u001a\u00020\u000fH\u0002J\b\u0010>\u001a\u00020 H\u0002R\u0016\u0010\t\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\f\u001a\b\u0012\u0004\u0012\u00020\r0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u000f0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0010\u001a\b\u0012\u0004\u0012\u00020\u00110\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u0012\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0015R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\r0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0015R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000f0\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0015R\u0010\u0010\u0019\u001a\u0004\u0018\u00010\u001aX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u001b\u001a\u0004\u0018\u00010\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00110\u0013\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0015\u00a8\u0006@"}, d2 = {"Lcom/focusflow/service/FocusTimerService;", "", "focusSessionDao", "Lcom/focusflow/data/dao/FocusSessionDao;", "notificationService", "Lcom/focusflow/service/NotificationService;", "gamificationService", "Lcom/focusflow/service/GamificationService;", "(Lcom/focusflow/data/dao/FocusSessionDao;Lcom/focusflow/service/NotificationService;Lcom/focusflow/service/GamificationService;)V", "_currentSession", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/focusflow/data/model/FocusSession;", "_isBreakTime", "", "_remainingTime", "", "_timerState", "Lcom/focusflow/service/TimerState;", "currentSession", "Lkotlinx/coroutines/flow/StateFlow;", "getCurrentSession", "()Lkotlinx/coroutines/flow/StateFlow;", "isBreakTime", "remainingTime", "getRemainingTime", "sessionStartTime", "Lkotlinx/datetime/LocalDateTime;", "timerJob", "Lkotlinx/coroutines/Job;", "timerState", "getTimerState", "endCurrentSession", "", "focusQuality", "", "productivityScore", "notes", "", "(Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPresetTimerOptions", "", "Lcom/focusflow/service/TimerPreset;", "getSessionStatistics", "Lcom/focusflow/service/FocusSessionStatistics;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "onTimerComplete", "pauseSession", "resumeSession", "skipBreak", "startBreak", "isLongBreak", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startFocusSession", "taskType", "sessionName", "plannedDurationMinutes", "sessionType", "sessionGoal", "backgroundSound", "(Ljava/lang/String;Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "startTimer", "durationSeconds", "stopTimer", "Companion", "app_debug"})
public final class FocusTimerService {
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.data.dao.FocusSessionDao focusSessionDao = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.NotificationService notificationService = null;
    @org.jetbrains.annotations.NotNull
    private final com.focusflow.service.GamificationService gamificationService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.data.model.FocusSession> _currentSession = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.data.model.FocusSession> currentSession = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.focusflow.service.TimerState> _timerState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.focusflow.service.TimerState> timerState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Long> _remainingTime = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Long> remainingTime = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isBreakTime = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isBreakTime = null;
    @org.jetbrains.annotations.Nullable
    private kotlinx.coroutines.Job timerJob;
    @org.jetbrains.annotations.Nullable
    private kotlinx.datetime.LocalDateTime sessionStartTime;
    public static final int DEFAULT_WORK_DURATION = 1500;
    public static final int DEFAULT_SHORT_BREAK = 300;
    public static final int DEFAULT_LONG_BREAK = 900;
    public static final int POMODOROS_BEFORE_LONG_BREAK = 4;
    @org.jetbrains.annotations.NotNull
    public static final com.focusflow.service.FocusTimerService.Companion Companion = null;
    
    @javax.inject.Inject
    public FocusTimerService(@org.jetbrains.annotations.NotNull
    com.focusflow.data.dao.FocusSessionDao focusSessionDao, @org.jetbrains.annotations.NotNull
    com.focusflow.service.NotificationService notificationService, @org.jetbrains.annotations.NotNull
    com.focusflow.service.GamificationService gamificationService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.data.model.FocusSession> getCurrentSession() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.focusflow.service.TimerState> getTimerState() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Long> getRemainingTime() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isBreakTime() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object startFocusSession(@org.jetbrains.annotations.NotNull
    java.lang.String taskType, @org.jetbrains.annotations.NotNull
    java.lang.String sessionName, int plannedDurationMinutes, @org.jetbrains.annotations.NotNull
    java.lang.String sessionType, @org.jetbrains.annotations.Nullable
    java.lang.String sessionGoal, @org.jetbrains.annotations.Nullable
    java.lang.String backgroundSound, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object pauseSession(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object resumeSession(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object endCurrentSession(@org.jetbrains.annotations.Nullable
    java.lang.Integer focusQuality, @org.jetbrains.annotations.Nullable
    java.lang.Integer productivityScore, @org.jetbrains.annotations.Nullable
    java.lang.String notes, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object startBreak(boolean isLongBreak, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object skipBreak(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void startTimer(long durationSeconds) {
    }
    
    private final java.lang.Object onTimerComplete(kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    private final void stopTimer() {
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getSessionStatistics(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.focusflow.service.FocusSessionStatistics> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final java.util.List<com.focusflow.service.TimerPreset> getPresetTimerOptions() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/focusflow/service/FocusTimerService$Companion;", "", "()V", "DEFAULT_LONG_BREAK", "", "DEFAULT_SHORT_BREAK", "DEFAULT_WORK_DURATION", "POMODOROS_BEFORE_LONG_BREAK", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}